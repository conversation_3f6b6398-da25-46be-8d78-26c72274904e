import discord
import asyncio
import argparse
import os
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from html import escape

parser = argparse.ArgumentParser(description="Coles Algo MLB Projection Sheet Scraper → HTML renderer")
parser.add_argument("--channel-id", type=str, default="1358105984224198818")
parser.add_argument("--output", type=str, default="mlb_projection_sheet_messages.html")
parser.add_argument("--token", type=str, default=os.getenv("DISCORD_TOKEN"))
args = parser.parse_args()

DEFAULT_TOKEN = "MTM5NzI4ODg3NzM5OTYwNTI1OQ.GbjChY.WQAyO6uGlmctv_m5g8YH8c7KXoI1ZCRs07J6pU"
BOT_TOKEN = args.token if args.token else DEFAULT_TOKEN
TARGET_CHANNEL_ID = int(args.channel_id)
OUTPUT_FILE = args.output

intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
client = discord.Client(intents=intents)

def iso(dt: Optional[datetime]) -> Optional[str]:
    if not dt:
        return None
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc).isoformat()

def render_html(messages: List[Dict[str, Any]]) -> str:
    css = """
    body { font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif; background: #2b2d31; color: #e3e5e8; margin: 0; padding: 24px; }
    .container { max-width: 1000px; margin: 0 auto; }
    .header { margin-bottom: 18px; }
    .msg { display: grid; grid-template-columns: 48px 1fr; gap: 12px; padding: 10px 12px; border-radius: 8px; }
    .msg:hover { background: #313338; }
    .avatar { width: 40px; height: 40px; border-radius: 50%; background: #5865f2; display: inline-block; }
    .meta { display: flex; align-items: baseline; gap: 8px; }
    .author { font-weight: 600; color: #f2f3f5; }
    .timestamp { color: #949ba4; font-size: 12px; }
    .content { white-space: pre-wrap; line-height: 1.4; }
    .attachments { margin-top: 8px; display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 8px; }
    .attachment { background: #1e1f22; border: 1px solid #3f4147; border-radius: 6px; padding: 8px; }
    .attachment img { max-width: 100%; height: auto; border-radius: 4px; display: block; }
    .kv { margin-top: 8px; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 12px; color: #cfd3da; background: #1e1f22; border: 1px solid #3f4147; border-radius: 6px; padding: 8px; overflow: auto; }
    a { color: #00aaff; text-decoration: none; }
    a:hover { text-decoration: underline; }
    """.strip()
    head = f"""
    <!doctype html>
    <html lang=\"en\">
    <head>
      <meta charset=\"utf-8\" />
      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />
      <title>MLB Projection Sheet Messages</title>
      <style>{css}</style>
    </head>
    <body>
    <div class=\"container\">"""
    parts: List[str] = [head, "<div class=\"header\"><h2>MLB Projection Sheet Channel Export</h2></div>"]
    for m in messages:
        author_display = escape(m.get("author_display", ""))
        ts = escape(m.get("timestamp", ""))
        content = escape(m.get("content", ""))
        avatar_url = m.get("author_avatar_url") or ""
        avatar_html = f"<img class=\"avatar\" src=\"{escape(avatar_url)}\" alt=\"\" />" if avatar_url else "<span class=\"avatar\"></span>"
        parts.append("<div class=\"msg\">")
        parts.append(f"<div>{avatar_html}</div>")
        parts.append("<div>")
        parts.append(f"<div class=\"meta\"><span class=\"author\">{author_display}</span><span class=\"timestamp\">{ts}</span></div>")
        if content:
            parts.append(f"<div class=\"content\">{content}</div>")
        atts = m.get("attachments", [])
        if atts:
            parts.append("<div class=\"attachments\">")
            for a in atts:
                url = a.get("url") or ""
                filename = escape(a.get("filename") or "")
                ctype = a.get("content_type") or ""
                parts.append("<div class=\"attachment\">")
                if isinstance(ctype, str) and ctype.startswith("image/"):
                    parts.append(f"<a href=\"{escape(url)}\" target=\"_blank\" rel=\"noreferrer noopener\"><img src=\"{escape(url)}\" alt=\"{filename}\" /></a>")
                else:
                    parts.append(f"<a href=\"{escape(url)}\" target=\"_blank\" rel=\"noreferrer noopener\">{filename or escape(url)}</a>")
                parts.append("</div>")
            parts.append("</div>")
        meta_items: List[str] = []
        for k in [
            "message_id","channel","author_id","author_username","author_discriminator",
            "edited_timestamp","pinned","type","reply_to_message_id","num_reactions","reactions"
        ]:
            v = m.get(k)
            if isinstance(v, str):
                meta_items.append(f"{escape(k)}: {escape(v)}")
            else:
                meta_items.append(f"{escape(k)}: {escape(str(v))}")
        parts.append(f"<div class=\"kv\">{'<br/>'.join(meta_items)}</div>")
        parts.append("</div>")
        parts.append("</div>")
    parts.append("</div></body></html>")
    return "".join(parts)

async def fetch_full_channel_history(channel_id: int) -> List[Dict[str, Any]]:
    channel = client.get_channel(channel_id)
    if channel is None:
        return []
    results: List[Dict[str, Any]] = []
    async for message in channel.history(limit=None, oldest_first=True):
        if getattr(message.author, "bot", False):
            pass
        attachments_meta = []
        for attachment in message.attachments:
            attachments_meta.append({
                "url": attachment.url,
                "filename": attachment.filename,
                "content_type": getattr(attachment, "content_type", None),
                "size": getattr(attachment, "size", None)
            })
        reactions_meta = []
        for reaction in message.reactions:
            reactions_meta.append({
                "emoji": str(reaction.emoji),
                "count": reaction.count
            })
        reference_msg_id: Optional[str] = None
        if message.reference and message.reference.message_id:
            reference_msg_id = str(message.reference.message_id)
        author_discriminator = getattr(message.author, "discriminator", None)
        author_username = getattr(message.author, "name", None) or getattr(message.author, "display_name", None) or str(message.author)
        author_display = f"{author_username}#{author_discriminator}" if author_discriminator not in (None, "0") else str(author_username)
        avatar_url: Optional[str] = None
        try:
            avatar_url = message.author.display_avatar.url  # type: ignore[attr-defined]
        except Exception:
            avatar_url = None
        results.append({
            "message_id": str(message.id),
            "channel": getattr(message.channel, "name", str(channel_id)),
            "content": message.content or "",
            "timestamp": iso(message.created_at) or "",
            "edited_timestamp": iso(message.edited_at),
            "author_id": str(message.author.id),
            "author_username": str(author_username),
            "author_discriminator": str(author_discriminator) if author_discriminator is not None else None,
            "author_display": author_display,
            "author_avatar_url": avatar_url,
            "attachments": attachments_meta,
            "reactions": reactions_meta,
            "num_reactions": sum(r.get("count", 0) for r in reactions_meta),
            "mentions_count": len(message.mentions) if hasattr(message, "mentions") else 0,
            "reply_to_message_id": reference_msg_id,
            "pinned": bool(getattr(message, "pinned", False)),
            "type": str(getattr(message, "type", ""))
        })
    return results

@client.event
async def on_ready():
    print(f"Logged in as {client.user}")
    data = await fetch_full_channel_history(TARGET_CHANNEL_ID)
    html = render_html(data)
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        f.write(html)
    print(f"Wrote {len(data)} messages to {OUTPUT_FILE}")
    await client.close()

if __name__ == "__main__":
    client.run(BOT_TOKEN)
