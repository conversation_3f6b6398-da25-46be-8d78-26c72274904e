{
    "name": "Scrape MLB Projection Sheets",
    "flow": [
        {
            "id": 2,
            "module": "google-sheets:getSheetContent",
            "version": 2,
            "parameters": {
                "__IMTCONN__": 3893777
            },
            "mapper": {
                "from": "share",
                "range": "A1:N271",
                "select": "list",
                "sheetId": "Hitter Projections",
                "spreadsheetId": "/1ScPIoWRKwcbnJPwZupHT55OGI4pBI_MYO1f2tnsDxcU",
                "tableFirstRow": "A1:N1",
                "includesHeaders": true,
                "dateTimeRenderOption": "FORMATTED_STRING"
            },
            "metadata": {
                "designer": {
                    "x": 0,
                    "y": 0
                },
                "restore": {
                    "expect": {
                        "from": {
                            "label": "Shared with me"
                        },
                        "select": {
                            "label": "Search by path"
                        },
                        "sheetId": {
                            "label": "Hitter Projections"
                        },
                        "spreadsheetId": {
                            "path": [
                                "All Data From MLB Projections"
                            ]
                        },
                        "includesHeaders": {
                            "mode": "chose"
                        },
                        "valueRenderOption": {
                            "mode": "chose",
                            "empty": true,
                            "label": "Empty"
                        },
                        "dateTimeRenderOption": {
                            "mode": "chose",
                            "label": "Formatted string"
                        }
                    },
                    "parameters": {
                        "__IMTCONN__": {
                            "data": {
                                "scoped": "true",
                                "connection": "google"
                            },
                            "label": "My Google connection (<EMAIL>)"
                        }
                    }
                },
                "parameters": [
                    {
                        "name": "__IMTCONN__",
                        "type": "account:google",
                        "label": "Connection",
                        "required": true
                    }
                ],
                "expect": [
                    {
                        "name": "select",
                        "type": "select",
                        "label": "Search Method",
                        "required": true,
                        "validate": {
                            "enum": [
                                "list",
                                "fromAll",
                                "map"
                            ]
                        }
                    },
                    {
                        "name": "range",
                        "type": "text",
                        "label": "Range",
                        "required": true
                    },
                    {
                        "name": "includesHeaders",
                        "type": "boolean",
                        "label": "Table contains headers",
                        "required": true
                    },
                    {
                        "name": "valueRenderOption",
                        "type": "select",
                        "label": "Value render option",
                        "validate": {
                            "enum": [
                                "FORMATTED_VALUE",
                                "UNFORMATTED_VALUE",
                                "FORMULA"
                            ]
                        }
                    },
                    {
                        "name": "dateTimeRenderOption",
                        "type": "select",
                        "label": "Date and time render option",
                        "validate": {
                            "enum": [
                                "SERIAL_NUMBER",
                                "FORMATTED_STRING"
                            ]
                        }
                    },
                    {
                        "name": "from",
                        "type": "select",
                        "label": "Drive",
                        "required": true,
                        "validate": {
                            "enum": [
                                "drive",
                                "share",
                                "team"
                            ]
                        }
                    },
                    {
                        "name": "spreadsheetId",
                        "type": "file",
                        "label": "Spreadsheet ID",
                        "required": true
                    },
                    {
                        "name": "sheetId",
                        "type": "select",
                        "label": "Sheet Name",
                        "required": true
                    },
                    {
                        "name": "tableFirstRow",
                        "type": "text",
                        "label": "Row with headers"
                    }
                ],
                "interface": [
                    {
                        "name": "__IMTLENGTH__",
                        "type": "uinteger",
                        "label": "Total number of bundles"
                    },
                    {
                        "name": "__IMTINDEX__",
                        "type": "uinteger",
                        "label": "Bundle order position"
                    },
                    {
                        "name": "__ROW_NUMBER__",
                        "type": "number",
                        "label": "Row number"
                    },
                    {
                        "name": "__SPREADSHEET_ID__",
                        "type": "text",
                        "label": "Spreadsheet ID"
                    },
                    {
                        "name": "__SHEET__",
                        "type": "text",
                        "label": "Sheet"
                    },
                    {
                        "name": "0",
                        "type": "text",
                        "label": "Hitter Name (A)"
                    },
                    {
                        "name": "1",
                        "type": "text",
                        "label": "xAB (B)"
                    },
                    {
                        "name": "2",
                        "type": "text",
                        "label": "xHits (C)"
                    },
                    {
                        "name": "3",
                        "type": "text",
                        "label": "xSingles (D)"
                    },
                    {
                        "name": "4",
                        "type": "text",
                        "label": "xDoubles (E)"
                    },
                    {
                        "name": "5",
                        "type": "text",
                        "label": "xTriples (F)"
                    },
                    {
                        "name": "6",
                        "type": "text",
                        "label": "xHomeruns (G)"
                    },
                    {
                        "name": "7",
                        "type": "text",
                        "label": "xTB (H)"
                    },
                    {
                        "name": "8",
                        "type": "text",
                        "label": "xWalks (I)"
                    },
                    {
                        "name": "9",
                        "type": "text",
                        "label": "xSB (J)"
                    },
                    {
                        "name": "10",
                        "type": "text",
                        "label": "xRuns (K)"
                    },
                    {
                        "name": "11",
                        "type": "text",
                        "label": "xRBIs (L)"
                    },
                    {
                        "name": "12",
                        "type": "text",
                        "label": "xHRR (M)"
                    },
                    {
                        "name": "13",
                        "type": "text",
                        "label": "xOBP (N)"
                    }
                ],
                "advanced": true
            }
        },
        {
            "id": 9,
            "module": "builtin:BasicAggregator",
            "version": 1,
            "parameters": {
                "feeder": 2
            },
            "mapper": {
                "0": "{{2.`0`}}",
                "1": "{{2.`1`}}",
                "2": "{{2.`2`}}",
                "3": "{{2.`3`}}",
                "4": "{{2.`4`}}",
                "5": "{{2.`5`}}",
                "6": "{{2.`6`}}",
                "7": "{{2.`7`}}",
                "8": "{{2.`8`}}",
                "9": "{{2.`9`}}",
                "10": "{{2.`10`}}",
                "11": "{{2.`11`}}",
                "12": "{{2.`12`}}",
                "13": "{{2.`13`}}"
            },
            "metadata": {
                "designer": {
                    "x": 300,
                    "y": 0
                },
                "restore": {
                    "extra": {
                        "feeder": {
                            "label": "Google Sheets - Get Range Values [2]"
                        },
                        "target": {
                            "label": "Custom"
                        }
                    }
                }
            }
        },
        {
            "id": 4,
            "module": "google-sheets:getSheetContent",
            "version": 2,
            "parameters": {
                "__IMTCONN__": 3893777
            },
            "mapper": {
                "from": "share",
                "range": "A1:H31",
                "select": "list",
                "sheetId": "Pitcher Projections",
                "spreadsheetId": "/1ScPIoWRKwcbnJPwZupHT55OGI4pBI_MYO1f2tnsDxcU",
                "tableFirstRow": "A1:H1",
                "includesHeaders": true,
                "valueRenderOption": "FORMATTED_VALUE",
                "dateTimeRenderOption": "FORMATTED_STRING"
            },
            "metadata": {
                "designer": {
                    "x": 600,
                    "y": 0
                },
                "restore": {
                    "expect": {
                        "from": {
                            "label": "Shared with me"
                        },
                        "select": {
                            "label": "Search by path"
                        },
                        "sheetId": {
                            "label": "Pitcher Projections"
                        },
                        "spreadsheetId": {
                            "path": [
                                "All Data From MLB Projections"
                            ]
                        },
                        "includesHeaders": {
                            "mode": "chose"
                        },
                        "valueRenderOption": {
                            "mode": "chose",
                            "label": "Formatted value"
                        },
                        "dateTimeRenderOption": {
                            "mode": "chose",
                            "label": "Formatted string"
                        }
                    },
                    "parameters": {
                        "__IMTCONN__": {
                            "data": {
                                "scoped": "true",
                                "connection": "google"
                            },
                            "label": "My Google connection (<EMAIL>)"
                        }
                    }
                },
                "parameters": [
                    {
                        "name": "__IMTCONN__",
                        "type": "account:google",
                        "label": "Connection",
                        "required": true
                    }
                ],
                "expect": [
                    {
                        "name": "select",
                        "type": "select",
                        "label": "Search Method",
                        "required": true,
                        "validate": {
                            "enum": [
                                "list",
                                "fromAll",
                                "map"
                            ]
                        }
                    },
                    {
                        "name": "range",
                        "type": "text",
                        "label": "Range",
                        "required": true
                    },
                    {
                        "name": "includesHeaders",
                        "type": "boolean",
                        "label": "Table contains headers",
                        "required": true
                    },
                    {
                        "name": "valueRenderOption",
                        "type": "select",
                        "label": "Value render option",
                        "validate": {
                            "enum": [
                                "FORMATTED_VALUE",
                                "UNFORMATTED_VALUE",
                                "FORMULA"
                            ]
                        }
                    },
                    {
                        "name": "dateTimeRenderOption",
                        "type": "select",
                        "label": "Date and time render option",
                        "validate": {
                            "enum": [
                                "SERIAL_NUMBER",
                                "FORMATTED_STRING"
                            ]
                        }
                    },
                    {
                        "name": "from",
                        "type": "select",
                        "label": "Drive",
                        "required": true,
                        "validate": {
                            "enum": [
                                "drive",
                                "share",
                                "team"
                            ]
                        }
                    },
                    {
                        "name": "spreadsheetId",
                        "type": "file",
                        "label": "Spreadsheet ID",
                        "required": true
                    },
                    {
                        "name": "sheetId",
                        "type": "select",
                        "label": "Sheet Name",
                        "required": true
                    },
                    {
                        "name": "tableFirstRow",
                        "type": "text",
                        "label": "Row with headers"
                    }
                ],
                "interface": [
                    {
                        "name": "__IMTLENGTH__",
                        "type": "uinteger",
                        "label": "Total number of bundles"
                    },
                    {
                        "name": "__IMTINDEX__",
                        "type": "uinteger",
                        "label": "Bundle order position"
                    },
                    {
                        "name": "__ROW_NUMBER__",
                        "type": "number",
                        "label": "Row number"
                    },
                    {
                        "name": "__SPREADSHEET_ID__",
                        "type": "text",
                        "label": "Spreadsheet ID"
                    },
                    {
                        "name": "__SHEET__",
                        "type": "text",
                        "label": "Sheet"
                    },
                    {
                        "name": "0",
                        "type": "text",
                        "label": "Pitcher Name (A)"
                    },
                    {
                        "name": "1",
                        "type": "text",
                        "label": "xPitches (B)"
                    },
                    {
                        "name": "2",
                        "type": "text",
                        "label": "xOuts (C)"
                    },
                    {
                        "name": "3",
                        "type": "text",
                        "label": "xBF (D)"
                    },
                    {
                        "name": "4",
                        "type": "text",
                        "label": "xStrikeouts (E)"
                    },
                    {
                        "name": "5",
                        "type": "text",
                        "label": "xWalks (F)"
                    },
                    {
                        "name": "6",
                        "type": "text",
                        "label": "xER (G)"
                    },
                    {
                        "name": "7",
                        "type": "text",
                        "label": "xHA (H)"
                    }
                ],
                "advanced": true
            }
        },
        {
            "id": 10,
            "module": "builtin:BasicAggregator",
            "version": 1,
            "parameters": {
                "feeder": 4
            },
            "mapper": {
                "0": "{{4.`0`}}",
                "1": "{{4.`1`}}",
                "2": "{{4.`2`}}",
                "3": "{{4.`3`}}",
                "4": "{{4.`4`}}",
                "5": "{{4.`5`}}",
                "6": "{{4.`6`}}",
                "7": "{{4.`7`}}"
            },
            "metadata": {
                "designer": {
                    "x": 900,
                    "y": 0
                },
                "restore": {
                    "extra": {
                        "feeder": {
                            "label": "Google Sheets - Get Range Values [4]"
                        },
                        "target": {
                            "label": "Custom"
                        }
                    }
                }
            }
        },
        {
            "id": 5,
            "module": "google-sheets:getSheetContent",
            "version": 2,
            "parameters": {
                "__IMTCONN__": 3893777
            },
            "mapper": {
                "from": "share",
                "range": "A1:Q16",
                "select": "list",
                "sheetId": "Game Summary",
                "spreadsheetId": "/1ScPIoWRKwcbnJPwZupHT55OGI4pBI_MYO1f2tnsDxcU",
                "tableFirstRow": "A1:Q1",
                "includesHeaders": true,
                "valueRenderOption": "FORMATTED_VALUE",
                "dateTimeRenderOption": "FORMATTED_STRING"
            },
            "metadata": {
                "designer": {
                    "x": 1200,
                    "y": 0
                },
                "restore": {
                    "expect": {
                        "from": {
                            "label": "Shared with me"
                        },
                        "select": {
                            "label": "Search by path"
                        },
                        "sheetId": {
                            "label": "Game Summary"
                        },
                        "spreadsheetId": {
                            "path": [
                                "All Data From MLB Projections"
                            ]
                        },
                        "includesHeaders": {
                            "mode": "chose"
                        },
                        "valueRenderOption": {
                            "mode": "chose",
                            "label": "Formatted value"
                        },
                        "dateTimeRenderOption": {
                            "mode": "chose",
                            "label": "Formatted string"
                        }
                    },
                    "parameters": {
                        "__IMTCONN__": {
                            "data": {
                                "scoped": "true",
                                "connection": "google"
                            },
                            "label": "My Google connection (<EMAIL>)"
                        }
                    }
                },
                "parameters": [
                    {
                        "name": "__IMTCONN__",
                        "type": "account:google",
                        "label": "Connection",
                        "required": true
                    }
                ],
                "expect": [
                    {
                        "name": "select",
                        "type": "select",
                        "label": "Search Method",
                        "required": true,
                        "validate": {
                            "enum": [
                                "list",
                                "fromAll",
                                "map"
                            ]
                        }
                    },
                    {
                        "name": "range",
                        "type": "text",
                        "label": "Range",
                        "required": true
                    },
                    {
                        "name": "includesHeaders",
                        "type": "boolean",
                        "label": "Table contains headers",
                        "required": true
                    },
                    {
                        "name": "valueRenderOption",
                        "type": "select",
                        "label": "Value render option",
                        "validate": {
                            "enum": [
                                "FORMATTED_VALUE",
                                "UNFORMATTED_VALUE",
                                "FORMULA"
                            ]
                        }
                    },
                    {
                        "name": "dateTimeRenderOption",
                        "type": "select",
                        "label": "Date and time render option",
                        "validate": {
                            "enum": [
                                "SERIAL_NUMBER",
                                "FORMATTED_STRING"
                            ]
                        }
                    },
                    {
                        "name": "from",
                        "type": "select",
                        "label": "Drive",
                        "required": true,
                        "validate": {
                            "enum": [
                                "drive",
                                "share",
                                "team"
                            ]
                        }
                    },
                    {
                        "name": "spreadsheetId",
                        "type": "file",
                        "label": "Spreadsheet ID",
                        "required": true
                    },
                    {
                        "name": "sheetId",
                        "type": "select",
                        "label": "Sheet Name",
                        "required": true
                    },
                    {
                        "name": "tableFirstRow",
                        "type": "text",
                        "label": "Row with headers"
                    }
                ],
                "interface": [
                    {
                        "name": "__IMTLENGTH__",
                        "type": "uinteger",
                        "label": "Total number of bundles"
                    },
                    {
                        "name": "__IMTINDEX__",
                        "type": "uinteger",
                        "label": "Bundle order position"
                    },
                    {
                        "name": "__ROW_NUMBER__",
                        "type": "number",
                        "label": "Row number"
                    },
                    {
                        "name": "__SPREADSHEET_ID__",
                        "type": "text",
                        "label": "Spreadsheet ID"
                    },
                    {
                        "name": "__SHEET__",
                        "type": "text",
                        "label": "Sheet"
                    },
                    {
                        "name": "0",
                        "type": "text",
                        "label": "Game Number (A)"
                    },
                    {
                        "name": "1",
                        "type": "text",
                        "label": "Away Team (B)"
                    },
                    {
                        "name": "2",
                        "type": "text",
                        "label": "Home Team (C)"
                    },
                    {
                        "name": "3",
                        "type": "text",
                        "label": "Away SP (D)"
                    },
                    {
                        "name": "4",
                        "type": "text",
                        "label": "Home SP (E)"
                    },
                    {
                        "name": "5",
                        "type": "text",
                        "label": "Away Score (F)"
                    },
                    {
                        "name": "6",
                        "type": "text",
                        "label": "Home Score (G)"
                    },
                    {
                        "name": "7",
                        "type": "text",
                        "label": "Total Runs (H)"
                    },
                    {
                        "name": "8",
                        "type": "text",
                        "label": "Winning Team (I)"
                    },
                    {
                        "name": "9",
                        "type": "text",
                        "label": "xHRs (J)"
                    },
                    {
                        "name": "10",
                        "type": "text",
                        "label": "Home Win % (K)"
                    },
                    {
                        "name": "11",
                        "type": "text",
                        "label": "xAway ML (L)"
                    },
                    {
                        "name": "12",
                        "type": "text",
                        "label": "xHome ML (M)"
                    },
                    {
                        "name": "13",
                        "type": "text",
                        "label": "OU (N)"
                    },
                    {
                        "name": "14",
                        "type": "text",
                        "label": "Delta (O)"
                    },
                    {
                        "name": "15",
                        "type": "text",
                        "label": "Absolute Delta (P)"
                    },
                    {
                        "name": "16",
                        "type": "text",
                        "label": "Absolute Run Diff Delta (Q)"
                    }
                ],
                "advanced": true
            }
        },
        {
            "id": 11,
            "module": "builtin:BasicAggregator",
            "version": 1,
            "parameters": {
                "feeder": 5
            },
            "mapper": {
                "0": "{{5.`0`}}",
                "1": "{{5.`1`}}",
                "2": "{{5.`2`}}",
                "3": "{{5.`3`}}",
                "4": "{{5.`4`}}",
                "5": "{{5.`5`}}",
                "6": "{{5.`6`}}",
                "7": "{{5.`7`}}",
                "8": "{{5.`8`}}",
                "9": "{{5.`9`}}",
                "10": "{{5.`10`}}",
                "11": "{{5.`11`}}",
                "12": "{{5.`12`}}",
                "13": "{{5.`13`}}",
                "14": "{{5.`14`}}",
                "15": "{{5.`15`}}",
                "16": "{{5.`16`}}"
            },
            "metadata": {
                "designer": {
                    "x": 1500,
                    "y": 0
                },
                "restore": {
                    "extra": {
                        "feeder": {
                            "label": "Google Sheets - Get Range Values [5]"
                        },
                        "target": {
                            "label": "Custom"
                        }
                    }
                }
            }
        },
        {
            "id": 6,
            "module": "google-sheets:getSheetContent",
            "version": 2,
            "parameters": {
                "__IMTCONN__": 3893777
            },
            "mapper": {
                "from": "share",
                "range": "A1:G16",
                "select": "list",
                "sheetId": "RFI",
                "spreadsheetId": "/1ScPIoWRKwcbnJPwZupHT55OGI4pBI_MYO1f2tnsDxcU",
                "tableFirstRow": "A1:G1",
                "includesHeaders": true,
                "valueRenderOption": "FORMATTED_VALUE",
                "dateTimeRenderOption": "FORMATTED_STRING"
            },
            "metadata": {
                "designer": {
                    "x": 1800,
                    "y": 0
                },
                "restore": {
                    "expect": {
                        "from": {
                            "label": "Shared with me"
                        },
                        "select": {
                            "label": "Search by path"
                        },
                        "sheetId": {
                            "label": "RFI"
                        },
                        "spreadsheetId": {
                            "path": [
                                "All Data From MLB Projections"
                            ]
                        },
                        "includesHeaders": {
                            "mode": "chose"
                        },
                        "valueRenderOption": {
                            "mode": "chose",
                            "label": "Formatted value"
                        },
                        "dateTimeRenderOption": {
                            "mode": "chose",
                            "label": "Formatted string"
                        }
                    },
                    "parameters": {
                        "__IMTCONN__": {
                            "data": {
                                "scoped": "true",
                                "connection": "google"
                            },
                            "label": "My Google connection (<EMAIL>)"
                        }
                    }
                },
                "parameters": [
                    {
                        "name": "__IMTCONN__",
                        "type": "account:google",
                        "label": "Connection",
                        "required": true
                    }
                ],
                "expect": [
                    {
                        "name": "select",
                        "type": "select",
                        "label": "Search Method",
                        "required": true,
                        "validate": {
                            "enum": [
                                "list",
                                "fromAll",
                                "map"
                            ]
                        }
                    },
                    {
                        "name": "range",
                        "type": "text",
                        "label": "Range",
                        "required": true
                    },
                    {
                        "name": "includesHeaders",
                        "type": "boolean",
                        "label": "Table contains headers",
                        "required": true
                    },
                    {
                        "name": "valueRenderOption",
                        "type": "select",
                        "label": "Value render option",
                        "validate": {
                            "enum": [
                                "FORMATTED_VALUE",
                                "UNFORMATTED_VALUE",
                                "FORMULA"
                            ]
                        }
                    },
                    {
                        "name": "dateTimeRenderOption",
                        "type": "select",
                        "label": "Date and time render option",
                        "validate": {
                            "enum": [
                                "SERIAL_NUMBER",
                                "FORMATTED_STRING"
                            ]
                        }
                    },
                    {
                        "name": "from",
                        "type": "select",
                        "label": "Drive",
                        "required": true,
                        "validate": {
                            "enum": [
                                "drive",
                                "share",
                                "team"
                            ]
                        }
                    },
                    {
                        "name": "spreadsheetId",
                        "type": "file",
                        "label": "Spreadsheet ID",
                        "required": true
                    },
                    {
                        "name": "sheetId",
                        "type": "select",
                        "label": "Sheet Name",
                        "required": true
                    },
                    {
                        "name": "tableFirstRow",
                        "type": "text",
                        "label": "Row with headers"
                    }
                ],
                "interface": [
                    {
                        "name": "__IMTLENGTH__",
                        "type": "uinteger",
                        "label": "Total number of bundles"
                    },
                    {
                        "name": "__IMTINDEX__",
                        "type": "uinteger",
                        "label": "Bundle order position"
                    },
                    {
                        "name": "__ROW_NUMBER__",
                        "type": "number",
                        "label": "Row number"
                    },
                    {
                        "name": "__SPREADSHEET_ID__",
                        "type": "text",
                        "label": "Spreadsheet ID"
                    },
                    {
                        "name": "__SHEET__",
                        "type": "text",
                        "label": "Sheet"
                    },
                    {
                        "name": "0",
                        "type": "text",
                        "label": "Game Number (A)"
                    },
                    {
                        "name": "1",
                        "type": "text",
                        "label": "Away Team (B)"
                    },
                    {
                        "name": "2",
                        "type": "text",
                        "label": "Home Team (C)"
                    },
                    {
                        "name": "3",
                        "type": "text",
                        "label": "xRFI (D)"
                    },
                    {
                        "name": "4",
                        "type": "text",
                        "label": "Chance of RFI (E)"
                    },
                    {
                        "name": "5",
                        "type": "text",
                        "label": "(F)"
                    },
                    {
                        "name": "6",
                        "type": "text",
                        "label": "Difference from 0.9 (G)"
                    }
                ],
                "advanced": true
            }
        },
        {
            "id": 12,
            "module": "builtin:BasicAggregator",
            "version": 1,
            "parameters": {
                "feeder": 6
            },
            "mapper": {
                "0": "{{6.`0`}}",
                "1": "{{6.`1`}}",
                "2": "{{6.`2`}}",
                "3": "{{6.`3`}}",
                "4": "{{6.`4`}}",
                "5": "{{6.`5`}}",
                "6": "{{6.`6`}}"
            },
            "metadata": {
                "designer": {
                    "x": 2100,
                    "y": 0
                },
                "restore": {
                    "extra": {
                        "feeder": {
                            "label": "Google Sheets - Get Range Values [6]"
                        },
                        "target": {
                            "label": "Custom"
                        }
                    }
                }
            }
        },
        {
            "id": 21,
            "module": "json:TransformToJSON",
            "version": 1,
            "parameters": {
                "space": ""
            },
            "mapper": {
                "object": "{\"hitter_projections\": {{9.array}}, \"pitcher_projections\": {{10.array}}, \"game_summary\": {{11.array}}, \"rfi\": {{12.array}} }"
            },
            "metadata": {
                "designer": {
                    "x": 3232,
                    "y": -1
                },
                "restore": {
                    "parameters": {
                        "space": {
                            "label": "Empty"
                        }
                    }
                },
                "parameters": [
                    {
                        "name": "space",
                        "type": "select",
                        "label": "Indentation",
                        "validate": {
                            "enum": [
                                "tab",
                                "2",
                                "4"
                            ]
                        }
                    }
                ],
                "expect": [
                    {
                        "name": "object",
                        "type": "any",
                        "label": "Object"
                    }
                ]
            }
        },
        {
            "id": 19,
            "module": "builtin:BasicRouter",
            "version": 1,
            "mapper": null,
            "metadata": {
                "designer": {
                    "x": 3822,
                    "y": 10
                }
            },
            "routes": [
                {
                    "flow": [
                        {
                            "id": 7,
                            "module": "open-router:makeAnApiCall",
                            "version": 1,
                            "parameters": {
                                "__IMTCONN__": 4422719
                            },
                            "mapper": {
                                "url": "/v1/chat/completions",
                                "body": "{\"model\":\"openai/gpt-5-nano\",\"stream\":false,\"temperature\":0,\"top_p\":1,\"response_format\":{\"type\":\"json_object\"},\"messages\":[{\"role\":\"system\",\"content\":\"You are an accurate parser for complete MLB projection sheets. Return ONLY a single valid JSON object matching this EXACT schema: {\\\"projections\\\":[{\\\"game_date\\\":\\\"YYYY-MM-DD\\\",\\\"away_team\\\":\\\"TeamName\\\",\\\"home_team\\\":\\\"TeamName\\\",\\\"away_sp\\\":\\\"Player Name\\\",\\\"home_sp\\\":\\\"Player Name\\\",\\\"away_score\\\":4.25,\\\"home_score\\\":3.75,\\\"total_runs_scored\\\":8.00,\\\"home_team_run_diff\\\":-0.50,\\\"winning_team\\\":\\\"TeamName\\\",\\\"game_xHRs\\\":1.25,\\\"home_win_pct\\\":45.5,\\\"xRFI\\\":0.95,\\\"player_name\\\":\\\"Player Name\\\",\\\"is_pitcher\\\":1,\\\"AB\\\":4.0,\\\"Hits\\\":1.2,\\\"Singles\\\":0.8,\\\"Doubles\\\":0.3,\\\"Triples\\\":0.1,\\\"Homeruns\\\":0.2,\\\"TB\\\":1.8,\\\"Walks\\\":0.4,\\\"SB\\\":0.1,\\\"Runs\\\":0.6,\\\"RBIs\\\":0.5,\\\"HRR\\\":2.1,\\\"OBP\\\":0.35,\\\"HitterStrikeouts\\\":0.9,\\\"Pitches\\\":85.0,\\\"Outs\\\":15.0,\\\"BF\\\":22.5,\\\"Strikeouts\\\":4.2,\\\"PitcherWalks\\\":2.1,\\\"ER\\\":2.8,\\\"HA\\\":5.3,\\\"xRFI_Chance\\\":null}]} . Use null for missing numeric values, use YYYY-MM-DD date, team nicknames only, is_pitcher is a number (1/0), stats are numbers, include EVERY player with ALL available stats, and output must be valid JSON (no markdown).\"},{\"role\":\"user\",\"content\":{{toJSON(21.json)}}}]}""method": "POST",
                                "headers": [
                                    {
                                        "key": "Content-Type",
                                        "value": "application/json"
                                    },
                                    {
                                        "key": "HTTP-Referer",
                                        "value": "https://projectparlay.com"
                                    },
                                    {
                                        "key": "X-Title",
                                        "value": "Project Parlay Automations"
                                    }
                                ]
                            },
                            "metadata": {
                                "designer": {
                                    "x": 4124,
                                    "y": 7
                                },
                                "restore": {
                                    "expect": {
                                        "qs": {
                                            "mode": "chose"
                                        },
                                        "method": {
                                            "mode": "chose",
                                            "label": "POST"
                                        },
                                        "headers": {
                                            "mode": "chose",
                                            "items": [
                                                null
                                            ]
                                        }
                                    },
                                    "parameters": {
                                        "__IMTCONN__": {
                                            "data": {
                                                "scoped": "true",
                                                "connection": "open-router-4ur2vj"
                                            },
                                            "label": "My OpenRouter connection"
                                        }
                                    }
                                },
                                "parameters": [
                                    {
                                        "name": "__IMTCONN__",
                                        "type": "account:open-router3,open-router-4ur2vj",
                                        "label": "Connection",
                                        "required": true
                                    }
                                ],
                                "expect": [
                                    {
                                        "name": "url",
                                        "type": "text",
                                        "label": "URL",
                                        "required": true
                                    },
                                    {
                                        "name": "method",
                                        "type": "select",
                                        "label": "Method",
                                        "required": true,
                                        "validate": {
                                            "enum": [
                                                "GET",
                                                "POST",
                                                "PUT",
                                                "PATCH",
                                                "DELETE"
                                            ]
                                        }
                                    },
                                    {
                                        "name": "headers",
                                        "spec": [
                                            {
                                                "name": "key",
                                                "type": "text",
                                                "label": "Key"
                                            },
                                            {
                                                "name": "value",
                                                "type": "text",
                                                "label": "Value"
                                            }
                                        ],
                                        "type": "array",
                                        "label": "Headers"
                                    },
                                    {
                                        "name": "qs",
                                        "spec": [
                                            {
                                                "name": "key",
                                                "type": "text",
                                                "label": "Key"
                                            },
                                            {
                                                "name": "value",
                                                "type": "text",
                                                "label": "Value"
                                            }
                                        ],
                                        "type": "array",
                                        "label": "Query String"
                                    },
                                    {
                                        "name": "body",
                                        "type": "any",
                                        "label": "Body"
                                    }
                                ]
                            }
                        },
                        {
                            "id": 14,
                            "module": "http:ActionSendData",
                            "version": 3,
                            "parameters": {
                                "handleErrors": true,
                                "useNewZLibDeCompress": true
                            },
                            "mapper": {
                                "ca": "",
                                "qs": [],
                                "url": "https://project-parlay-mattfavela.replit.app/api/external/mlb_projections",
                                "data": "{{trim(7.body.choices[].message.content)}}",
                                "gzip": true,
                                "method": "post",
                                "headers": [
                                    {
                                        "name": "Content-Type",
                                        "value": "application/json"
                                    },
                                    {
                                        "name": "X-API-Key",
                                        "value": "pk_ext_api_2025_ppadmin"
                                    }
                                ],
                                "timeout": "300",
                                "useMtls": false,
                                "authPass": "",
                                "authUser": "",
                                "bodyType": "raw",
                                "contentType": "application/json",
                                "serializeUrl": false,
                                "shareCookies": false,
                                "parseResponse": true,
                                "followRedirect": true,
                                "useQuerystring": false,
                                "followAllRedirects": false,
                                "rejectUnauthorized": true
                            },
                            "metadata": {
                                "designer": {
                                    "x": 5015,
                                    "y": 21
                                },
                                "restore": {
                                    "expect": {
                                        "qs": {
                                            "mode": "chose"
                                        },
                                        "method": {
                                            "mode": "chose",
                                            "label": "POST"
                                        },
                                        "headers": {
                                            "mode": "chose",
                                            "items": [
                                                null,
                                                null
                                            ]
                                        },
                                        "bodyType": {
                                            "label": "Raw"
                                        },
                                        "contentType": {
                                            "label": "Empty"
                                        }
                                    }
                                },
                                "parameters": [
                                    {
                                        "name": "handleErrors",
                                        "type": "boolean",
                                        "label": "Evaluate all states as errors (except for 2xx and 3xx )",
                                        "required": true
                                    },
                                    {
                                        "name": "useNewZLibDeCompress",
                                        "type": "hidden"
                                    }
                                ],
                                "expect": [
                                    {
                                        "name": "url",
                                        "type": "url",
                                        "label": "URL",
                                        "required": true
                                    },
                                    {
                                        "name": "serializeUrl",
                                        "type": "boolean",
                                        "label": "Serialize URL",
                                        "required": true
                                    },
                                    {
                                        "name": "method",
                                        "type": "select",
                                        "label": "Method",
                                        "required": true,
                                        "validate": {
                                            "enum": [
                                                "get",
                                                "head",
                                                "post",
                                                "put",
                                                "patch",
                                                "delete",
                                                "options"
                                            ]
                                        }
                                    },
                                    {
                                        "name": "headers",
                                        "spec": [
                                            {
                                                "name": "name",
                                                "type": "text",
                                                "label": "Name",
                                                "required": true
                                            },
                                            {
                                                "name": "value",
                                                "type": "text",
                                                "label": "Value"
                                            }
                                        ],
                                        "type": "array",
                                        "label": "Headers"
                                    },
                                    {
                                        "name": "qs",
                                        "spec": [
                                            {
                                                "name": "name",
                                                "type": "text",
                                                "label": "Name",
                                                "required": true
                                            },
                                            {
                                                "name": "value",
                                                "type": "text",
                                                "label": "Value"
                                            }
                                        ],
                                        "type": "array",
                                        "label": "Query String"
                                    },
                                    {
                                        "name": "bodyType",
                                        "type": "select",
                                        "label": "Body type",
                                        "validate": {
                                            "enum": [
                                                "raw",
                                                "x_www_form_urlencoded",
                                                "multipart_form_data"
                                            ]
                                        }
                                    },
                                    {
                                        "name": "parseResponse",
                                        "type": "boolean",
                                        "label": "Parse response",
                                        "required": true
                                    },
                                    {
                                        "name": "authUser",
                                        "type": "text",
                                        "label": "User name"
                                    },
                                    {
                                        "name": "authPass",
                                        "type": "password",
                                        "label": "Password"
                                    },
                                    {
                                        "name": "timeout",
                                        "type": "uinteger",
                                        "label": "Timeout",
                                        "validate": {
                                            "max": 300,
                                            "min": 1
                                        }
                                    },
                                    {
                                        "name": "shareCookies",
                                        "type": "boolean",
                                        "label": "Share cookies with other HTTP modules",
                                        "required": true
                                    },
                                    {
                                        "name": "ca",
                                        "type": "cert",
                                        "label": "Self-signed certificate"
                                    },
                                    {
                                        "name": "rejectUnauthorized",
                                        "type": "boolean",
                                        "label": "Reject connections that are using unverified (self-signed) certificates",
                                        "required": true
                                    },
                                    {
                                        "name": "followRedirect",
                                        "type": "boolean",
                                        "label": "Follow redirect",
                                        "required": true
                                    },
                                    {
                                        "name": "useQuerystring",
                                        "type": "boolean",
                                        "label": "Disable serialization of multiple same query string keys as arrays",
                                        "required": true
                                    },
                                    {
                                        "name": "gzip",
                                        "type": "boolean",
                                        "label": "Request compressed content",
                                        "required": true
                                    },
                                    {
                                        "name": "useMtls",
                                        "type": "boolean",
                                        "label": "Use Mutual TLS",
                                        "required": true
                                    },
                                    {
                                        "name": "contentType",
                                        "type": "select",
                                        "label": "Content type",
                                        "validate": {
                                            "enum": [
                                                "text/plain",
                                                "application/json",
                                                "application/xml",
                                                "text/xml",
                                                "text/html",
                                                "custom"
                                            ]
                                        }
                                    },
                                    {
                                        "name": "data",
                                        "type": "buffer",
                                        "label": "Request content"
                                    },
                                    {
                                        "name": "followAllRedirects",
                                        "type": "boolean",
                                        "label": "Follow all redirect",
                                        "required": true
                                    }
                                ]
                            }
                        }
                    ]
                }
            ]
        }
    ],
    "metadata": {
        "instant": false,
        "version": 1,
        "scenario": {
            "roundtrips": 1,
            "maxErrors": 3,
            "autoCommit": true,
            "autoCommitTriggerLast": true,
            "sequential": false,
            "slots": null,
            "confidential": false,
            "dataloss": false,
            "dlq": false,
            "freshVariables": false
        },
        "designer": {
            "orphans": []
        },
        "zone": "us2.make.com",
        "notes": []
    }
}