{"input": {"location": {"$mid": 1, "fsPath": "/home/<USER>/.vscode-server/extensions/extensions.json", "external": "file:///home/<USER>/.vscode-server/extensions/extensions.json", "path": "/home/<USER>/.vscode-server/extensions/extensions.json", "scheme": "file"}, "mtime": 1754553168729, "profile": true, "profileScanOptions": {"bailOutWhenFileNotFound": true}, "type": 1, "validate": true, "productVersion": "1.102.3", "productDate": "2025-07-29T03:00:23.339Z", "productCommit": "488a1f239235055e34e673291fb8d8c810886f81", "devMode": false, "language": "en", "translations": {}}, "result": [{"type": 1, "identifier": {"id": "github.vscode-pull-request-github", "uuid": "69ddd764-339a-4ecc-97c1-9c4ece58e36d"}, "manifest": {"name": "vscode-pull-request-github", "displayName": "GitHub Pull Requests", "description": "Pull Request and Issue Provider for GitHub", "icon": "resources/icons/github_logo.png", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-pull-request-github"}, "bugs": {"url": "https://github.com/Microsoft/vscode-pull-request-github/issues"}, "enabledApiProposals": ["activeComment", "chatParticipantAdditions", "chatParticipantPrivate", "codiconDecoration", "codeActionRanges", "commentingRangeHint", "commentReactor", "commentReveal", "commentThreadApplicability", "contribAccessibilityHelpContent", "contribCommentEditorActionsMenu", "contribCommentPeekContext", "contribCommentThreadAdditionalMenu", "contribCommentsViewThreadMenus", "contribEditorContentMenu", "contribShareMenu", "diffCommand", "quickDiffProvider", "remoteCodingAgents", "shareProvider", "tokenInformation", "treeViewMarkdownMessage"], "version": "0.114.3", "publisher": "GitHub", "engines": {"vscode": "^1.102.0"}, "categories": ["Other", "AI", "Cha<PERSON>"], "extensionDependencies": ["vscode.github-authentication"], "activationEvents": ["onStartupFinished", "onFileSystem:newIssue", "onFileSystem:pr", "onFileSystem:githubpr", "onFileSystem:review", "onWebviewPanel:pr.codingAgentSessionLogView"], "browser": "./dist/browser/extension", "l10n": "./dist/browser/extension", "main": "./dist/extension", "capabilities": {"untrustedWorkspaces": {"supported": true}, "virtualWorkspaces": true}, "contributes": {"remoteCodingAgents": [{"id": "githubCodingAgent", "command": "githubpr.remoteAgent", "displayName": "GitHub Copilot coding agent", "description": "Copilot coding agent is a remote, autonomous software development agent. Developers delegate tasks to the agent, which iterates on pull requests based on feedback and reviews.", "followUpRegex": "open-pull-request-webview.*((%7B.*?%7D)|(\\{.*?\\}))", "when": "config.githubPullRequests.codingAgent.enabled && config.githubPullRequests.codingAgent.uiIntegration && copilotCodingAgentAssignable"}], "chatParticipants": [{"id": "githubpr", "name": "githubpr", "fullName": "GitHub Pull Requests", "description": "Chat participant for GitHub Pull Requests extension", "when": "config.githubPullRequests.experimental.chat"}], "configuration": {"type": "object", "title": "GitHub Pull Requests", "properties": {"githubPullRequests.pullRequestTitle": {"deprecationMessage": "The pull request title now uses the same defaults as GitHub, and can be edited before create.", "type": "string", "enum": ["commit", "branch", "custom", "ask"], "enumDescriptions": ["Use the latest commit message", "Use the branch name", "Specify a custom title", "Ask which of the above methods to use"], "default": "ask", "description": "The title used when creating pull requests."}, "githubPullRequests.pullRequestDescription": {"type": "string", "enum": ["template", "commit", "none", "Copilot"], "enumDescriptions": ["Use a pull request template and commit description, or just use the commit description if no templates were found", "Use the latest commit message only", "Do not have a default description", "Generate a pull request title and description from GitHub Copilot. Requires that the GitHub Copilot extension is installed and authenticated. Will fall back to `commit` if Copilot is not set up."], "default": "template", "description": "The description used when creating pull requests."}, "githubPullRequests.defaultCreateOption": {"type": "string", "enum": ["lastUsed", "create", "createDraft", "createAutoMerge"], "markdownEnumDescriptions": ["The most recently used create option.", "The pull request will be created.", "The pull request will be created as a draft.", "The pull request will be created with auto-merge enabled. The merge method selected will be the default for the repo or the value of `githubPullRequests.defaultMergeMethod` if set."], "default": "lastUsed", "description": "The create option that the \"Create\" button will default to when creating a pull request."}, "githubPullRequests.createDraft": {"type": "boolean", "default": false, "deprecationMessage": "Use the setting 'githubPullRequests.defaultCreateOption' instead.", "description": "Whether the \"Draft\" checkbox will be checked by default when creating a pull request."}, "githubPullRequests.logLevel": {"type": "string", "enum": ["info", "debug", "off"], "default": "info", "description": "Logging for GitHub Pull Request extension. The log is emitted to the output channel named as GitHub Pull Request.", "markdownDeprecationMessage": "Log level is now controlled by the [Developer: Set Log Level...](command:workbench.action.setLogLevel) command. You can set the log level for the current session and also the default log level from there."}, "githubPullRequests.remotes": {"type": "array", "default": ["origin", "upstream"], "items": {"type": "string"}, "markdownDescription": "List of remotes, by name, to fetch pull requests from."}, "githubPullRequests.includeRemotes": {"type": "string", "enum": ["default", "all"], "default": "default", "deprecationMessage": "The setting `githubPullRequests.includeRemotes` has been deprecated. Use `githubPullRequests.remotes` to configure what remotes are shown.", "description": "By default we only support remotes created by users. If you want to see pull requests from remotes this extension created for pull requests, change this setting to 'all'."}, "githubPullRequests.queries": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "The label to display for the query in the Pull Requests tree"}, "query": {"type": "string", "description": "The query used for searching pull requests."}}, "default": {"label": "Assigned To Me", "query": "repo:${owner}/${repository} is:open assignee:${user}"}}, "scope": "resource", "markdownDescription": "Specifies what queries should be used in the GitHub Pull Requests tree. All queries are made against **the currently opened repos**. Each query object has a `label` that will be shown in the tree and a search `query` using [GitHub search syntax](https://help.github.com/en/articles/understanding-the-search-syntax). The following variables can be used: \n - `${user}` will resolve to the currently logged in user \n - `${owner}` will resolve to the owner of the current repository, ex. `microsoft` in `microsoft/vscode` \n - `${repository}` will resolve to the repository name, ex. `vscode` in `microsoft/vscode` \n - `${today-Nd}`, where `N` is the number of days ago, will resolve to a date, ex. `2025-01-04`. \n\n By default these queries define the categories \"Waiting For My Review\", \"Assigned To Me\" and \"Created By Me\". If you want to preserve these, make sure they are still in the array when you modify the setting.", "default": [{"label": "Copilot on My Behalf", "query": "repo:${owner}/${repository} is:open author:copilot involves:${user}"}, {"label": "Local Pull Request Branches", "query": "default"}, {"label": "Waiting For My Review", "query": "repo:${owner}/${repository} is:open review-requested:${user}"}, {"label": "Created By Me", "query": "repo:${owner}/${repository} is:open author:${user}"}, {"label": "All Open", "query": "default"}]}, "githubPullRequests.labelCreated": {"type": "array", "items": {"type": "string", "description": "Each string element is value of label that you want to add"}, "default": [], "description": "Group of labels that you want to add to the pull request automatically. Labels that don't exist in the repository won't be added."}, "githubPullRequests.defaultMergeMethod": {"type": "string", "enum": ["merge", "squash", "rebase"], "default": "merge", "description": "The method to use when merging pull requests."}, "githubPullRequests.showInSCM": {"type": "boolean", "default": false, "deprecationMessage": "This setting is deprecated. Views can now be dragged to any location.", "description": "When true, show GitHub Pull Requests within the SCM viewlet. Otherwise show a separate view container for them."}, "githubPullRequests.notifications": {"type": "string", "enum": ["pullRequests", "off"], "default": "off", "description": "If GitHub notifications should be shown to the user."}, "githubPullRequests.fileListLayout": {"type": "string", "enum": ["flat", "tree"], "default": "tree", "description": "The layout to use when displaying changed files list."}, "githubPullRequests.defaultDeletionMethod.selectLocalBranch": {"type": "boolean", "default": true, "description": "When true, the option to delete the local branch will be selected by default when deleting a branch from a pull request."}, "githubPullRequests.defaultDeletionMethod.selectRemote": {"type": "boolean", "default": true, "description": "When true, the option to delete the remote will be selected by default when deleting a branch from a pull request."}, "githubPullRequests.terminalLinksHandler": {"type": "string", "enum": ["github", "vscode", "ask"], "enumDescriptions": ["Create the pull request on GitHub", "Create the pull request in VS Code", "Ask which method to use"], "default": "ask", "description": "Default handler for terminal links."}, "githubPullRequests.createOnPublishBranch": {"type": "string", "enum": ["never", "ask"], "enumDescriptions": ["Never create a pull request when a branch is published.", "Ask if you want to create a pull request when a branch is published."], "default": "ask", "description": "Create a pull request when a branch is published."}, "githubPullRequests.commentExpandState": {"type": "string", "enum": ["expandUnresolved", "collapseAll"], "enumDescriptions": ["All unresolved comments will be expanded.", "All comments will be collapsed"], "default": "expandUnresolved", "description": "Controls whether comments are expanded when a document with comments is opened. Requires a reload to take effect for comments that have already been added."}, "githubPullRequests.useReviewMode": {"description": "Choose which pull request states will use review mode. \"Open\" pull requests will always use review mode. Setting to \"auto\" will use review mode for open, closed, and merged pull requests in web, but only open pull requests on desktop.", "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"merged": {"type": "boolean", "description": "Use review mode for merged pull requests.", "default": false}, "closed": {"type": "boolean", "description": "Use review mode for closed pull requests. Merged pull requests are not considered \"closed\".", "default": false}}, "required": ["merged", "closed"]}, {"type": "string", "enum": ["auto"]}], "default": "auto"}, "githubPullRequests.assignCreated": {"type": "string", "description": "All pull requests created with this extension will be assigned to this user. To assign to yourself, use the '${user}' variable."}, "githubPullRequests.pushBranch": {"type": "string", "enum": ["prompt", "always"], "default": "prompt", "enumDescriptions": ["Prompt to push the branch when creating a PR and the \"from\" branch is not available on the remote.", "Always push the branch when creating a PR and the \"from\" branch is not available on the remote."], "description": "Push the \"from\" branch when creating a PR and the \"from\" branch is not available on the remote."}, "githubPullRequests.pullBranch": {"type": "string", "enum": ["prompt", "never", "always"], "default": "prompt", "markdownEnumDescriptions": ["Prompt to pull a PR branch when changes are detected in the PR.", "Never pull a PR branch when changes are detected in the PR.", "Always pull a PR branch when changes are detected in the PR. When `\"git.autoStash\": true` this will instead `prompt` to prevent unexpected file changes."], "description": "Pull changes from the remote when a PR branch is checked out locally. Changes are detected when the PR is manually refreshed and during periodic background updates."}, "githubPullRequests.allowFetch": {"type": "boolean", "default": true, "description": "Allows `git fetch` to be run for checked-out pull request branches when checking for updates to the pull request."}, "githubPullRequests.ignoredPullRequestBranches": {"type": "array", "default": [], "items": {"type": "string", "description": "Branch name"}, "description": "Prevents branches that are associated with a pull request from being automatically detected. This will prevent review mode from being entered on these branches."}, "githubPullRequests.neverIgnoreDefaultBranch": {"type": "boolean", "description": "Never offer to ignore a pull request associated with the default branch of a repository."}, "githubPullRequests.overrideDefaultBranch": {"type": "string", "description": "The default branch for a repository is set on github.com. With this setting, you can override that default with another branch."}, "githubPullRequests.postCreate": {"type": "string", "enum": ["none", "openOverview", "checkoutDefaultBranch", "checkoutDefaultBranchAndShow", "checkoutDefaultBranchAndCopy"], "description": "The action to take after creating a pull request.", "default": "openOverview", "enumDescriptions": ["No action", "Open the overview page of the pull request", "Checkout the default branch of the repository", "Checkout the default branch of the repository and show the pull request in the Pull Requests view", "Checkout the default branch of the repository and copy a link to the pull request to the clipboard"]}, "githubPullRequests.defaultCommentType": {"type": "string", "enum": ["single", "review"], "default": "review", "description": "The default comment type to use when submitting a comment and there is no active review", "enumDescriptions": ["Submits the comment as a single comment that will be immediately visible to other users", "Submits the comment as a review comment that will be visible to other users once the review is submitted"]}, "githubPullRequests.quickDiff": {"type": "boolean", "description": "Enables quick diff in the editor gutter for checked-out pull requests. Requires a reload to take effect", "default": false}, "githubPullRequests.setAutoMerge": {"type": "boolean", "description": "Checks the \"Auto-merge\" checkbox in the \"Create Pull Request\" view.", "deprecationMessage": "Use the setting 'githubPullRequests.defaultCreateOption' instead.", "default": false}, "githubPullRequests.pullPullRequestBranchBeforeCheckout": {"type": "string", "description": "Controls whether the pull request branch is pulled before checkout. Can also be set to additionally merge updates from the base branch.", "enum": ["never", "pull", "pullAndMergeBase", "pullAndUpdateBase"], "default": "pull", "enumDescriptions": ["Never pull the pull request branch before checkout.", "Pull the pull request branch before checkout.", "Pull the pull request branch before checkout, fetch the base branch, and merge the base branch into the pull request branch.", "Pull the pull request branch before checkout, fetch the base branch, merge the base branch into the pull request branch, and finally push the pull request branch to the remote."]}, "githubPullRequests.upstreamRemote": {"type": "string", "enum": ["add", "never"], "markdownDescription": "Controls whether an `upstream` remote is automatically added for forks", "markdownEnumDescriptions": ["An `upstream` remote will be automatically added for forks", "An `upstream` remote will never be automatically added for forks"], "default": "add"}, "githubPullRequests.createDefaultBaseBranch": {"type": "string", "enum": ["repositoryDefault", "createdFromBranch", "auto"], "markdownEnumDescriptions": ["The default branch of the repository", "The branch that the current branch was created from, if known", "When the current repository is a fork, this will work like \"repositoryDefault\". Otherwise, it will work like \"createdFromBranch\"."], "default": "auto", "markdownDescription": "Controls what the base branch picker defaults to when creating a pull request"}, "githubPullRequests.experimental.chat": {"type": "boolean", "markdownDescription": "Enables the `@githubpr` Copilot chat participant in the chat view. `@githubpr` can help search for issues and pull requests, suggest fixes for issues, and summarize issues, pull requests, and notifications.", "default": true}, "githubPullRequests.codingAgent.enabled": {"type": "boolean", "default": true, "markdownDescription": "Enables integration with the asynchronous Copilot coding agent. The '#copilotCodingAgent' tool will be available in agent mode when this setting is enabled.", "tags": ["experimental"]}, "githubPullRequests.codingAgent.autoCommitAndPush": {"type": "boolean", "default": true, "markdownDescription": "Allow automatic git operations (commit, push) to be performed when starting a coding agent session", "tags": ["experimental"]}, "githubPullRequests.codingAgent.uiIntegration": {"type": "boolean", "default": false, "markdownDescription": "Enables UI integration within VS Code to create new coding agent sessions.", "tags": ["experimental", "onExP"]}, "githubPullRequests.experimental.notificationsMarkPullRequests": {"type": "string", "markdownDescription": "Adds an action in the Notifications view to mark pull requests with no non-empty reviews, comments, or commits since you last viewed the pull request as read.", "enum": ["markAsDone", "mark<PERSON><PERSON><PERSON>", "none"], "default": "none"}, "githubPullRequests.experimental.useQuickChat": {"type": "boolean", "markdownDescription": "Controls whether the Copilot \"Summarize\" commands in the Pull Requests, Issues, and Notifications views will use quick chat. Only has an effect if `#githubPullRequests.experimental.chat#` is enabled.", "default": false}, "githubPullRequests.webviewRefreshInterval": {"type": "number", "markdownDescription": "The interval, in seconds, at which the pull request and issues webviews are refreshed when the webview is the active tab.", "default": 60}, "githubIssues.ignoreMilestones": {"type": "array", "default": [], "description": "An array of milestones titles to never show issues from."}, "githubIssues.createIssueTriggers": {"type": "array", "items": {"type": "string", "description": "String that enables the 'Create issue from comment' code action. Should not contain whitespace."}, "default": ["TODO", "todo", "BUG", "FIXME", "ISSUE", "HACK"], "description": "Strings that will cause the 'Create issue from comment' code action to show."}, "githubIssues.createInsertFormat": {"type": "string", "enum": ["number", "url"], "default": "number", "description": "Controls whether an issue number (ex. #1234) or a full url (ex. https://github.com/owner/name/issues/1234) is inserted when the Create Issue code action is run."}, "githubIssues.issueCompletions.enabled": {"type": "boolean", "default": true, "description": "Controls whether completion suggestions are shown for issues."}, "githubIssues.userCompletions.enabled": {"type": "boolean", "default": true, "description": "Controls whether completion suggestions are shown for users."}, "githubIssues.ignoreCompletionTrigger": {"type": "array", "items": {"type": "string", "description": "Language that issue completions should not trigger on '#'."}, "default": ["coffeescript", "diff", "dockerfile", "dockercompose", "ignore", "ini", "julia", "makefile", "perl", "powershell", "python", "r", "ruby", "shellscript", "yaml"], "description": "Languages that the '#' character should not be used to trigger issue completion suggestions."}, "githubIssues.ignoreUserCompletionTrigger": {"type": "array", "items": {"type": "string", "description": "Language that user completions should not trigger on '@'."}, "default": [], "description": "Languages that the '@' character should not be used to trigger user completion suggestions."}, "githubIssues.issueBranchTitle": {"type": "string", "default": "${user}/issue${issueNumber}", "markdownDescription": "Advanced settings for the name of the branch that is created when you start working on an issue. \n- `${user}` will be replace with the currently logged in username \n- `${issueNumber}` will be replaced with the current issue number \n- `${sanitizedIssueTitle}` will be replaced with the issue title, with all spaces and unsupported characters (https://git-scm.com/docs/git-check-ref-format) removed. For lowercase, use `${sanitizedLowercaseIssueTitle}` "}, "githubIssues.useBranchForIssues": {"type": "string", "enum": ["on", "off", "prompt"], "enumDescriptions": ["A branch will always be checked out when you start working on an issue. If the branch doesn't exist, it will be created.", "A branch will not be created when you start working on an issue. If you have worked on an issue before and a branch was created for it, that same branch will be checked out.", "A prompt will show for setting the name of the branch that will be created and checked out."], "default": "on", "markdownDescription": "Determines whether a branch should be checked out when working on an issue. To configure the name of the branch, set `#githubIssues.issueBranchTitle#`."}, "githubIssues.issueCompletionFormatScm": {"type": "string", "default": "${issueTitle}\nFixes ${issueNumberLabel}", "markdownDescription": "Sets the format of issue completions in the SCM inputbox. \n- `${user}` will be replace with the currently logged in username \n- `${issueNumber}` will be replaced with the current issue number \n- `${issueNumberLabel}` will be replaced with a label formatted as #number or owner/repository#number, depending on whether the issue is in the current repository"}, "githubIssues.workingIssueFormatScm": {"type": "string", "default": "${issueTitle} \nFixes ${issueNumberLabel}", "markdownDescription": "Sets the format of the commit message that is set in the SCM inputbox when you **Start Working on an Issue**. Defaults to `${issueTitle} \nFixes ${issueNumberLabel}`", "editPresentation": "multilineText"}, "githubIssues.queries": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "The label to display for the query in the Issues tree."}, "query": {"type": "string", "markdownDescription": "The search query using [GitHub search syntax](https://help.github.com/en/articles/understanding-the-search-syntax) with variables. The variable `${user}` can be used to specify the logged in user within a search. `${owner}` and `${repository}` can be used to specify the repository by using `repo:${owner}/${repository}`."}, "groupBy": {"type": "array", "markdownDescription": "The categories to group issues by when displaying them, in the order in which they should be grouped", "items": {"type": "string", "enum": ["repository", "milestone"], "enumDescriptions": ["Group issues by their milestone.", "Group issues by their repository."]}}}}, "scope": "resource", "markdownDescription": "Specifies what queries should be used in the GitHub issues tree using [GitHub search syntax](https://help.github.com/en/articles/understanding-the-search-syntax) with variables. The first query listed will be expanded in the Issues view. The \"default\" query includes issues assigned to you by Milestone. If you want to preserve these, make sure they are still in the array when you modify the setting.", "default": [{"label": "My Issues", "query": "is:open assignee:${user} repo:${owner}/${repository}", "groupBy": ["milestone"]}, {"label": "Created Issues", "query": "author:${user} state:open repo:${owner}/${repository} sort:created-desc"}, {"label": "Recent Issues", "query": "state:open repo:${owner}/${repository} sort:updated-desc"}]}, "githubIssues.assignWhenWorking": {"type": "boolean", "default": true, "description": "Assigns the issue you're working on to you. Only applies when the issue you're working on is in a repo you currently have open."}, "githubPullRequests.focusedMode": {"properties": {"oneOf": [{"type": "boolean"}, {"type": "string"}]}, "enum": ["firstDiff", "overview", "multiDiff", false], "enumDescriptions": ["Show the first diff in the pull request. If there are no changes, show the overview.", "Show the overview of the pull request.", "Show all diffs in the pull request. If there are no changes, show the overview.", "Do not change the layout."], "default": "multiDiff", "description": "The layout to use when a pull request is checked out. Set to false to prevent layout changes."}, "githubPullRequests.showPullRequestNumberInTree": {"type": "boolean", "default": false, "description": "Shows the pull request number in the tree view."}, "githubIssues.alwaysPromptForNewIssueRepo": {"type": "boolean", "default": false, "description": "Enabling will always prompt which repository to create an issue in instead of basing off the current open file."}}}, "viewsContainers": {"activitybar": [{"id": "github-pull-requests", "title": "GitHub", "icon": "$(github)"}, {"id": "github-pull-request", "title": "GitHub Pull Request", "icon": "$(git-pull-request)"}]}, "views": {"github-pull-requests": [{"id": "github:login", "name": "<PERSON><PERSON>", "when": "ReposManagerStateContext == NeedsAuthentication", "icon": "$(git-pull-request)"}, {"id": "pr:gith<PERSON>", "name": "Pull Requests", "when": "ReposManagerStateContext != NeedsAuthentication && !github:resolvingConflicts", "icon": "$(git-pull-request)", "accessibilityHelpContent": "Helpful commands include:\n-GitHub Pull Requests: Refresh Pull Requests List<keybinding:pr.refreshList>\n-GitHub Pull Requests: Focus on Issues View<keybinding:issues:github.focus> \n-GitHub Pull Requests: Focus on Pull Requests View<keybinding:pr:github.focus>\n-GitHub Issues: Copy GitHub Permalink<keybinding:issue.copyGithubPermalink>\n-GitHub Issues: Create an Issue<keybinding:issue.createIssueFromFile>\n-GitHub Pull Requests: Create Pull Request<keybinding:pr.create>"}, {"id": "issues:github", "name": "Issues", "when": "ReposManagerStateContext != NeedsAuthentication && !github:resolvingConflicts", "icon": "$(issues)", "accessibilityHelpContent": "Helpful commands include:\n-GitHub Pull Requests: Refresh Pull Requests List<keybinding:pr.refreshList>\n-GitHub Pull Requests: Focus on Issues View<keybinding:issues:github.focus> \n-GitHub Pull Requests: Focus on Pull Requests View<keybinding:pr:github.focus>\n-GitHub Issues: Copy GitHub Permalink<keybinding:issue.copyGithubPermalink>\n-GitHub Issues: Create an Issue<keybinding:issue.createIssueFromFile>\n-GitHub Pull Requests: Create Pull Request<keybinding:pr.create>"}, {"id": "notifications:github", "name": "Notifications", "when": "ReposManagerStateContext != NeedsAuthentication && !github:resolvingConflicts && (remoteName != codespaces || !isWeb)", "icon": "$(bell)", "accessibilityHelpContent": "Helpful commands include:\n-GitHub Pull Requests: Refresh Pull Requests List<keybinding:pr.refreshList>\n-GitHub Pull Requests: Focus on Issues View<keybinding:issues:github.focus> \n-GitHub Pull Requests: Focus on Pull Requests View<keybinding:pr:github.focus>\n-GitHub Issues: Copy GitHub Permalink<keybinding:issue.copyGithubPermalink>\n-GitHub Issues: Create an Issue<keybinding:issue.createIssueFromFile>\n-GitHub Pull Requests: Create Pull Request<keybinding:pr.create>", "visibility": "collapsed"}, {"id": "github:conflictResolution", "name": "Conflict Resolution", "when": "github:resolvingConflicts", "icon": "$(git-merge)"}], "github-pull-request": [{"id": "github:createPullRequestWebview", "type": "webview", "name": "Create", "when": "github:createPullRequest || github:revertPullRequest", "icon": "$(git-pull-request-create)", "visibility": "visible", "initialSize": 2}, {"id": "github:compareChangesFiles", "name": "Files Changed", "when": "github:createPullRequest", "icon": "$(git-compare)", "visibility": "visible", "initialSize": 1}, {"id": "github:compare<PERSON><PERSON><PERSON><PERSON>om<PERSON>s", "name": "Commits", "when": "github:createPullRequest", "icon": "$(git-compare)", "visibility": "visible", "initialSize": 1}, {"id": "prStatus:github", "name": "Changes In Pull Request", "when": "github:inReviewMode && !github:createPullRequest && !github:revertPullRequest", "icon": "$(git-pull-request)", "visibility": "visible", "initialSize": 3}, {"id": "github:activePullRequest", "type": "webview", "name": "Review Pull Request", "when": "github:inReviewMode && github:focusedReview && !github:createPullRequest && !github:revertPullRequest && github:activePRCount <= 1", "icon": "$(git-pull-request)", "initialSize": 2}, {"id": "github:activePullRequest:welcome", "name": "Active Pull Request", "when": "!github:stateValidated && github:focusedReview", "icon": "$(git-pull-request)"}]}, "commands": [{"command": "githubpr.remoteAgent", "title": "Remote agent integration", "enablement": "config.githubPullRequests.codingAgent.enabled"}, {"command": "github.api.preloadPullRequest", "title": "Preload Pull Request", "category": "GitHub Pull Requests"}, {"command": "pr.create", "title": "Create Pull Request", "icon": "$(git-pull-request-create)", "category": "GitHub Pull Requests"}, {"command": "pr.pushAndCreate", "title": "Create Pull Request", "icon": "$(git-pull-request-create)", "category": "GitHub Pull Requests"}, {"command": "pr.pick", "title": "Checkout Pull Request", "category": "GitHub Pull Requests", "enablement": "viewItem =~ /hasHeadRef/", "icon": "$(arrow-right)"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>", "title": "Open Changes", "category": "GitHub Pull Requests", "icon": "$(diff-multiple)"}, {"command": "pr.pickOnVscodeDev", "title": "Checkout Pull Request on vscode.dev", "category": "GitHub Pull Requests", "icon": "$(globe)"}, {"command": "pr.exit", "title": "Checkout Default Branch", "category": "GitHub Pull Requests"}, {"command": "pr.dismissNotification", "title": "Dismiss Notification", "category": "GitHub Pull Requests"}, {"command": "pr.merge", "title": "<PERSON><PERSON>quest", "category": "GitHub Pull Requests"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON> Request Ready For Review", "category": "GitHub Pull Requests"}, {"command": "pr.openPullRequestOnGitHub", "title": "Open Pull Request on GitHub", "category": "GitHub Pull Requests", "icon": "$(globe)"}, {"command": "pr.openAllD<PERSON>s", "title": "Open All Diffs", "category": "GitHub Pull Requests"}, {"command": "pr.refreshPullRequest", "title": "Refresh Pull Request", "category": "GitHub Pull Requests"}, {"command": "pr.openFileOnGitHub", "title": "Open File on GitHub", "category": "GitHub Pull Requests"}, {"command": "pr.copyCommit<PERSON>ash", "title": "Copy Commit <PERSON>", "category": "GitHub Pull Requests"}, {"command": "pr.openOriginalFile", "title": "Open Original File", "category": "GitHub Pull Requests"}, {"command": "pr.openModifiedFile", "title": "Open Modified File", "category": "GitHub Pull Requests"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "title": "Open Diff View", "category": "GitHub Pull Requests", "icon": "$(compare-changes)"}, {"command": "pr.openDiffViewFromEditor", "title": "Open Pull Request Diff View", "category": "GitHub Pull Requests", "icon": "$(git-pull-request)"}, {"command": "pr.openDescription", "title": "View Pull Request Description", "category": "GitHub Pull Requests", "when": "github:inReviewMode", "icon": "$(note)"}, {"command": "pr.openDescriptionToTheSide", "title": "Open Pull Request Description to the Side", "icon": "$(split-horizontal)"}, {"command": "pr.refreshDescription", "title": "Refresh Pull Request Description", "category": "GitHub Pull Requests"}, {"command": "pr.focusDescriptionInput", "title": "Focus Pull Request Description Review Input", "category": "GitHub Pull Requests"}, {"command": "pr.showDiffSinceLastReview", "title": "Show Changes Since Last Review", "icon": "$(git-pull-request-new-changes)"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Show All Changes", "icon": "$(git-pull-request-go-to-changes)"}, {"command": "pr.checkoutByNumber", "title": "Checkout Pull Request by Number", "category": "GitHub Pull Requests", "icon": "$(symbol-numeric)"}, {"command": "review.openFile", "title": "Open File", "icon": "$(go-to-file)"}, {"command": "review.openLocalFile", "title": "Open File", "icon": "$(go-to-file)"}, {"command": "review.suggestDiff", "title": "Suggest Edit", "category": "GitHub Pull Requests"}, {"command": "pr.refreshList", "title": "Refresh <PERSON>ull Requests List", "icon": "$(refresh)", "category": "GitHub Pull Requests"}, {"command": "pr.setFileListLayoutAsTree", "title": "View as Tree", "icon": "$(list-tree)", "category": "GitHub Pull Requests"}, {"command": "pr.setFileListLayoutAsFlat", "title": "View as List", "icon": "$(list-flat)", "category": "GitHub Pull Requests"}, {"command": "pr.refresh<PERSON><PERSON><PERSON>", "title": "Refresh", "icon": "$(refresh)", "category": "GitHub Pull Requests"}, {"command": "pr.configurePRViewl<PERSON>", "title": "Configure...", "category": "GitHub Pull Requests", "icon": "$(gear)"}, {"command": "pr.deleteLocalBranch", "title": "Delete Local Branch", "category": "GitHub Pull Requests"}, {"command": "pr.signin", "title": "Sign in to GitHub", "category": "GitHub Pull Requests"}, {"command": "pr.signinNoEnterprise", "title": "Sign in to GitHub", "category": "GitHub Pull Requests"}, {"command": "pr.sign<PERSON><PERSON><PERSON>", "title": "Sign in to GitHub Enterprise", "category": "GitHub Pull Requests"}, {"command": "pr.deleteLocalBranchesNRemotes", "title": "Delete local branches and remotes", "category": "GitHub Pull Requests"}, {"command": "pr.createComment", "title": "Add Review Comment", "category": "GitHub Pull Requests", "enablement": "!commentIsEmpty"}, {"command": "pr.createSingleComment", "title": "Add Comment", "category": "GitHub Pull Requests", "enablement": "!commentIsEmpty"}, {"command": "pr.makeSuggestion", "title": "Make Code Suggestion", "category": "GitHub Pull Requests"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Start Review", "category": "GitHub Pull Requests", "enablement": "!commentIsEmpty"}, {"command": "pr.editComment", "title": "Edit Comment", "category": "GitHub Pull Requests", "icon": "$(edit)", "enablement": "!(comment =~ /temporary/)"}, {"command": "pr.cancelEditComment", "title": "Cancel", "category": "GitHub Pull Requests"}, {"command": "pr.saveComment", "title": "Save", "category": "GitHub Pull Requests", "enablement": "!commentIsEmpty"}, {"command": "pr.deleteComment", "title": "Delete Comment", "category": "GitHub Pull Requests", "icon": "$(trash)", "enablement": "!(comment =~ /temporary/)"}, {"command": "pr.<PERSON><PERSON><PERSON>iewThread", "title": "Resolve Conversation", "category": "GitHub Pull Requests", "icon": "$(check)"}, {"command": "pr.unresolveReviewThread", "title": "Unresolve Conversation", "category": "GitHub Pull Requests"}, {"command": "pr.unresolveReviewThreadFromView", "title": "Unresolve Conversation", "category": "GitHub Pull Requests", "icon": "$(sync)"}, {"command": "pr.diffOutdatedCommentWithHead", "title": "Diff Comment with HEAD", "category": "GitHub Pull Requests", "icon": "$(git-compare)"}, {"command": "pr.signinAndRefreshList", "title": "Sign in and Refresh", "category": "GitHub Pull Requests"}, {"command": "pr.configure<PERSON><PERSON><PERSON>", "title": "Configure Remotes...", "category": "GitHub Pull Requests"}, {"command": "pr.refreshActivePullRequest", "title": "Refresh", "category": "GitHub Pull Requests", "icon": "$(refresh)"}, {"command": "pr.<PERSON><PERSON><PERSON>", "title": "<PERSON> File As Viewed", "category": "GitHub Pull Requests", "icon": "$(pass)"}, {"command": "pr.unmarkF<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON> File As Not Viewed", "category": "GitHub Pull Requests", "icon": "$(pass-filled)"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Go to Review", "category": "GitHub Pull Requests"}, {"command": "pr.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>", "title": "Collapse All Comments", "category": "Comments", "icon": "$(collapse-all)"}, {"command": "pr.edit<PERSON><PERSON><PERSON>", "title": "Edit Query", "category": "GitHub Pull Requests", "icon": "$(edit)"}, {"command": "pr.openPullsWebsite", "title": "Open on GitHub", "category": "GitHub Pull Requests", "icon": "$(globe)"}, {"command": "pr.reset<PERSON><PERSON>wedF<PERSON>", "title": "Reset Viewed Files", "category": "GitHub Pull Requests"}, {"command": "pr.goToNextDiffInPr", "title": "Go to Next Diff in Pull Request", "category": "GitHub Pull Requests"}, {"command": "pr.goToPreviousDiffInPr", "title": "Go to Previous Diff in Pull Request", "category": "GitHub Pull Requests"}, {"command": "pr.copyCommentLink", "title": "Copy Comment Link", "category": "GitHub Pull Requests", "icon": "$(copy)", "enablement": "!(comment =~ /temporary/)"}, {"command": "pr.applySuggestion", "title": "Apply Suggestion", "category": "GitHub Pull Requests", "icon": "$(replace)"}, {"command": "pr.applySuggestionWithCopilot", "title": "Apply Suggestion With Copilot", "category": "GitHub Pull Requests", "icon": "$(sparkle)"}, {"command": "pr.addAssigneesToNewPr", "title": "Add Assignees", "category": "GitHub Pull Requests", "icon": "$(account)"}, {"command": "pr.addReviewersToNewPr", "title": "Add Reviewers", "category": "GitHub Pull Requests", "icon": "$(feedback)"}, {"command": "pr.addLabelsToNewPr", "title": "Apply Labels", "category": "GitHub Pull Requests", "icon": "$(tag)"}, {"command": "pr.addMilestoneToNewPr", "title": "Set Milestone", "category": "GitHub Pull Requests", "icon": "$(milestone)"}, {"command": "pr.addProjectsToNewPr", "title": "Set Projects", "category": "GitHub Pull Requests", "icon": "$(github-project)"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Pre-review Changes", "category": "GitHub Pull Requests", "enablement": "!pr:preReviewing && !pr:creating", "icon": "$(comment)"}, {"command": "pr.addFileComment", "title": "Add File Comment", "category": "GitHub Pull Requests", "icon": "$(comment)"}, {"command": "pr.checkoutFromReadonlyFile", "title": "Checkout Pull Request", "category": "GitHub Pull Requests"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Resolve Conflict", "category": "GitHub Pull Requests"}, {"command": "pr.accept<PERSON><PERSON>ge", "title": "Accept <PERSON><PERSON>", "category": "GitHub Pull Requests"}, {"command": "pr.closeRelatedEditors", "title": "Close All Pull Request Editors", "category": "GitHub Pull Requests"}, {"command": "pr.toggleEditorCommentingOn", "title": "Toggle Editor Commenting On", "category": "GitHub Pull Requests", "icon": "$(eye-closed)"}, {"command": "pr.toggleEditorCom<PERSON>ingOff", "title": "Toggle Editor Commenting Off", "category": "GitHub Pull Requests", "icon": "$(eye)"}, {"command": "review.diffWithPrHead", "title": "Compare Base With Pull Request Head (readonly)", "category": "GitHub Pull Requests"}, {"command": "review.diffLocalWithPrHead", "title": "Compare Pull Request Head with Local", "category": "GitHub Pull Requests"}, {"command": "review.approve", "title": "Approve", "category": "GitHub Pull Requests"}, {"command": "review.comment", "title": "Comment", "category": "GitHub Pull Requests"}, {"command": "review.requestChanges", "title": "Request Changes", "category": "GitHub Pull Requests"}, {"command": "review.approveOnDotCom", "title": "Approve on github.com", "category": "GitHub Pull Requests"}, {"command": "review.requestChangesOnDotCom", "title": "Request changes on github.com", "category": "GitHub Pull Requests"}, {"command": "review.approveDescription", "title": "Approve", "category": "GitHub Pull Requests"}, {"command": "review.commentDescription", "title": "Comment", "category": "GitHub Pull Requests"}, {"command": "review.requestChangesDescription", "title": "Request Changes", "category": "GitHub Pull Requests"}, {"command": "review.approveOnDotComDescription", "title": "Approve on github.com", "category": "GitHub Pull Requests"}, {"command": "review.requestChangesOnDotComDescription", "title": "Request changes on github.com", "category": "GitHub Pull Requests"}, {"command": "review.createSuggestionsFromChanges", "title": "Create Pull Request Suggestions", "icon": "$(comment)", "category": "GitHub Pull Requests"}, {"command": "review.createSuggestionFromChange", "title": "Convert to Pull Request Suggestion", "icon": "$(comment)", "category": "GitHub Pull Requests"}, {"command": "review.copyPrLink", "title": "Copy <PERSON>ull Request Link", "category": "GitHub Pull Requests"}, {"command": "pr.createPrMenuCreate", "title": "Create", "category": "GitHub Pull Requests"}, {"command": "pr.createPrMenuDraft", "title": "Create Draft", "category": "GitHub Pull Requests"}, {"command": "pr.createPrMenuMergeWhenReady", "title": "Create + Merge When Ready", "category": "GitHub Pull Requests"}, {"command": "pr.createPrMenuMerge", "title": "Create + Auto-Merge", "category": "GitHub Pull Requests"}, {"command": "pr.createPrMenuSquash", "title": "Create + Auto-Squash", "category": "GitHub Pull Requests"}, {"command": "pr.createPrMenuRebase", "title": "Create + Auto-Rebase", "category": "GitHub Pull Requests"}, {"command": "issue.openDescription", "title": "View Issue Description", "category": "GitHub Issues"}, {"command": "issue.createIssueFromSelection", "title": "Create Issue From Selection", "category": "GitHub Issues"}, {"command": "issue.createIssueFromClipboard", "title": "Create Issue From Clipboard", "category": "GitHub Issues"}, {"command": "pr.copyVscodeDevPrLink", "title": "Copy vscode.dev Pull Request Link", "category": "GitHub Issues"}, {"command": "pr.refreshComments", "title": "Refresh Pull Request Comments", "category": "GitHub Pull Requests", "icon": "$(refresh)"}, {"command": "issue.copyGithubDevLinkWithoutRange", "title": "Copy github.dev Link", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyGithubDevLinkFile", "title": "Copy github.dev Link", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyGithubDevLink", "title": "Copy github.dev Link", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyGithubPermalink", "title": "Copy GitHub Permalink", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyGithubHeadLink", "title": "<PERSON><PERSON>itHub Head Link", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyGithubPermalinkWithoutRange", "title": "Copy GitHub Permalink", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyGithubHeadLinkWithoutRange", "title": "<PERSON><PERSON>itHub Head Link", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyMarkdownGithubPermalink", "title": "<PERSON><PERSON> as <PERSON><PERSON>", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.copyMarkdownGithubPermalinkWithoutRange", "title": "<PERSON><PERSON> as <PERSON><PERSON>", "category": "GitHub Issues", "enablement": "!isInEmbeddedEditor"}, {"command": "issue.openGithubPermalink", "title": "Open Permalink on GitHub", "category": "GitHub Issues"}, {"command": "issue.openIssue", "title": "Open Issue on GitHub", "category": "GitHub Issues", "icon": "$(globe)"}, {"command": "issue.copyIssueNumber", "title": "Copy Issue Number", "category": "GitHub Issues"}, {"command": "issue.copyIssueUrl", "title": "Copy Issue Link", "category": "GitHub Issues"}, {"command": "issue.refresh", "title": "Refresh", "category": "GitHub Issues", "icon": "$(refresh)"}, {"command": "issue.suggest<PERSON><PERSON><PERSON>", "title": "Refresh Suggestions", "category": "GitHub Issues"}, {"command": "issue.startWorking", "title": "Start Working on Issue", "category": "GitHub Issues", "icon": "$(arrow-right)"}, {"command": "issue.startWorkingBranchDescriptiveTitle", "title": "Start Working on Issue and Checkout Topic Branch", "category": "GitHub Issues", "icon": "$(arrow-right)"}, {"command": "issue.continueWorking", "title": "Continue Working on Issue", "category": "GitHub Issues", "icon": "$(arrow-right)"}, {"command": "issue.startWorkingBranchPrompt", "title": "Start Working and Set Branch...", "category": "GitHub Issues"}, {"command": "issue.stopWorking", "title": "Stop Working on Issue", "category": "GitHub Issues", "icon": "$(primitive-square)"}, {"command": "issue.stopWorkingBranchDescriptiveTitle", "title": "Stop Working on Issue and Leave Topic Branch", "category": "GitHub Issues", "icon": "$(primitive-square)"}, {"command": "issue.statusBar", "title": "Current Issue Options", "category": "GitHub Issues"}, {"command": "issue.getCurrent", "title": "Get current issue", "category": "GitHub Issues"}, {"command": "issue.edit<PERSON>uery", "title": "Edit Query", "category": "GitHub Issues", "icon": "$(edit)"}, {"command": "issue.createIssue", "title": "Create an Issue", "category": "GitHub Issues", "icon": "$(plus)"}, {"command": "issue.createIssueFromFile", "title": "Create Issue", "icon": "$(check)", "enablement": "!issues.creatingFromFile"}, {"command": "issue.issueCompletion", "title": "Issue Completion Chosen"}, {"command": "issue.userCompletion", "title": "User Completion Chosen"}, {"command": "issue.signinAndRefreshList", "title": "Sign in and Refresh", "category": "GitHub Issues"}, {"command": "issue.goToLinkedCode", "title": "Go to Linked Code", "category": "GitHub Issues"}, {"command": "issues.openIssuesWebsite", "title": "Open on GitHub", "category": "GitHub Issues", "icon": "$(globe)"}, {"command": "issue.chatSummarizeIssue", "title": "Summarize With Copilot", "category": "GitHub Issues", "icon": "$(copilot)"}, {"command": "issue.chatSuggestFix", "title": "Suggest a Fix with Copilot", "category": "GitHub Issues", "icon": "$(sparkle)"}, {"command": "issues.configureIssuesViewlet", "title": "Configure...", "category": "GitHub Issues", "icon": "$(gear)"}, {"command": "notifications.refresh", "title": "Refresh", "category": "GitHub Notifications", "icon": "$(refresh)"}, {"command": "notifications.loadMore", "title": "Load More Notifications", "category": "GitHub Notifications"}, {"command": "notifications.sortByTimestamp", "title": "Sort by Timestamp", "category": "GitHub Notifications"}, {"command": "notifications.sortByPriority", "title": "Sort by Priority using Copilot", "category": "GitHub Notifications"}, {"command": "notification.openOnGitHub", "title": "Open on GitHub", "category": "GitHub Notifications", "icon": "$(globe)"}, {"command": "notification.chatSummarizeNotification", "title": "Summarize With Copilot", "category": "GitHub Notifications", "icon": "$(copilot)"}, {"command": "notification.markAsRead", "title": "<PERSON> <PERSON>", "category": "GitHub Notifications", "icon": "$(mail-read)"}, {"command": "notification.markAsDone", "title": "<PERSON> as <PERSON>", "category": "GitHub Notifications", "icon": "$(check-all)"}, {"command": "notifications.markPullRequestsAsRead", "title": "<PERSON> Requests as <PERSON>", "category": "GitHub Notifications", "icon": "$(git-pull-request-done)"}, {"command": "notifications.markPullRequestsAsDone", "title": "<PERSON> Requests as <PERSON>", "category": "GitHub Notifications", "icon": "$(git-pull-request-done)"}, {"command": "notifications.configureNotificationsViewlet", "title": "Configure...", "category": "GitHub Notifications", "icon": "$(gear)"}, {"command": "codingAgent.openSessionLog", "title": "Open Coding Agent Session Log", "category": "GitHub Pull Requests"}], "viewsWelcome": [{"view": "github:login", "when": "ReposManagerStateContext == NeedsAuthentication && github:hasGitHubRemotes", "contents": "You have not yet signed in with GitHub\n[Sign in](command:pr.signin)"}, {"view": "pr:gith<PERSON>", "when": "gitNotInstalled", "contents": "Git is not installed or otherwise not available. Install git or fix your git installation and then reload."}, {"view": "github:login", "when": "ReposManagerStateContext == NeedsAuthentication && !github:hasGitHubRemotes && gitOpenRepositoryCount", "contents": "You have not yet signed in with GitHub\n[Sign in](command:pr.signin<PERSON><PERSON>Enter<PERSON>)"}, {"view": "github:login", "when": "(ReposManagerStateContext == NeedsAuthentication) && ((!github:hasGitHubRemotes && gitOpenRepositoryCount) || config.github-enterprise.uri)", "contents": "[Sign in with GitHub Enterprise](command:pr.signinenter<PERSON>)"}, {"view": "pr:gith<PERSON>", "when": "git.state != initialized && !github:initialized && workspaceFolderCount > 0", "contents": "Loading..."}, {"view": "pr:gith<PERSON>", "when": "workspaceFolderCount > 0 && github:loadingPrsTree", "contents": "Loading..."}, {"view": "pr:gith<PERSON>", "when": "workspaceFolderCount == 0", "contents": "You have not yet opened a folder.\n[Open Folder](command:workbench.action.files.openFolder)"}, {"view": "pr:gith<PERSON>", "when": "git.state == initialized && gitOpenRepositoryCount == 0 && workspaceFolderCount > 0 && git.parentRepositoryCount == 0", "contents": "No git repositories found"}, {"view": "pr:gith<PERSON>", "when": "git.state == initialized && gitOpenRepositoryCount == 0 && workspaceFolderCount > 0 && git.parentRepositoryCount == 1", "contents": "A git repository was found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether git repositories in parent folders of workspaces or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders)."}, {"view": "pr:gith<PERSON>", "when": "git.state == initialized && gitOpenRepositoryCount == 0 && workspaceFolderCount > 0 && git.parentRepositoryCount > 1", "contents": "A git repository was found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether git repositories in parent folders of workspaces or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders)."}, {"view": "issues:github", "when": "git.state != initialized && !github:initialized && workspaceFolderCount > 0", "contents": "Loading..."}, {"view": "issues:github", "when": "workspaceFolderCount > 0 && github:loadingPrsTree", "contents": "Loading..."}, {"view": "issues:github", "when": "workspaceFolderCount == 0", "contents": "You have not yet opened a folder."}, {"view": "issues:github", "when": "git.state == initialized && gitOpenRepositoryCount == 0 && workspaceFolderCount > 0 && git.parentRepositoryCount == 0", "contents": "No git repositories found"}, {"view": "issues:github", "when": "git.state == initialized && gitOpenRepositoryCount == 0 && workspaceFolderCount > 0 && git.parentRepositoryCount == 1", "contents": "A git repository was found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether git repositories in parent folders of workspaces or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders)."}, {"view": "issues:github", "when": "git.state == initialized && gitOpenRepositoryCount == 0 && workspaceFolderCount > 0 && git.parentRepositoryCount > 1", "contents": "A git repository was found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether git repositories in parent folders of workspaces or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders)."}, {"view": "github:activePullRequest:welcome", "when": "!github:stateValidated", "contents": "Loading..."}, {"view": "notifications:github", "when": "!github:notificationCount", "contents": "Loading..."}, {"view": "notifications:github", "when": "ReposManagerStateContext == RepositoriesLoaded && github:notificationCount == -1", "contents": "No notifications, your inbox is empty $(rocket)"}], "keybindings": [{"key": "ctrl+shift+space", "command": "issue.suggest<PERSON><PERSON><PERSON>", "when": "suggestWidgetVisible"}, {"key": "ctrl+s", "mac": "cmd+s", "command": "issue.createIssueFromFile", "when": "resourceScheme == newIssue && config.files.autoSave != off"}, {"key": "ctrl+enter", "mac": "cmd+enter", "command": "issue.createIssueFromFile", "when": "resourceScheme == newIssue"}, {"key": "ctrl+k m", "mac": "cmd+k m", "command": "pr.makeSuggestion", "when": "commentEditorFocused"}], "menus": {"commandPalette": [{"command": "github.api.preloadPullRequest", "when": "false"}, {"command": "githubpr.remoteAgent", "when": "false"}, {"command": "pr.configure<PERSON><PERSON><PERSON>", "when": "gitHubOpenRepositoryCount != 0"}, {"command": "pr.configurePRViewl<PERSON>", "when": "gitHubOpenRepositoryCount != 0"}, {"command": "pr.pick", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.pickOnVscodeDev", "when": "false"}, {"command": "pr.exit", "when": "github:inReviewMode"}, {"command": "pr.dismissNotification", "when": "false"}, {"command": "pr.reset<PERSON><PERSON>wedF<PERSON>", "when": "github:inReviewMode"}, {"command": "review.openFile", "when": "false"}, {"command": "review.openLocalFile", "when": "false"}, {"command": "pr.create", "when": "gitHubOpenRepositoryCount != 0 && github:authenticated"}, {"command": "pr.pushAndCreate", "when": "false"}, {"command": "pr.merge", "when": "gitHubOpenRepositoryCount != 0 && github:inReviewMode"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "gitHubOpenRepositoryCount != 0 && github:inReviewMode"}, {"command": "pr.openPullRequestOnGitHub", "when": "gitHubOpenRepositoryCount != 0 && github:inReviewMode"}, {"command": "pr.openAllD<PERSON>s", "when": "gitHubOpenRepositoryCount != 0 && github:inReviewMode"}, {"command": "pr.refreshDescription", "when": "gitHubOpenRepositoryCount != 0 && github:inReviewMode"}, {"command": "pr.openFileOnGitHub", "when": "false"}, {"command": "pr.openOriginalFile", "when": "false"}, {"command": "pr.openModifiedFile", "when": "false"}, {"command": "pr.refreshPullRequest", "when": "false"}, {"command": "pr.deleteLocalBranch", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "when": "false"}, {"command": "pr.openDiffViewFromEditor", "when": "false"}, {"command": "pr.openDescriptionToTheSide", "when": "false"}, {"command": "pr.openDescription", "when": "gitHubOpenRepositoryCount != 0 && github:inReviewMode"}, {"command": "pr.focusDescriptionInput", "when": "github:pullRequestDescriptionVisible"}, {"command": "pr.showDiffSinceLastReview", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.closeRelatedEditors", "when": "gitHubOpenRepositoryCount != 0"}, {"command": "pr.toggleEditorCommentingOn", "when": "false"}, {"command": "pr.toggleEditorCom<PERSON>ingOff", "when": "false"}, {"command": "review.suggestDiff", "when": "false"}, {"command": "review.approve", "when": "false"}, {"command": "review.comment", "when": "false"}, {"command": "review.requestChanges", "when": "false"}, {"command": "review.approveOnDotCom", "when": "false"}, {"command": "review.requestChangesOnDotCom", "when": "false"}, {"command": "review.approveDescription", "when": "false"}, {"command": "review.commentDescription", "when": "false"}, {"command": "review.requestChangesDescription", "when": "false"}, {"command": "review.approveOnDotComDescription", "when": "false"}, {"command": "review.requestChangesOnDotComDescription", "when": "false"}, {"command": "review.createSuggestionsFromChanges", "when": "false"}, {"command": "review.createSuggestionFromChange", "when": "activeEditor == workbench.editors.textDiffEditor && (resourcePath in github:unviewedFiles || resourcePath in github:viewedFiles)"}, {"command": "pr.refreshList", "when": "gitHubOpenRepositoryCount != 0 && github:authenticated && github:hasGitHubRemotes"}, {"command": "pr.setFileListLayoutAsTree", "when": "false"}, {"command": "pr.setFileListLayoutAsFlat", "when": "false"}, {"command": "pr.refresh<PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.signin", "when": "gitHubOpenRepositoryCount != 0 && github:hasGitHubRemotes"}, {"command": "pr.signinNoEnterprise", "when": "false"}, {"command": "pr.sign<PERSON><PERSON><PERSON>", "when": "gitHubOpenRepositoryCount != 0 && github:hasGitHubRemotes"}, {"command": "pr.signinAndRefreshList", "when": "false"}, {"command": "pr.copyCommit<PERSON>ash", "when": "false"}, {"command": "pr.createComment", "when": "false"}, {"command": "pr.createSingleComment", "when": "false"}, {"command": "pr.makeSuggestion", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.editComment", "when": "false"}, {"command": "pr.cancelEditComment", "when": "false"}, {"command": "pr.saveComment", "when": "false"}, {"command": "pr.deleteComment", "when": "false"}, {"command": "pr.unresolveReviewThread", "when": "false"}, {"command": "pr.unresolveReviewThreadFromView", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON>iewThread", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.edit<PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.unmarkF<PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.checkoutByNumber", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && github:authenticated"}, {"command": "pr.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.copyVscodeDevPrLink", "when": "github:inReviewMode && remoteName != codespaces && embedderIdentifier != github.dev"}, {"command": "pr.goToNextDiffInPr", "when": "activeEditor == workbench.editors.textDiffEditor && resourcePath in github:unviewedFiles"}, {"command": "pr.goToNextDiffInPr", "when": "activeEditor == workbench.editors.textDiffEditor && resourcePath in github:viewedFiles"}, {"command": "pr.goToPreviousDiffInPr", "when": "activeEditor == workbench.editors.textDiffEditor && resourcePath in github:unviewedFiles"}, {"command": "pr.goToPreviousDiffInPr", "when": "activeEditor == workbench.editors.textDiffEditor && resourcePath in github:viewedFiles"}, {"command": "pr.copyCommentLink", "when": "false"}, {"command": "pr.addAssigneesToNewPr", "when": "false"}, {"command": "pr.addReviewersToNewPr", "when": "false"}, {"command": "pr.addLabelsToNewPr", "when": "false"}, {"command": "pr.addMilestoneToNewPr", "when": "false"}, {"command": "pr.addProjectsToNewPr", "when": "false"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.addFileComment", "when": "false"}, {"command": "review.diffWithPrHead", "when": "false"}, {"command": "review.diffLocalWithPrHead", "when": "false"}, {"command": "pr.createPrMenuCreate", "when": "false"}, {"command": "pr.createPrMenuDraft", "when": "false"}, {"command": "pr.createPrMenuMergeWhenReady", "when": "false"}, {"command": "pr.createPrMenuMerge", "when": "false"}, {"command": "pr.createPrMenuSquash", "when": "false"}, {"command": "pr.createPrMenuRebase", "when": "false"}, {"command": "pr.refreshComments", "when": "gitHubOpenRepositoryCount != 0"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "pr.accept<PERSON><PERSON>ge", "when": "isMergeResultEditor && mergeEditorBaseUri =~ /^(githubpr|gitpr):/"}, {"command": "issue.openDescription", "when": "false"}, {"command": "issue.copyGithubPermalink", "when": "github:hasGitHubRemotes"}, {"command": "issue.copyGithubHeadLink", "when": "github:hasGitHubRemotes"}, {"command": "issue.copyMarkdownGithubPermalink", "when": "github:hasGitHubRemotes"}, {"command": "issue.openGithubPermalink", "when": "github:hasGitHubRemotes"}, {"command": "issue.openIssue", "when": "false"}, {"command": "issue.copyIssueNumber", "when": "false"}, {"command": "issue.copyIssueUrl", "when": "false"}, {"command": "issue.refresh", "when": "false"}, {"command": "issue.suggest<PERSON><PERSON><PERSON>", "when": "false"}, {"command": "issue.startWorking", "when": "false"}, {"command": "issue.startWorkingBranchDescriptiveTitle", "when": "false"}, {"command": "issue.continueWorking", "when": "false"}, {"command": "issue.startWorkingBranchPrompt", "when": "false"}, {"command": "issue.stopWorking", "when": "false"}, {"command": "issue.stopWorkingBranchDescriptiveTitle", "when": "false"}, {"command": "issue.statusBar", "when": "false"}, {"command": "issue.getCurrent", "when": "false"}, {"command": "issue.edit<PERSON>uery", "when": "false"}, {"command": "issue.createIssue", "when": "github:hasGitHubRemotes && github:authenticated"}, {"command": "issue.createIssueFromFile", "when": "false"}, {"command": "issue.issueCompletion", "when": "false"}, {"command": "issue.userCompletion", "when": "false"}, {"command": "issue.signinAndRefreshList", "when": "false"}, {"command": "issue.goToLinkedCode", "when": "false"}, {"command": "issue.copyGithubDevLinkWithoutRange", "when": "false"}, {"command": "issue.copyGithubDevLinkFile", "when": "false"}, {"command": "issue.copyGithubDevLink", "when": "false"}, {"command": "issue.copyGithubPermalinkWithoutRange", "when": "false"}, {"command": "issue.copyMarkdownGithubPermalinkWithoutRange", "when": "false"}, {"command": "issue.copyGithubHeadLinkWithoutRange", "when": "false"}, {"command": "issues.configureIssuesViewlet", "when": "false"}, {"command": "pr.refreshActivePullRequest", "when": "false"}, {"command": "pr.applySuggestion", "when": "false"}, {"command": "pr.applySuggestionWithCopilot", "when": "false"}, {"command": "pr.openPullsWebsite", "when": "github:hasGitHubRemotes"}, {"command": "issues.openIssuesWebsite", "when": "github:hasGitHubRemotes"}, {"command": "issue.chatSummarizeIssue", "when": "false"}, {"command": "issue.chatSuggestFix", "when": "false"}, {"command": "notifications.sortByTimestamp", "when": "false"}, {"command": "notifications.sortByPriority", "when": "false"}, {"command": "notifications.loadMore", "when": "false"}, {"command": "notifications.refresh", "when": "false"}, {"command": "notification.openOnGitHub", "when": "false"}, {"command": "notification.markAsRead", "when": "false"}, {"command": "notification.markAsDone", "when": "false"}, {"command": "notification.chatSummarizeNotification", "when": "false"}, {"command": "notifications.markPullRequestsAsRead", "when": "false"}, {"command": "notifications.markPullRequestsAsDone", "when": "false"}, {"command": "notifications.configureNotificationsViewlet", "when": "false"}, {"command": "review.copyPrLink", "when": "github:inReviewMode"}], "view/title": [{"command": "pr.create", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == pr:github", "group": "navigation@1"}, {"command": "pr.refreshList", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == pr:github", "group": "navigation@2"}, {"command": "pr.openPullsWebsite", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == pr:github", "group": "overflow@1"}, {"command": "pr.checkoutByNumber", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == pr:github", "group": "overflow@2"}, {"command": "pr.configurePRViewl<PERSON>", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == pr:github", "group": "overflow@3"}, {"command": "pr.refresh<PERSON><PERSON><PERSON>", "when": "view == prStatus:github", "group": "navigation@2"}, {"command": "pr.setFileListLayoutAsTree", "when": "view == prStatus:github && fileListLayout:flat", "group": "navigation1"}, {"command": "pr.setFileListLayoutAsFlat", "when": "view == prStatus:github && !fileListLayout:flat", "group": "navigation1"}, {"command": "pr.toggleEditorCommentingOn", "when": "view == prStatus:github && !commentingEnabled", "group": "navigation@0"}, {"command": "pr.toggleEditorCom<PERSON>ingOff", "when": "view == prStatus:github && commentingEnabled", "group": "navigation@0"}, {"command": "issue.createIssue", "when": "view == issues:github && github:hasGitHubRemotes", "group": "navigation@1"}, {"command": "issue.refresh", "when": "view == issues:github", "group": "navigation@2"}, {"command": "issues.openIssuesWebsite", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == issues:github", "group": "overflow@1"}, {"command": "issues.configureIssuesViewlet", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == issues:github", "group": "overflow@2"}, {"command": "pr.refreshActivePullRequest", "when": "view == github:activePullRequest && github:hasGitHubRemotes", "group": "navigation@1"}, {"command": "pr.openDescription", "when": "view == github:activePullRequest && github:hasGitHubRemotes", "group": "navigation@2"}, {"command": "pr.openPullRequestOnGitHub", "when": "view == github:activePullRequest && github:hasGitHubRemotes", "group": "navigation@3"}, {"command": "pr.addAssigneesToNewPr", "when": "view == github:createPullRequestWebview && github:createPrPermissions != READ && github:createPrPermissions", "group": "navigation@1"}, {"command": "pr.addReviewersToNewPr", "when": "view == github:createPullRequestWebview && github:createPrPermissions != READ && github:createPrPermissions", "group": "navigation@2"}, {"command": "pr.addLabelsToNewPr", "when": "view == github:createPullRequestWebview && github:createPrPermissions != READ && github:createPrPermissions", "group": "navigation@3"}, {"command": "pr.addMilestoneToNewPr", "when": "view == github:createPullRequestWebview && github:createPrPermissions != READ && github:createPrPermissions", "group": "navigation@4"}, {"command": "pr.addProjectsToNewPr", "when": "view == github:createPullRequestWebview && github:createPrPermissions != READ && github:createPrPermissions", "group": "navigation@5"}, {"command": "pr.refreshComments", "when": "view == workbench.panel.comments", "group": "navigation"}, {"command": "notifications.sortByTimestamp", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == notifications:github", "group": "sortNotifications@1"}, {"command": "notifications.sortByPriority", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == notifications:github", "group": "sortNotifications@2"}, {"command": "notifications.configureNotificationsViewlet", "when": "view == notifications:github", "group": "sortNotifications@3"}, {"command": "notifications.markPullRequestsAsRead", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == notifications:github && config.githubPullRequests.experimental.notificationsMarkPullRequests == markAsRead", "group": "navigation@0"}, {"command": "notifications.markPullRequestsAsDone", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == notifications:github && config.githubPullRequests.experimental.notificationsMarkPullRequests == markAsDone", "group": "navigation@0"}, {"command": "notifications.refresh", "when": "gitHubOpenRepositoryCount != 0 && github:initialized && view == notifications:github", "group": "navigation@1"}], "view/item/context": [{"command": "pr.pick", "when": "view == pr:github && viewItem =~ /(pullrequest(:local)?:nonactive)/", "group": "1_pullrequest@1"}, {"command": "pr.exit", "when": "view == pr:github && viewItem =~ /pullrequest(:local)?:active/", "group": "1_pullrequest@1"}, {"command": "pr.pickOnVscodeDev", "when": "view == pr:github && viewItem =~ /pullrequest(:local)?:nonactive/ && (!isWeb || remoteName != codespaces && virtualWorkspace != vscode-vfs)", "group": "1_pullrequest@2"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /(pullrequest|description)/ && config.multiDiffEditor.experimental.enabled", "group": "2_pullrequest@1"}, {"command": "pr.openDescriptionToTheSide", "group": "2_pullrequest@2", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /(pullrequest|description)/"}, {"command": "pr.openPullRequestOnGitHub", "when": "view == pr:github && viewItem =~ /pullrequest/", "group": "2_pullrequest@3"}, {"command": "pr.refreshPullRequest", "when": "view == pr:github && viewItem =~ /pullrequest/", "group": "3_pullrequest@1"}, {"command": "pr.deleteLocalBranch", "when": "view == pr:github && viewItem =~ /pullrequest:local:nonactive/", "group": "4_pullrequest@4"}, {"command": "pr.dismissNotification", "when": "view == pr:github && viewItem =~ /pullrequest(.*):notification/", "group": "4_pullrequest@5"}, {"command": "issue.chatSummarizeIssue", "when": "view == pr:github && viewItem =~ /pullrequest/ && github.copilot-chat.activated && config.githubPullRequests.experimental.chat", "group": "5_pullrequest@2"}, {"command": "pr.pick", "when": "view == pr:github && viewItem =~ /(pullrequest(:local)?:nonactive)/", "group": "inline@1"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /(pullrequest|description)/ && config.multiDiffEditor.experimental.enabled", "group": "inline@0"}, {"command": "pr.showDiffSinceLastReview", "group": "inline@1", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /(pullrequest|description):(active|nonactive):hasChangesSinceReview:showingAllChanges/"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "inline@1", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /(pullrequest|description):(active|nonactive):hasChangesSinceReview:showingChangesSinceReview/"}, {"command": "notification.chatSummarizeNotification", "group": "issues_0@0", "when": "view == notifications:github && (viewItem == 'Issue' || viewItem == 'PullRequest') && config.githubPullRequests.experimental.notificationsView && config.githubPullRequests.experimental.chat"}, {"command": "notification.openOnGitHub", "group": "issues_0@1", "when": "view == notifications:github && (viewItem == 'Issue' || viewItem == 'PullRequest') && config.githubPullRequests.experimental.notificationsView"}, {"command": "notification.markAsRead", "group": "inline@3", "when": "view == notifications:github && (viewItem == 'Issue' || viewItem == 'PullRequest') && config.githubPullRequests.experimental.notificationsView"}, {"command": "notification.markAsRead", "group": "issues_0@2", "when": "view == notifications:github && (viewItem == 'Issue' || viewItem == 'PullRequest') && config.githubPullRequests.experimental.notificationsView"}, {"command": "notification.markAsDone", "group": "inline@4", "when": "view == notifications:github && (viewItem == 'Issue' || viewItem == 'PullRequest') && config.githubPullRequests.experimental.notificationsView"}, {"command": "notification.markAsDone", "group": "issues_0@3", "when": "view == notifications:github && (viewItem == 'Issue' || viewItem == 'PullRequest') && config.githubPullRequests.experimental.notificationsView"}, {"command": "pr.openPullRequestOnGitHub", "group": "inline@3", "when": "view == prStatus:github && viewItem =~ /description/ && github:activePRCount >= 2"}, {"command": "pr.copyCommit<PERSON>ash", "when": "view == prStatus:github && viewItem =~ /commit/"}, {"command": "review.openFile", "group": "inline@0", "when": "openDiffOnClick && showInlineOpenFileAction && view == prStatus:github && viewItem =~ /filechange(?!:DELETE)/"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "group": "inline@0", "when": "!openDiffOnClick && showInlineOpenFileAction && view == prStatus:github && viewItem =~ /filechange(?!:DELETE)/"}, {"command": "pr.openFileOnGitHub", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /filechange/", "group": "0_open@0"}, {"command": "pr.openOriginalFile", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /filechange:MODIFY/", "group": "0_open@1"}, {"command": "pr.openModifiedFile", "when": "view =~ /(pr|prStatus):github/ && viewItem =~ /filechange:MODIFY/", "group": "0_open@2"}, {"command": "review.diffWithPrHead", "group": "1_diff@0", "when": "openDiffOnClick && view == prStatus:github && viewItem =~ /filechange(?!:DELETE)/"}, {"command": "review.diffLocalWithPrHead", "group": "1_diff@1", "when": "openDiffOnClick && view == prStatus:github && viewItem =~ /filechange(?!:DELETE)/"}, {"command": "pr.edit<PERSON><PERSON><PERSON>", "when": "view == pr:github && viewItem == query", "group": "inline"}, {"command": "pr.edit<PERSON><PERSON><PERSON>", "when": "view == pr:github && viewItem == query"}, {"command": "issue.openIssue", "when": "view == issues:github && viewItem =~ /^(link)?(current|continue)?issue/", "group": "issues_0@1"}, {"command": "issue.goToLinkedCode", "when": "view == issues:github && viewItem =~ /^link(current|continue)?issue/", "group": "issues_0@0"}, {"command": "issue.startWorking", "when": "view == issues:github && viewItem =~ /^(link)?issue/ && config.githubIssues.useBranchForIssues != on", "group": "inline@2"}, {"command": "issue.startWorkingBranchDescriptiveTitle", "when": "view == issues:github && viewItem =~ /^(link)?issue/ && config.githubIssues.useBranchForIssues == on", "group": "inline@2"}, {"command": "issue.startWorking", "when": "view == issues:github && viewItem =~ /^(link)?continueissue/ && config.githubIssues.useBranchForIssues != on", "group": "inline@2"}, {"command": "issue.startWorkingBranchDescriptiveTitle", "when": "view == issues:github && viewItem =~ /^(link)?continueissue/ && config.githubIssues.useBranchForIssues == on", "group": "inline@2"}, {"command": "issue.startWorking", "alt": "issue.startWorkingBranchPrompt", "when": "view == issues:github && viewItem =~ /^(link)?issue/", "group": "issues_0@2"}, {"command": "issue.continueWorking", "when": "view == issues:github && viewItem =~ /^(link)?continueissue/", "group": "issues_0@2"}, {"command": "pr.create", "when": "view == issues:github && viewItem =~ /^(link)?currentissue/", "group": "issues_0@2"}, {"command": "issue.stopWorking", "when": "view == issues:github && viewItem =~ /^(link)?currentissue/", "group": "issues_0@3"}, {"command": "issue.stopWorking", "when": "view == issues:github && viewItem =~ /^(link)?currentissue/ && config.githubIssues.useBranchForIssues != on", "group": "inline@1"}, {"command": "issue.stopWorkingBranchDescriptiveTitle", "when": "view == issues:github && viewItem =~ /^(link)?currentissue/ && config.githubIssues.useBranchForIssues == on", "group": "inline@1"}, {"command": "issue.chatSummarizeIssue", "when": "view == issues:github && viewItem =~ /^(link)?(current|continue)?issue/ && github.copilot-chat.activated && config.githubPullRequests.experimental.chat", "group": "issues_1@0"}, {"command": "issue.chatSuggestFix", "when": "view == issues:github && viewItem =~ /^(link)?(current|continue)?issue/ && github.copilot-chat.activated && config.githubPullRequests.experimental.chat", "group": "issues_1@1"}, {"command": "issue.copyIssueNumber", "when": "view == issues:github && viewItem =~ /^(link)?(current|continue)?issue/", "group": "issues_2@1"}, {"command": "issue.copyIssueUrl", "when": "view == issues:github && viewItem =~ /^(link)?(current|continue)?issue/", "group": "issues_2@2"}, {"command": "issue.edit<PERSON>uery", "when": "view == issues:github && viewItem == query", "group": "inline"}, {"command": "issue.edit<PERSON>uery", "when": "view == issues:github && viewItem == query"}], "commentsView/commentThread/context": [{"command": "pr.diffOutdatedCommentWithHead", "group": "inline@0", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /outdated/"}, {"command": "pr.<PERSON><PERSON><PERSON>iewThread", "group": "inline@1", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canResolve/"}, {"command": "pr.unresolveReviewThreadFromView", "group": "inline@1", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canUnresolve/"}, {"command": "pr.diffOutdatedCommentWithHead", "group": "context@0", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /outdated/"}, {"command": "pr.<PERSON><PERSON><PERSON>iewThread", "group": "context@1", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canResolve/"}, {"command": "pr.unresolveReviewThreadFromView", "group": "context@1", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canUnresolve/"}], "editor/title": [{"command": "review.openFile", "group": "navigation", "when": "resourceScheme =~ /^review$/ && isInDiffEditor"}, {"command": "review.openLocalFile", "group": "navigation", "when": "resourceScheme =~ /^review$/ && !isInDiffEditor"}, {"command": "issue.createIssueFromFile", "group": "navigation", "when": "resourceFilename == NewIssue.md"}, {"command": "pr.<PERSON><PERSON><PERSON>", "group": "navigation", "when": "resourceScheme != pr && resourceScheme != review && resourceScheme != filechange && resourcePath in github:unviewedFiles"}, {"command": "pr.unmarkF<PERSON><PERSON><PERSON><PERSON>", "group": "navigation", "when": "resourceScheme != pr && resourceScheme != review && resourceScheme != filechange && resourcePath in github:viewedFiles"}, {"command": "pr.openDiffViewFromEditor", "group": "navigation", "when": "!isInDiffEditor && resourceScheme != pr && resourceScheme != review && resourceScheme != filechange && resourcePath in github:unviewedFiles"}, {"command": "pr.openDiffViewFromEditor", "group": "navigation", "when": "!isInDiffEditor && resourceScheme != pr && resourceScheme != review && resourceScheme != filechange && resourcePath in github:viewedFiles"}, {"command": "pr.addFileComment", "group": "navigation", "when": "(resourceScheme == pr) || (resourcePath in github:viewedFiles) || (resourcePath in github:unviewedFiles)"}], "editor/content": [{"command": "pr.accept<PERSON><PERSON>ge", "when": "isMergeResultEditor && mergeEditorBaseUri =~ /^(githubpr|gitpr):/"}], "scm/title": [{"command": "review.suggestDiff", "when": "scmProvider =~ /^git|^remoteHub:github/ && scmProviderRootUri in github:reposInReviewMode", "group": "inline"}, {"command": "pr.create", "when": "scmProvider =~ /^git|^remoteHub:github/ && scmProviderRootUri in github:reposNotInReviewMode", "group": "navigation"}], "scm/resourceGroup/context": [{"command": "review.createSuggestionsFromChanges", "when": "scmProviderRootUri in github:reposInReviewMode && scmProvider =~ /^git|^remoteHub:github/ && scmResourceGroup == workingTree", "group": "inline@-2"}], "scm/resourceState/context": [{"command": "review.createSuggestionsFromChanges", "when": "scmProviderRootUri in github:reposInReviewMode && scmProvider =~ /^git|^remoteHub:github/ && scmResourceGroup == workingTree", "group": "1_modification@5"}], "comments/commentThread/context": [{"command": "pr.createComment", "group": "inline@1", "when": "(commentController =~ /^github-browse/ && prInDraft) || (commentController =~ /^github-review/ && reviewInDraftMode)"}, {"command": "pr.createSingleComment", "group": "inline@1", "when": "config.githubPullRequests.defaultCommentType != review && ((commentController =~ /^github-browse/ && !prInDraft) || (commentController =~ /^github-review/ && !reviewInDraftMode))"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "inline@1", "when": "config.githubPullRequests.defaultCommentType == review && ((commentController =~ /^github-browse/ && !prInDraft) || (commentController =~ /^github-review/ && !reviewInDraftMode))"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "inline@2", "when": "config.githubPullRequests.defaultCommentType != review && ((commentController =~ /^github-browse/ && !prInDraft) || (commentController =~ /^github-review/ && !reviewInDraftMode))"}, {"command": "pr.createSingleComment", "group": "inline@2", "when": "config.githubPullRequests.defaultCommentType == review && ((commentController =~ /^github-browse/ && !prInDraft) || commentController =~ /^github-review/ && !reviewInDraftMode)"}], "comments/comment/editorActions": [{"command": "pr.makeSuggestion", "group": "inline@3", "when": "commentController =~ /^github-(browse|review)/ && !github:activeCommentHasSuggestion"}], "comments/commentThread/additionalActions": [{"command": "pr.<PERSON><PERSON><PERSON>iewThread", "group": "inline@1", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canResolve/"}, {"command": "pr.unresolveReviewThread", "group": "inline@1", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canUnresolve/"}, {"command": "pr.<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "inline@2", "when": "(commentController =~ /^github-browse/ && prInDraft) || (commentController =~ /^github-review/ && reviewInDraftMode)"}], "comments/commentThread/title/context": [{"command": "pr.<PERSON><PERSON><PERSON>iewThread", "group": "inline@3", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canResolve/"}, {"command": "pr.unresolveReviewThread", "group": "inline@3", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canUnresolve/"}], "comments/commentThread/comment/context": [{"command": "pr.<PERSON><PERSON><PERSON>iewThread", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canResolve/"}, {"command": "pr.unresolveReviewThread", "when": "commentController =~ /^github-(browse|review)/ && commentThread =~ /canUnresolve/"}, {"command": "pr.applySuggestion", "when": "commentController =~ /^github-review/ && comment =~ /hasSuggestion/"}, {"command": "pr.applySuggestionWithCopilot", "when": "commentController =~ /^github-review/ && !(comment =~ /hasSuggestion/)"}], "comments/comment/title": [{"command": "pr.applySuggestion", "group": "inline@0", "when": "commentController =~ /^github-review/ && comment =~ /hasSuggestion/"}, {"command": "pr.applySuggestionWithCopilot", "group": "overflow@0", "when": "commentController =~ /^github-review/ && !(comment =~ /hasSuggestion/)"}, {"command": "pr.editComment", "group": "overflow@1", "when": "commentController =~ /^github-(browse|review)/ && comment =~ /canEdit/"}, {"command": "pr.deleteComment", "group": "overflow@2", "when": "commentController =~ /^github-(browse|review)/ && comment =~ /canDelete/"}, {"command": "pr.copyCommentLink", "group": "overflow@3", "when": "commentController =~ /^github-(browse|review)/ && comment =~ /canEdit/"}], "comments/commentThread/title": [{"command": "pr.refreshComments", "group": "0_refresh@0", "when": "commentController =~ /^github-(browse|review)/"}, {"command": "pr.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>", "group": "1_collapse@0", "when": "commentController =~ /^github-(browse|review)/"}], "comments/comment/context": [{"command": "pr.saveComment", "group": "inline@1", "when": "commentController =~ /^github-(browse|review)/"}, {"command": "pr.cancelEditComment", "group": "inline@2", "when": "commentController =~ /^github-(browse|review)/"}], "editor/context/copy": [{"command": "issue.copyGithubPermalink", "when": "github:hasGitHubRemotes", "group": "3_githubPullRequests@0"}, {"command": "issue.copyMarkdownGithubPermalink", "when": "github:hasGitHubRemotes", "group": "3_githubPullRequests@1"}, {"command": "issue.copyGithubHeadLink", "when": "github:hasGitHubRemotes", "group": "3_githubPullRequests@2"}], "editor/context/share": [{"command": "issue.copyGithubPermalink", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@0"}, {"command": "issue.copyMarkdownGithubPermalink", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@1"}, {"command": "issue.copyGithubHeadLink", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@2"}, {"command": "issue.copyGithubDevLink", "when": "github:hasGitHubRemotes && remoteName == codespaces && isWeb || github:hasGitHubRemotes && embedderIdentifier == github.dev", "group": "0_vscode@0"}], "editor/context": [{"command": "review.createSuggestionFromChange", "when": "activeEditor == workbench.editors.textDiffEditor && (resourcePath in github:unviewedFiles || resourcePath in github:viewedFiles)", "group": "2_git@6"}], "file/share": [{"command": "issue.copyGithubPermalink", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@0"}, {"command": "pr.copyVscodeDevPrLink", "when": "github:hasGitHubRemotes && github:inReviewMode && remoteName != codespaces && embedderIdentifier != github.dev", "group": "1_githubPullRequests@1"}, {"command": "issue.copyMarkdownGithubPermalink", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@2"}, {"command": "issue.copyGithubHeadLink", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@3"}, {"command": "issue.copyGithubDevLinkFile", "when": "github:hasGitHubRemotes && remoteName == codespaces && isWeb || github:hasGitHubRemotes && embedderIdentifier == github.dev", "group": "0_vscode@0"}], "editor/lineNumber/context": [{"command": "issue.copyGithubPermalink", "when": "github:hasGitHubRemotes && activeEditor == workbench.editors.files.textFileEditor && config.editor.lineNumbers == on", "group": "1_cutcopypaste@3"}, {"command": "issue.copyMarkdownGithubPermalink", "when": "github:hasGitHubRemotes && activeEditor == workbench.editors.files.textFileEditor && config.editor.lineNumbers == on", "group": "1_cutcopypaste@4"}, {"command": "issue.copyGithubHeadLink", "when": "github:hasGitHubRemotes && activeEditor == workbench.editors.files.textFileEditor && config.editor.lineNumbers == on", "group": "1_cutcopypaste@5"}, {"command": "issue.copyGithubDevLink", "when": "github:hasGitHubRemotes && remoteName == codespaces && isWeb || github:hasGitHubRemotes && embedderIdentifier == github.dev", "group": "1_cutcopypaste@0"}], "editor/title/context": [{"command": "pr.closeRelatedEditors", "when": "resourceScheme == 'pr' || resourceScheme == 'review' || resourcePath in github:unviewedFiles || resourcePath in github:viewedFiles", "group": "1_close@60"}], "editor/title/context/share": [{"command": "issue.copyGithubPermalinkWithoutRange", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@10"}, {"command": "issue.copyMarkdownGithubPermalinkWithoutRange", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@11"}, {"command": "issue.copyGithubHeadLinkWithoutRange", "when": "github:hasGitHubRemotes", "group": "1_githubPullRequests@12"}, {"command": "issue.copyGithubDevLinkWithoutRange", "when": "github:hasGitHubRemotes && remoteName == codespaces && isWeb || github:hasGitHubRemotes && embedderIdentifier == github.dev", "group": "0_vscode@0"}], "explorer/context/share": [{"command": "issue.copyGithubPermalinkWithoutRange", "when": "github:hasGitHubRemotes", "group": "5_githubPulLRequests@10"}, {"command": "issue.copyMarkdownGithubPermalinkWithoutRange", "when": "github:hasGitHubRemotes", "group": "5_githubPulLRequests@11"}, {"command": "issue.copyGithubHeadLinkWithoutRange", "when": "github:hasGitHubRemotes", "group": "5_githubPulLRequests@12"}, {"command": "issue.copyGithubDevLinkWithoutRange", "when": "github:hasGitHubRemotes && remoteName == codespaces && isWeb || github:hasGitHubRemotes && embedderIdentifier == github.dev", "group": "0_vscode@0"}], "menuBar/edit/copy": [{"command": "issue.copyGithubPermalink", "when": "github:hasGitHubRemotes"}, {"command": "issue.copyMarkdownGithubPermalink", "when": "github:hasGitHubRemotes"}], "remoteHub/pullRequest": [{"command": "pr.create", "when": "scmProvider =~ /^remoteHub:github/", "group": "1_modification@0"}], "webview/context": [{"command": "pr.createPrMenuCreate", "when": "webviewId == 'github:createPullRequestWebview' && github:createPrMenu", "group": "0_create@0"}, {"command": "pr.createPrMenuDraft", "when": "webviewId == 'github:createPullRequestWebview' && github:createPrMenu && github:createPrMenuDraft", "group": "0_create@1"}, {"command": "pr.createPrMenuMergeWhenReady", "when": "webviewId == 'github:createPullRequestWebview' && github:createPrMenu && github:createPrMenuMergeWhenReady", "group": "1_create@0"}, {"command": "pr.createPrMenuMerge", "when": "webviewId == 'github:createPullRequestWebview' && github:createPrMenu && github:createPrMenuMerge", "group": "1_create@0"}, {"command": "pr.createPrMenuSquash", "when": "webviewId == 'github:createPullRequestWebview' && github:createPrMenu && github:createPrMenuSquash", "group": "1_create@1"}, {"command": "pr.createPrMenuRebase", "when": "webviewId == 'github:createPullRequestWebview' && github:createPrMenu && github:createPrMenuRebase", "group": "1_create@2"}, {"command": "review.approve", "when": "webviewId == 'github:activePullRequest' && github:reviewCommentMenu && github:reviewCommentApprove"}, {"command": "review.comment", "when": "webviewId == 'github:activePullRequest' && github:reviewCommentMenu && github:reviewCommentComment"}, {"command": "review.requestChanges", "when": "webviewId == 'github:activePullRequest' && github:reviewCommentMenu && github:reviewCommentRequestChanges"}, {"command": "review.approveOnDotCom", "when": "webviewId == 'github:activePullRequest' && github:reviewCommentMenu && github:reviewCommentApproveOnDotCom"}, {"command": "review.requestChangesOnDotCom", "when": "webviewId == 'github:activePullRequest' && github:reviewCommentMenu && github:reviewCommentRequestChangesOnDotCom"}, {"command": "review.approveDescription", "when": "webviewId == PullRequestOverview && github:reviewCommentMenu && github:reviewCommentApprove"}, {"command": "review.commentDescription", "when": "webviewId == PullRequestOverview && github:reviewCommentMenu && github:reviewCommentComment"}, {"command": "review.requestChangesDescription", "when": "webviewId == PullRequestOverview && github:reviewCommentMenu && github:reviewCommentRequestChanges"}, {"command": "review.approveOnDotComDescription", "when": "webviewId == PullRequestOverview && github:reviewCommentMenu && github:reviewCommentApproveOnDotCom"}, {"command": "review.requestChangesOnDotComDescription", "when": "webviewId == PullRequestOverview && github:reviewCommentMenu && github:reviewCommentRequestChangesOnDotCom"}]}, "colors": [{"id": "issues.newIssueDecoration", "defaults": {"dark": "#ffffff48", "light": "#00000048", "highContrast": "editor.foreground", "highContrastLight": "editor.foreground"}, "description": "The color used for the assignees and labels fields in a new issue editor."}, {"id": "issues.open", "defaults": {"dark": "#3FB950", "light": "#3FB950", "highContrast": "editor.foreground", "highContrastLight": "editor.foreground"}, "description": "The color used for indicating that an issue is open."}, {"id": "issues.closed", "defaults": {"dark": "#cb2431", "light": "#cb2431", "highContrast": "editor.foreground", "highContrastLight": "editor.foreground"}, "description": "The color used for indicating that an issue is closed."}, {"id": "pullRequests.merged", "defaults": {"dark": "#8957e5", "light": "#8957e5", "highContrast": "editor.background", "highContrastLight": "editor.background"}, "description": "The color used for indicating that a pull request is merged."}, {"id": "pullRequests.draft", "defaults": {"dark": "#6e7681", "light": "#6e7681", "highContrast": "editor.background", "highContrastLight": "editor.background"}, "description": "The color used for indicating that a pull request is a draft."}, {"id": "pullRequests.open", "defaults": {"dark": "issues.open", "light": "issues.open", "highContrast": "editor.background", "highContrastLight": "editor.background"}, "description": "The color used for indicating that a pull request is open."}, {"id": "pullRequests.closed", "defaults": {"dark": "issues.closed", "light": "issues.closed", "highContrast": "editor.background", "highContrastLight": "editor.background"}, "description": "The color used for indicating that a pull request is closed."}, {"id": "pullRequests.notification", "defaults": {"dark": "notificationsInfoIcon.foreground", "light": "notificationsInfoIcon.foreground", "highContrast": "editor.foreground", "highContrastLight": "editor.foreground"}, "description": "The color used for indicating a notification on a pull request"}], "resourceLabelFormatters": [{"scheme": "review", "formatting": {"label": "${path}", "separator": "/", "workspaceSuffix": "GitHub", "stripPathStartingSeparator": true}}], "languageModelTools": [{"name": "github-pull-request_copilot-coding-agent", "displayName": "Copilot coding agent", "modelDescription": "Completes the provided task using an asynchronous coding agent. Use when the user wants copilot continue completing a task in the background or asynchronously. IMPORTANT: Use this tool LAST/FINAL when users mention '#github-pull-request_copilot-coding-agent' in their query. This indicates they want the task/job implemented by the remote coding agent after all other analysis, planning, and preparation is complete. Call this tool at the END to hand off the fully-scoped task to the asynchronous GitHub Copilot coding agent. The agent will create a new branch, implement the changes, and open a pull request. Always use this tool as the final step when the hashtag is mentioned, after completing any other necessary tools or analysis first.", "when": "config.githubPullRequests.codingAgent.enabled", "icon": "resources/icons/copilot.svg", "canBeReferencedInPrompt": true, "toolReferenceName": "copilotCodingAgent", "userDescription": "Completes the provided task using an asynchronous coding agent. Use when the user wants copilot continue completing a task in the background or asynchronously. Launch an autonomous GitHub Copilot agent to work on coding tasks in the background. The agent will create a new branch, implement the requested changes, and open a pull request with the completed work.", "inputSchema": {"type": "object", "required": ["title", "body"], "properties": {"title": {"type": "string", "description": "The title of the issue. Populate from chat context."}, "body": {"type": "string", "description": "The body/description of the issue. Populate from chat context."}, "existingPullRequest": {"type": "number", "description": "The number of an existing pull request related to the current coding agent task. Look in the chat history for this number.  In the chat it may look like 'Coding agent will continue work in #17...'. In this example, you should return '17'."}}}}, {"name": "github-pull-request_issue_fetch", "tags": ["github", "issues", "prs"], "toolReferenceName": "issue_fetch", "displayName": "Get a GitHub Issue or PR", "modelDescription": "Get a GitHub issue/PR's details as a JSON object.", "icon": "$(info)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"repo": {"type": "object", "description": "The repository to get the issue/PR from.", "properties": {"owner": {"type": "string", "description": "The owner of the repository to get the issue/PR from."}, "name": {"type": "string", "description": "The name of the repository to get the issue/PR from."}}, "required": ["owner", "name"]}, "issueNumber": {"type": "number", "description": "The number of the issue/PR to get."}}, "required": ["issueNumber"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_notification_fetch", "tags": ["github", "notification"], "toolReferenceName": "notification_fetch", "displayName": "Get a GitHub Notification", "modelDescription": "Get a GitHub notification's details as a JSON object.", "icon": "$(info)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"thread_id": {"type": "string", "description": "The notification thread id."}}, "required": ["thread_id"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_issue_summarize", "tags": ["github", "issues", "prs"], "toolReferenceName": "issue_summarize", "displayName": "Summarize a GitHub Issue or PR", "modelDescription": "Summarizes a GitHub issue or pull request. A summary is a great way to describe an issue or pull request.", "icon": "$(info)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the issue/PR"}, "body": {"type": "string", "description": "The body of the issue/PR"}, "owner": {"type": "string", "description": "The owner of the repo in which the issue/PR is located"}, "repo": {"type": "string", "description": "The repo in which the issue/PR is located"}, "comments": {"type": "array", "items": {"type": "object", "properties": {"body": {"type": "string", "description": "The comment body"}, "author": {"type": "string", "description": "The author of the comment"}}}, "description": "The array of associated string comments"}, "fileChanges": {"type": "array", "items": {"type": "object", "properties": {"fileName": {"type": "string", "description": "The name of the file of the change"}, "patch": {"type": "string", "description": "The patch of the change"}}}, "description": "For a PR, the array of associated file changes"}}, "required": ["title", "body", "comments", "owner", "repo"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_notification_summarize", "tags": ["github", "notification"], "toolReferenceName": "notification_summarize", "displayName": "Summarize a GitHub Notification", "modelDescription": "Summarizes a GitHub notification. A summary is a great way to describe a notification.", "icon": "$(info)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"lastReadAt": {"type": "string", "description": "The last read time of the notification."}, "lastUpdatedAt": {"type": "string", "description": "The last updated time of the notification."}, "unread": {"type": "boolean", "description": "Whether the notification is unread."}, "title": {"type": "string", "description": "The title of the notification issue/PR"}, "body": {"type": "string", "description": "The body of the notification issue/PR"}, "owner": {"type": "string", "description": "The owner of the repo in which the issue/PR is located"}, "repo": {"type": "string", "description": "The repo in which the issue/PR is located"}, "comments": {"type": "array", "items": {"type": "object", "properties": {"body": {"type": "string", "description": "The comment body"}, "author": {"type": "string", "description": "The author of the comment"}}}, "description": "The array of unread comments under the issue/PR of the notification"}, "threadId": {"type": "number", "description": "The thread id of the notification"}, "notificationKey": {"type": "string", "description": "The key of the notification"}, "itemNumber": {"type": "string", "description": "The number of the issue/PR in the notification"}, "itemType": {"type": "string", "description": "The type of the item in the notification - whether it is an issue or a PR"}, "fileChanges": {"type": "array", "items": {"type": "object", "properties": {"fileName": {"type": "string", "description": "The name of the file of the change"}, "patch": {"type": "string", "description": "The patch of the change"}}, "required": ["fileName", "patch"]}, "description": "For a notification about a PR, the array of associated file changes"}}, "required": ["title", "comments", "lastUpdatedAt", "unread", "threadId", "notificationKey", "owner", "repo", "itemNumber", "itemType"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_suggest-fix", "tags": ["github", "issues"], "toolReferenceName": "suggest-fix", "displayName": "Suggest a Fix for a GitHub Issue", "modelDescription": "Summarize and suggest a fix for a GitHub issue.", "icon": "$(info)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"repo": {"type": "object", "description": "The repository to get the issue from.", "properties": {"owner": {"type": "string", "description": "The owner of the repository to get the issue from."}, "name": {"type": "string", "description": "The name of the repository to get the issue from."}}, "required": ["owner", "name"]}, "issueNumber": {"type": "number", "description": "The number of the issue to get."}}, "required": ["issueNumber", "repo"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_formSearchQuery", "tags": ["github", "issues", "search", "query", "natural language"], "toolReferenceName": "searchSyntax", "displayName": "Convert natural language to a GitHub search query", "modelDescription": "Converts natural language to a GitHub search query. Should ALWAYS be called before doing a search.", "icon": "$(search)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"repo": {"type": "object", "description": "The repository to get the issue from.", "properties": {"owner": {"type": "string", "description": "The owner of the repository to get the issue from."}, "name": {"type": "string", "description": "The name of the repository to get the issue from."}}, "required": ["owner", "name"]}, "naturalLanguageString": {"type": "string", "description": "A plain text description of what the search should be."}}, "required": ["naturalLanguageString"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_doSearch", "tags": ["github", "issues", "search"], "toolReferenceName": "doSearch", "displayName": "Execute a GitHub search", "modelDescription": "Execute a GitHub search given a well formed GitHub search query. Call github-pull-request_formSearchQuery first to get good search syntax and pass the exact result in as the 'query'.", "icon": "$(search)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"repo": {"type": "object", "description": "The repository to get the issue from.", "properties": {"owner": {"type": "string", "description": "The owner of the repository to get the issue from."}, "name": {"type": "string", "description": "The name of the repository to get the issue from."}}, "required": ["owner", "name"]}, "query": {"type": "string", "description": "A well formed GitHub search query using proper GitHub search syntax."}}, "required": ["query", "repo"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_renderIssues", "tags": ["github", "issues", "render", "display"], "toolReferenceName": "renderIssues", "displayName": "Render issue items in a markdown table", "modelDescription": "Render issue items from an issue search in a markdown table. The markdown table will be displayed directly to the user by the tool. No further display should be done after this!", "icon": "$(paintcan)", "canBeReferencedInPrompt": false, "inputSchema": {"type": "object", "properties": {"arrayOfIssues": {"type": "array", "description": "An array of GitHub Issues.", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the issue."}, "number": {"type": "number", "description": "The number of the issue."}, "url": {"type": "string", "description": "The URL of the issue."}, "state": {"type": "string", "description": "The state of the issue (open/closed)."}, "createdAt": {"type": "string", "description": "The creation date of the issue."}, "updatedAt": {"type": "string", "description": "The last update date of the issue."}, "closedAt": {"type": "string", "description": "The closing date of the issue."}, "author": {"type": "object", "description": "The author of the issue.", "properties": {"login": {"type": "string", "description": "The login of the author."}, "url": {"type": "string", "description": "The URL of the author's profile."}}}, "labels": {"type": "array", "description": "The labels associated with the issue.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the label."}, "color": {"type": "string", "description": "The color of the label."}}}}, "assignees": {"type": "array", "description": "The assignees of the issue.", "items": {"type": "object", "properties": {"login": {"type": "string", "description": "The login of the assignee."}, "url": {"type": "string", "description": "The URL of the assignee's profile."}}}}, "commentsCount": {"type": "number", "description": "The number of comments on the issue."}}}, "required": ["title", "number", "url", "state", "createdAt", "author", "commentCount", "reactionCount"]}, "totalIssues": {"type": "number", "description": "The total number of issues in the search."}}, "required": ["arrayOfIssues", "totalIssues"]}, "when": "config.githubPullRequests.experimental.chat"}, {"name": "github-pull-request_activePullRequest", "tags": ["github", "pull request"], "toolReferenceName": "activePullRequest", "displayName": "Active Pull Request", "modelDescription": "Get comprehensive information about the active or current GitHub pull request (PR). This includes the PR title, full description, list of changed files, review comments, PR state, and status checks/CI results. For PRs created by Copilot, it also includes the session logs which indicate the development process and decisions made by the coding agent. When asked about the active or current pull request, do this first! Use this tool for any request related to \"current changes,\" \"pull request details,\" \"what changed,\" \"PR status,\" or similar queries even if the user does not explicitly mention \"pull request.\"", "icon": "$(git-pull-request)", "canBeReferencedInPrompt": true, "userDescription": "Get information about the active GitHub pull request. This information includes: comments, files changed, pull request title + description, pull request state, and pull request status checks/CI.", "when": "config.githubPullRequests.experimental.chat"}]}, "scripts": {"postinstall": "yarn update-dts", "bundle": "webpack --mode production --env esbuild", "bundle:node": "webpack --mode production --config-name extension:node --config-name webviews", "bundle:web": "webpack --mode production --config-name extension:webworker --config-name webviews", "clean": "rm -r dist/", "compile": "webpack --mode development --env esbuild", "compile:test": "tsc -p tsconfig.test.json", "compile:node": "webpack --mode development --config-name extension:node --config-name webviews", "compile:web": "webpack --mode development --config-name extension:webworker --config-name webviews", "lint": "eslint --fix --cache --config .eslintrc.json --ignore-pattern src/env/browser/**/* \"{src,webviews}/**/*.{ts,tsx}\"", "lint:browser": "eslint --fix --cache --cache-location .eslintcache.browser --config .eslintrc.browser.json --ignore-pattern src/env/node/**/* \"{src,webviews}/**/*.{ts,tsx}\"", "package": "npx vsce package --yarn", "test": "yarn run test:preprocess && node ./out/src/test/runTests.js", "test:preprocess": "yarn run compile:test && yarn run test:preprocess-gql && yarn run test:preprocess-svg && yarn run test:preprocess-fixtures", "browsertest:preprocess": "tsc ./src/test/browser/runTests.ts --outDir ./dist/browser/test --rootDir ./src/test/browser --target es6 --module commonjs", "browsertest": "yarn run browsertest:preprocess && node ./dist/browser/test/runTests.js", "test:preprocess-gql": "node scripts/preprocess-gql --in src/github/queries.gql --out out/src/github/queries.gql && node scripts/preprocess-gql --in src/github/queriesExtra.gql --out out/src/github/queriesExtra.gql && node scripts/preprocess-gql --in src/github/queriesShared.gql --out out/src/github/queriesShared.gql && node scripts/preprocess-gql --in src/github/queriesLimited.gql --out out/src/github/queriesLimited.gql", "test:preprocess-svg": "node scripts/preprocess-svg --in ../resources/ --out out/resources", "test:preprocess-fixtures": "node scripts/preprocess-fixtures --in src --out out", "update-dts": "cd \"src/@types\" && npx vscode-dts main && npx vscode-dts dev", "watch": "webpack --watch --mode development --env esbuild", "watch:web": "webpack --watch --mode development --config-name extension:webworker --config-name webviews", "hygiene": "node ./build/hygiene.js", "prepare": "husky install"}, "devDependencies": {"@shikijs/monaco": "^3.7.0", "@types/chai": "^4.1.4", "@types/glob": "7.1.3", "@types/lru-cache": "^5.1.0", "@types/marked": "^0.7.2", "@types/mocha": "^8.2.2", "@types/node": "18.17.1", "@types/react": "^16.8.4", "@types/react-dom": "^16.8.2", "@types/sinon": "7.0.11", "@types/temp": "0.8.34", "@types/vscode": "1.89.0", "@types/webpack-env": "^1.16.0", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "@vscode/test-electron": "^2.3.8", "@vscode/test-web": "^0.0.29", "assert": "^2.0.0", "buffer": "^6.0.3", "constants-browserify": "^1.0.0", "crypto-browserify": "3.12.0", "css-loader": "5.1.3", "esbuild-loader": "4.2.2", "eslint": "7.22.0", "eslint-cli": "1.1.1", "eslint-plugin-import": "2.22.1", "event-stream": "^4.0.1", "fork-ts-checker-webpack-plugin": "6.1.1", "glob": "7.1.6", "graphql": "15.5.0", "graphql-tag": "2.11.0", "gulp-filter": "^7.0.0", "husky": "^8.0.1", "jsdom": "19.0.0", "jsdom-global": "3.0.2", "json5": "2.2.2", "merge-options": "3.0.4", "minimist": "^1.2.6", "mkdirp": "1.0.4", "mocha": "^9.0.1", "mocha-junit-reporter": "1.23.0", "mocha-multi-reporters": "1.1.7", "monaco-editor-webpack-plugin": "^7.1.0", "os-browserify": "^0.3.0", "p-all": "^1.0.0", "path-browserify": "1.0.1", "process": "^0.11.10", "raw-loader": "4.0.2", "react-testing-library": "7.0.1", "sinon": "9.0.0", "source-map-support": "0.5.19", "stream-browserify": "^3.0.0", "style-loader": "2.0.0", "svg-inline-loader": "^0.8.2", "temp": "0.9.4", "terser-webpack-plugin": "5.1.1", "timers-browserify": "^2.0.12", "ts-loader": "8.0.18", "tty": "1.0.1", "typescript": "4.5.5", "typescript-formatter": "^7.2.2", "vinyl-fs": "^3.0.3", "webpack": "5.94.0", "webpack-cli": "4.2.0"}, "dependencies": {"@octokit/rest": "20.1.2", "@octokit/types": "13.8.0", "@vscode/codicons": "^0.0.36", "@vscode/extension-telemetry": "0.7.5", "@vscode/prompt-tsx": "^0.3.0-alpha.12", "apollo-boost": "^0.4.9", "apollo-link-context": "1.0.20", "cockatiel": "^3.1.1", "cross-fetch": "3.1.5", "dayjs": "1.10.4", "debounce": "^1.2.1", "events": "3.2.0", "fast-deep-equal": "^3.1.3", "jsonc-parser": "^3.3.1", "jszip": "^3.10.1", "lru-cache": "6.0.0", "markdown-it": "^14.1.0", "marked": "^4.0.10", "monaco-editor": "^0.52.2", "react": "^16.12.0", "react-dom": "^16.12.0", "shiki": "^3.7.0", "ssh-config": "4.1.1", "stream-http": "^3.2.0", "temporal-polyfill": "^0.3.0", "tunnel": "0.0.6", "url-search-params-polyfill": "^8.1.1", "uuid": "8.3.2", "vscode-tas-client": "^0.1.84", "vsls": "^0.3.967"}, "license": "MIT"}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.114.3", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "GitHub", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1754240328132, "pinned": false, "source": "gallery", "id": "69ddd764-339a-4ecc-97c1-9c4ece58e36d", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 33406241}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "github.copilot-chat", "uuid": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f"}, "manifest": {"name": "copilot-chat", "displayName": "GitHub Copilot Chat", "description": "AI chat features powered by Copilot", "version": "0.29.1", "build": "1", "internalAIKey": "1058ec22-3c95-4951-8443-f26c1f325911", "internalLargeStorageAriaKey": "ec712b3202c5462fb6877acae7f1f9d7-c19ad55e-3e3c-4f99-984b-827f6d95bd9e-6917", "ariaKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "buildType": "prod", "publisher": "GitHub", "homepage": "https://github.com/features/copilot?editor=vscode", "license": "SEE LICENSE IN LICENSE.txt", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-copilot-chat"}, "bugs": {"url": "https://github.com/microsoft/vscode/issues"}, "qna": "https://github.com/github-community/community/discussions/categories/copilot", "icon": "assets/copilot.png", "pricing": "Trial", "engines": {"vscode": "^1.102.0-20250627", "npm": ">=9.0.0", "node": ">=22.14.0"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Machine Learning"], "keywords": ["ai", "openai", "codex", "pilot", "snippets", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "c#", "java", "kotlin", "co-pilot"], "badges": [{"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange", "href": "https://github.com/github-copilot/signup?editor=vscode", "description": "Sign up for GitHub Copilot"}, {"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social", "href": "https://github.com/github/copilot-docs", "description": "Star Copilot on GitHub"}, {"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social", "href": "https://www.youtube.com/@GitHub/search?query=copilot", "description": "Check out GitHub on Youtube"}, {"url": "https://img.shields.io/twitter/follow/github?style=social", "href": "https://twitter.com/github", "description": "Follow GitHub on Twitter"}], "activationEvents": ["onStartupFinished", "onLanguageModelChat:copilot", "onUri", "onFileSystem:ccreq", "onFileSystem:ccsettings"], "main": "./dist/extension", "l10n": "./l10n", "enabledApiProposals": ["extensionsAny", "newSymbolNamesProvider", "interactive", "codeActionAI", "activeComment", "commentReveal", "contribCommentThreadAdditionalMenu", "contribCommentsViewThreadMenus", "documentFiltersExclusive", "embeddings", "findTextInFiles", "findTextInFiles2", "findFiles2", "textSearchProvider", "terminalDataWriteEvent", "terminalExecuteCommandEvent", "terminalSelection", "terminalQuickFixProvider", "mappedEditsProvider", "aiRelatedInformation", "chatParticipantAdditions", "chatEditing", "defaultChatParticipant", "contribSourceControlInputBoxMenu", "authLearnMore", "testObserver", "aiTextSearchProvider", "chatParticipantPrivate", "chat<PERSON>rovider", "contribDebugCreateConfiguration", "chatReferenceDiagnostic", "textSearchProvider2", "chatReferenceBinaryData", "languageModelSystem", "languageModelCapabilities", "inlineCompletionsAdditions", "languageModelDataPart", "chatStatusItem", "taskProblemMatcherStatus", "contribLanguageModelToolSets", "textDocumentChangeReason", "resolvers"], "contributes": {"languageModelTools": [{"name": "copilot_searchCodebase", "toolReferenceName": "codebase", "displayName": "Codebase", "icon": "$(folder)", "canBeReferencedInPrompt": true, "userDescription": "Find relevant file chunks, symbols, and other information in your codebase", "modelDescription": "Run a natural language search for relevant code or documentation comments from the user's current workspace. Returns relevant code snippets from the user's current workspace if it is large, or the full contents of the workspace if it is small.", "tags": ["codesearch", "vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to search the codebase for. Should contain all relevant context. Should ideally be text that might appear in the codebase, such as function names, variable names, or comments."}}, "required": ["query"]}}, {"name": "copilot_searchWorkspaceSymbols", "toolReferenceName": "symbols", "displayName": "Workspace Symbols", "icon": "$(symbol)", "userDescription": "Search for workspace symbols using language services.", "modelDescription": "Search the user's workspace for code symbols using language services. Use this tool when the user is looking for a specific symbol in their workspace.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"symbolName": {"type": "string", "description": "The symbol to search for, such as a function name, class name, or variable name."}}, "required": ["symbolName"]}}, {"name": "copilot_listCodeUsages", "toolReferenceName": "usages", "displayName": "Find Usages", "icon": "$(references)", "canBeReferencedInPrompt": true, "userDescription": "Find references, definitions, and other usages of a symbol", "modelDescription": "Request to list all usages (references, definitions, implementations etc) of a function, class, method, variable etc. Use this tool when \n1. Looking for a sample implementation of an interface or class\n2. Checking how a function is used throughout the codebase.\n3. Including and updating all usages when changing a function, method, or constructor", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"symbolName": {"type": "string", "description": "The name of the symbol, such as a function name, class name, method name, variable name, etc."}, "filePaths": {"type": "array", "description": "One or more file paths which likely contain the definition of the symbol. For instance the file which declares a class or function. This is optional but will speed up the invocation of this tool and improve the quality of its output.", "items": {"type": "string"}}}, "required": ["symbolName"]}}, {"name": "copilot_getVSCodeAPI", "toolReferenceName": "vscodeAPI", "displayName": "Get VS Code API References", "icon": "$(references)", "userDescription": "Use VS Code API references to answer questions about VS Code extension development.", "modelDescription": "Get relevant VS Code API references to answer questions about VS Code extension development. Use this tool when the user asks about VS Code APIs, capabilities, or best practices related to developing VS Code extensions. Use it in all VS Code extension development workspaces.", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to search vscode documentation for. Should contain all relevant context."}}, "required": ["query"]}, "tags": [], "canBeReferencedInPrompt": true}, {"displayName": "Think", "name": "copilot_think", "tags": [], "when": "config.github.copilot.chat.agent.thinkingTool", "modelDescription": "Use this tool to think deeply about the user's request and organize your thoughts. This tool helps improve response quality by allowing the model to consider the request carefully, brainstorm solutions, and plan complex tasks. It's particularly useful for:\n\n1. Exploring repository issues and brainstorming bug fixes\n2. Analyzing test results and planning fixes\n3. Planning complex refactoring approaches\n4. Designing new features and architecture\n5. Organizing debugging hypotheses\n\nThe tool logs your thought process for transparency but doesn't execute any code or make changes.", "inputSchema": {"type": "object", "properties": {"thoughts": {"type": "string", "description": "Your thoughts about the current task or problem. This should be a clear, structured explanation of your reasoning, analysis, or planning process."}}, "required": ["thoughts"]}}, {"name": "copilot_findFiles", "toolReferenceName": "fileSearch", "displayName": "Find Files", "modelDescription": "Search for files in the workspace by glob pattern. This only returns the paths of matching files. Use this tool when you know the exact filename pattern of the files you're searching for. Glob patterns match from the root of the workspace folder. Examples:\n- **/*.{js,ts} to match all js/ts files in the workspace.\n- src/** to match all files under the top-level src folder.\n- **/foo/**/*.js to match all js files under any foo folder in the workspace.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search for files with names or paths matching this glob pattern."}, "maxResults": {"type": "number", "description": "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults."}}, "required": ["query"]}}, {"name": "copilot_findTextInFiles", "toolReferenceName": "textSearch", "displayName": "Find Text In Files", "modelDescription": "Do a fast text search in the workspace. Use this tool when you want to search with an exact string or regex. If you are not sure what words will appear in the workspace, prefer using regex patterns with alternation (|) or character classes to search for multiple potential words at once instead of making separate searches. For example, use 'function|method|procedure' to look for all of those words at once. Use includePattern to search within files matching a specific pattern, or in a specific file, using a relative path. Use this tool when you want to see an overview of a particular file, instead of using read_file many times to look for code within a file.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The pattern to search for in files in the workspace. Use regex with alternation (e.g., 'word1|word2|word3') or character classes to find multiple potential words in a single search. Be sure to set the isRegexp property properly to declare whether it's a regex or plain text pattern. Is case-insensitive."}, "isRegexp": {"type": "boolean", "description": "Whether the pattern is a regex."}, "includePattern": {"type": "string", "description": "Search files matching this glob pattern. Will be applied to the relative path of files within the workspace. To search recursively inside a folder, use a proper glob pattern like \"src/folder/**\". Do not use | in includePattern."}, "maxResults": {"type": "number", "description": "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults."}}, "required": ["query", "isRegexp"]}}, {"name": "copilot_applyPatch", "displayName": "Apply Patch", "toolReferenceName": "applyPatch", "userDescription": "Edit text files in the workspace", "modelDescription": "Edit text files. Do not use this tool to edit Jupyter notebooks. `apply_patch` allows you to execute a diff/patch against a text file, but the format of the diff specification is unique to this task, so pay careful attention to these instructions. To use the `apply_patch` command, you should pass a message of the following structure as \"input\":\n\n*** Begin Patch\n[YOUR_PATCH]\n*** End Patch\n\nWhere [YOUR_PATCH] is the actual content of your patch, specified in the following V4A diff format.\n\n*** [ACTION] File: [/absolute/path/to/file] -> ACTION can be one of Add, Update, or Delete.\nAn example of a message that you might pass as \"input\" to this function, in order to apply a patch, is shown below.\n\n*** Begin Patch\n*** Update File: /Users/<USER>/pygorithm/searching/binary_search.py\n@@class BaseClass\n@@    def search():\n-        pass\n+        raise NotImplementedError()\n\n@@class Subclass\n@@    def search():\n-        pass\n+        raise NotImplementedError()\n\n*** End Patch\nDo not use line numbers in this diff format.", "inputSchema": {"type": "object", "properties": {"input": {"type": "string", "description": "The edit patch to apply."}, "explanation": {"type": "string", "description": "A short description of what the tool call is aiming to achieve."}}, "required": ["input", "explanation"]}}, {"name": "copilot_readFile", "toolReferenceName": "readFile", "displayName": "Read File", "modelDescription": "Read the contents of a file.\n\nYou must specify the line range you're interested in. Line numbers are 1-indexed. If the file contents returned are insufficient for your task, you may call this tool again to retrieve more content. Prefer reading larger ranges over doing many small reads.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"filePath": {"description": "The absolute path of the file to read.", "type": "string"}, "startLine": {"type": "number", "description": "The line number to start reading from, 1-based."}, "endLine": {"type": "number", "description": "The inclusive line number to end reading at, 1-based."}}, "required": ["filePath", "startLine", "endLine"]}}, {"name": "copilot_listDirectory", "toolReferenceName": "listDirectory", "displayName": "List Dir", "modelDescription": "List the contents of a directory. Result will have the name of the child. If the name ends in /, it's a folder, otherwise a file", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"path": {"type": "string", "description": "The absolute path to the directory to list."}}, "required": ["path"]}}, {"name": "copilot_runInTerminal", "toolReferenceName": "runInTerminal", "displayName": "Run In Terminal", "modelDescription": "This tool allows you to execute shell commands in a persistent terminal session, preserving environment variables, working directory, and other context across multiple commands.\n\nCommand Execution:\n- Supports multi-line commands \n\nDirectory Management:\n- Must use absolute paths to avoid navigation issues.\n\nProgram Execution:\n- Supports Python, Node.js, and other executables.\n- Install dependencies via pip, npm, etc.\n\nBackground Processes:\n- For long-running tasks (e.g., servers), set isBackground=true.\n- Returns a terminal ID for checking status and runtime later.\n\nOutput Management:\n- Output is automatically truncated if longer than 60KB to prevent context overflow\n- Use filters like 'head', 'tail', 'grep' to limit output size\n- For pager commands, disable paging: use 'git --no-pager' or add '| cat'\n\nBest Practices:\n- Be specific with commands to avoid excessive output\n- Use targeted queries instead of broad scans\n- Consider using 'wc -l' to count before listing many items", "tags": [], "inputSchema": {"type": "object", "properties": {"command": {"type": "string", "description": "The command to run in the terminal."}, "explanation": {"type": "string", "description": "A one-sentence description of what the command does. This will be shown to the user before the command is run."}, "isBackground": {"type": "boolean", "description": "Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of background processes: building in watch mode, starting a server. You can check the output of a background process later on by using copilot_getTerminalOutput."}}, "required": ["command", "explanation", "isBackground"]}}, {"name": "copilot_getTerminalOutput", "toolReferenceName": "getTerminalOutput", "displayName": "Get Terminal Output", "modelDescription": "Get the output of a terminal command previously started with runInTerminal", "tags": [], "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the terminal command output to check."}}, "required": ["id"]}}, {"name": "copilot_getTaskOutput", "toolReferenceName": "getTaskOutput", "displayName": "Get Task Output", "modelDescription": "Retrieves the output of a running VS Code task.\n- Use this tool when the user is trying to understand the current project state, debug issues, or analyze task-related errors, output, or status.", "tags": [], "inputSchema": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The workspace folder path containing the task"}, "id": {"type": "string", "description": "The task ID to run."}, "maxCharsToRetrieve": {"type": "number", "description": "The maximum number of characters to retrieve from the terminal output."}}, "required": ["id", "workspaceFolder"]}}, {"name": "copilot_getErrors", "displayName": "Get Problems", "toolReferenceName": "problems", "canBeReferencedInPrompt": true, "icon": "$(error)", "userDescription": "Check errors for a particular file", "modelDescription": "Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePaths": {"description": "The absolute paths to the files to check for errors.", "type": "array", "items": {"type": "string"}}}, "required": ["filePaths"]}}, {"name": "copilot_readProjectStructure", "displayName": "Project Structure", "modelDescription": "Get a file tree representation of the workspace.", "tags": []}, {"name": "copilot_getChangedFiles", "displayName": "Git Changes", "toolReferenceName": "changes", "icon": "$(diff)", "canBeReferencedInPrompt": true, "userDescription": "Get diffs of changed files", "modelDescription": "Get git diffs of current file changes in a git repository. Don't forget that you can use copilot_runInTerminal to run git commands in a terminal as well.", "tags": ["vscode_codesearch"], "inputSchema": {"type": "object", "properties": {"repositoryPath": {"type": "string", "description": "The absolute path to the git repository to look for changes in. If not provided, the active git repository will be used."}, "sourceControlState": {"type": "array", "items": {"type": "string", "enum": ["staged", "unstaged", "merge-conflicts"]}, "description": "The kinds of git state to filter by. Allowed values are: 'staged', 'unstaged', and 'merge-conflicts'. If not provided, all states will be included."}}}}, {"name": "copilot_testFailure", "toolReferenceName": "testFailure", "displayName": "Test Failure", "icon": "$(beaker)", "userDescription": "Includes information about the last unit test failure", "modelDescription": "Includes test failure information in the prompt.", "inputSchema": {}, "tags": ["vscode_editing_with_tests", "enable_other_tool_copilot_readFile", "enable_other_tool_copilot_listDirectory", "enable_other_tool_copilot_findFiles", "enable_other_tool_copilot_runTests"], "canBeReferencedInPrompt": true}, {"name": "copilot_updateUserPreferences", "toolReferenceName": "updateUserPreferences", "displayName": "Update User Preferences", "modelDescription": "Update the user's preferences file with new information about the user and their coding preferences, based on the current chat history.", "canBeReferencedInPrompt": true, "tags": [], "inputSchema": {"type": "object", "properties": {"facts": {"type": "array", "items": {"type": "string"}, "description": "An array of new user preferences to remember."}}, "required": ["facts"]}, "when": "config.github.copilot.chat.enableUserPreferences"}, {"name": "copilot_runTests", "toolReferenceName": "runTests", "canBeReferencedInPrompt": true, "displayName": "Run Tests", "modelDescription": "Runs unit tests in files. Use this tool if the user asks to run tests or when you want to validate changes using unit tests. When possible, always try to provide `files` paths containing the relevant unit tests in order to avoid unnecessarily long test runs.", "inputSchema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string"}, "description": "Test files to run. If not provided, all test files will be run."}}}, "tags": ["vscode_editing_with_tests", "enable_other_tool_copilot_readFile", "enable_other_tool_copilot_listDirectory", "enable_other_tool_copilot_findFiles", "enable_other_tool_copilot_runTests"]}, {"name": "copilot_getTerminalSelection", "toolReferenceName": "terminalSelection", "displayName": "Terminal Selection", "modelDescription": "Get the user's current selection in the active terminal.", "userDescription": "The active terminal's selection", "canBeReferencedInPrompt": true, "icon": "$(terminal)"}, {"name": "copilot_getTerminalLastCommand", "toolReferenceName": "terminalLastCommand", "displayName": "Terminal Last Command", "modelDescription": "Get the user's current selection in the active terminal.", "userDescription": "The active terminal's last run command", "canBeReferencedInPrompt": true, "icon": "$(terminal)"}, {"name": "copilot_createNewWorkspace", "displayName": "Create New Workspace", "toolReferenceName": "newWorkspace", "icon": "$(new-folder)", "userDescription": "Scaffold a new workspace in VS Code", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "modelDescription": "Get steps to help the user create any project in a VS Code workspace. Use this tool to help users set up new projects, including TypeScript-based projects, Model Context Protocol (MCP) servers, VS Code extensions, Next.js projects, Vite projects, or any other project.", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to use to generate the new workspace. This should be a clear and concise description of the workspace the user wants to create."}}, "required": ["query"]}, "tags": ["enable_other_tool_install_extension", "enable_other_tool_get_project_setup_info"]}, {"name": "copilot_createAndRunTask", "displayName": "Create and Run Task", "toolReferenceName": "createAndRunTask", "canBeReferencedInPrompt": false, "icon": "$(json)", "userDescription": "Create and run a task in the workspace", "when": "config.github.copilot.chat.agent.runTasks", "modelDescription": "For a workspace, this tool will create a task based on the package.json, README.md, and project structure so that the project can be built and run.", "inputSchema": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The absolute path of the workspace folder where the tasks.json file will be created."}, "task": {"type": "object", "description": "The task to add to the new tasks.json file.", "properties": {"label": {"type": "string", "description": "The label of the task."}, "type": {"type": "string", "description": "The type of the task. The only supported value is 'shell'.", "enum": ["shell"]}, "command": {"type": "string", "description": "The shell command to run for the task. Use this to specify commands for building or running the application."}, "args": {"type": "array", "description": "The arguments to pass to the command.", "items": {"type": "string"}}, "isBackground": {"type": "boolean", "description": "Whether the task runs in the background without blocking the UI or other tasks. Set to true for long-running processes like watch tasks or servers that should continue executing without requiring user attention. When false, the task will block the terminal until completion."}, "problemMatcher": {"type": "array", "description": "The problem matcher to use to parse task output for errors and warnings. Can be a predefined matcher like '$tsc' (TypeScript), '$eslint-stylish', '$gcc', etc., or a custom pattern defined in tasks.json. This helps VS Code display errors in the Problems panel and enables quick navigation to error locations.", "items": {"type": "string"}}, "group": {"type": "string", "description": "The group to which the task belongs."}}, "required": ["label", "type", "command"]}}, "required": ["task", "workspaceFolder"]}, "tags": ["enable_other_tool_copilot_getProjectSetupInfo"]}, {"name": "copilot_getProjectSetupInfo", "displayName": "Get Project Setup Info", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "toolReferenceName": "getProjectSetupInfo", "modelDescription": "Do not call this tool without first calling the tool to create a workspace. This tool provides a project setup information for a Visual Studio Code workspace based on a project type and programming language.", "inputSchema": {"type": "object", "properties": {"projectType": {"type": "string", "description": "The type of project to create. Supported values are: 'python-script', 'python-project', 'mcp-server', 'model-context-protocol-server', 'vscode-extension', 'next-js', 'vite' and 'other'"}, "language": {"type": "string", "description": "The programming language for the project. Supported: 'javascript', 'typescript', 'python' and 'other'."}}, "required": ["projectType"]}, "tags": []}, {"name": "copilot_installExtension", "displayName": "Install Extension in VS Code", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "toolReferenceName": "installExtension", "modelDescription": "Install an extension in VS Code. Use this tool to install an extension in Visual Studio Code as part of a new workspace creation process only.", "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the extension to install. This should be in the format <publisher>.<extension>."}, "name": {"type": "string", "description": "The name of the extension to install. This should be a clear and concise description of the extension."}}, "required": ["id", "name"]}, "tags": []}, {"name": "copilot_runVscodeCommand", "displayName": "Run VS Code Command", "when": "config.github.copilot.chat.newWorkspaceCreation.enabled", "toolReferenceName": "runVscodeCommand", "modelDescription": "Run a command in VS Code. Use this tool to run a command in Visual Studio Code as part of a new workspace creation process only.", "inputSchema": {"type": "object", "properties": {"commandId": {"type": "string", "description": "The ID of the command to execute. This should be in the format <command>."}, "name": {"type": "string", "description": "The name of the command to execute. This should be a clear and concise description of the command."}, "args": {"type": "array", "description": "The arguments to pass to the command. This should be an array of strings.", "items": {"type": "string"}}}, "required": ["commandId", "name"]}, "tags": []}, {"name": "copilot_createNewJupyterNotebook", "displayName": "Create New Jupyter Notebook", "icon": "$(notebook)", "toolReferenceName": "newJupyterNotebook", "modelDescription": "Generates a new Jupyter Notebook (.ipynb) in VS Code. Jupyter Notebooks are interactive documents commonly used for data exploration, analysis, visualization, and combining code with narrative text. This tool should only be called when the user explicitly requests to create a new Jupyter Notebook.", "userDescription": "Create a new Jupyter Notebook", "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to use to generate the jupyter notebook. This should be a clear and concise description of the notebook the user wants to create."}}, "required": ["query"]}, "tags": []}, {"name": "copilot_runVsCodeTask", "displayName": "runVsCodeTask", "toolReferenceName": "runTask", "modelDescription": "Runs a VS Code task.\n\n- If you see that an appropriate task exists for building or running code, prefer to use this tool to run the task instead of using the run_in_terminal tool.\n- Make sure that any appropriate build or watch task is running before trying to run tests or execute code.\n- If the user asks to run a task, use this tool to do so.", "inputSchema": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The workspace folder path containing the task"}, "id": {"type": "string", "description": "The task ID to run."}}, "required": ["workspaceFolder", "id"]}}, {"name": "copilot_insertEdit", "toolReferenceName": "insertEdit", "displayName": "Edit File", "modelDescription": "Insert new code into an existing file in the workspace. Use this tool once per file that needs to be modified, even if there are multiple changes for a file. Generate the \"explanation\" property first.\nThe system is very smart and can understand how to apply your edits to the files, you just need to provide minimal hints.\nAvoid repeating existing code, instead use comments to represent regions of unchanged code. Be as concise as possible. For example:\n// ...existing code...\n{ changed code }\n// ...existing code...\n{ changed code }\n// ...existing code...\n\nHere is an example of how you should use format an edit to an existing Person class:\nclass Person {\n\t// ...existing code...\n\tage: number;\n\t// ...existing code...\n\tgetAge() {\n\treturn this.age;\n\t}\n}", "tags": [], "inputSchema": {"type": "object", "properties": {"explanation": {"type": "string", "description": "A short explanation of the edit being made."}, "filePath": {"type": "string", "description": "An absolute path to the file to edit."}, "code": {"type": "string", "description": "The code change to apply to the file.\nThe system is very smart and can understand how to apply your edits to the files, you just need to provide minimal hints.\nAvoid repeating existing code, instead use comments to represent regions of unchanged code. Be as concise as possible. For example:\n// ...existing code...\n{ changed code }\n// ...existing code...\n{ changed code }\n// ...existing code...\n\nHere is an example of how you should use format an edit to an existing Person class:\nclass Person {\n\t// ...existing code...\n\tage: number;\n\t// ...existing code...\n\tgetAge() {\n\t\treturn this.age;\n\t}\n}"}}, "required": ["explanation", "filePath", "code"]}}, {"name": "copilot_createFile", "toolReferenceName": "createFile", "displayName": "Create File", "modelDescription": "This is a tool for creating a new file in the workspace. The file will be created with the specified content. The directory will be created if it does not already exist. Never use this tool to edit a file that already exists.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "The absolute path to the file to create."}, "content": {"type": "string", "description": "The content to write to the file."}}, "required": ["filePath", "content"]}}, {"name": "copilot_createDirectory", "toolReferenceName": "createDirectory", "displayName": "Create Directory", "modelDescription": "Create a new directory structure in the workspace. Will recursively create all directories in the path, like mkdir -p. You do not need to use this tool before using create_file, that tool will automatically create the needed directories.", "tags": [], "inputSchema": {"type": "object", "properties": {"dirPath": {"type": "string", "description": "The absolute path to the directory to create."}}, "required": ["<PERSON><PERSON><PERSON>"]}}, {"name": "copilot_openSimpleBrowser", "displayName": "Open Simple Browser", "modelDescription": "Preview a website or open a URL in the editor's Simple Browser. Useful for quickly viewing locally hosted websites, demos, or resources without leaving the coding environment.", "userDescription": "Preview a locally hosted website in the Simple Browser", "toolReferenceName": "openSimpleBrowser", "canBeReferencedInPrompt": true, "tags": [], "inputSchema": {"type": "object", "properties": {"url": {"type": "string", "description": "The website URL to preview or open in the Simple Browser inside the editor."}}, "required": ["url"]}}, {"name": "copilot_replaceString", "toolReferenceName": "replaceString", "displayName": "Replace String in File", "modelDescription": "This is a tool for making edits in an existing file in the workspace. For moving or renaming files, use run in terminal tool with the 'mv' command instead. For larger edits, split them into smaller edits and call the edit tool multiple times to ensure accuracy. Before editing, always ensure you have the context to understand the file's contents and context. To edit a file, provide: 1) filePath (absolute path), 2) oldString (MUST be the exact literal text to replace including all whitespace, indentation, newlines, and surrounding code etc), and 3) newString (MUST be the exact literal text to replace \\`oldString\\` with (also including all whitespace, indentation, newlines, and surrounding code etc.). Ensure the resulting code is correct and idiomatic.). Each use of this tool replaces exactly ONE occurrence of oldString.\n\nCRITICAL for \\`oldString\\`: Must uniquely identify the single instance to change. Include at least 3 lines of context BEFORE and AFTER the target text, matching whitespace and indentation precisely. If this string matches multiple locations, or does not match exactly, the tool will fail. Never use ...existing code... comments in the oldString or newString.", "when": "!config.github.copilot.chat.disableReplaceTool", "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the file to edit."}, "oldString": {"type": "string", "description": "The exact literal text to replace, preferably unescaped. For single replacements (default), include at least 3 lines of context BEFORE and AFTER the target text, matching whitespace and indentation precisely. For multiple replacements, specify expected_replacements parameter. If this string is not the exact literal text (i.e. you escaped it) or does not match exactly, the tool will fail."}, "newString": {"type": "string", "description": "The exact literal text to replace `old_string` with, preferably unescaped. Provide the EXACT text. Ensure the resulting code is correct and idiomatic."}}, "required": ["filePath", "oldString", "newString"]}}, {"name": "copilot_editNotebook", "toolReferenceName": "editNotebook", "displayName": "Edit Notebook", "modelDescription": "This is a tool for editing an existing Notebook file in the workspace. Generate the \"explanation\" property first.\nThe system is very smart and can understand how to apply your edits to the notebooks.\nWhen updating the content of an existing cell, ensure newCode includes at least 3-5 lines of context both before and after the new changes, preserving whitespace and indentation exactly.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file to edit, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1."}, "explanation": {"type": "string", "description": "A one-sentence description of edit operation. This will be shown to the user before the tool is run."}, "cellId": {"type": "string", "description": "Id of the cell that needs to be deleted or edited. Use the value `TOP`, `BOTTOM` when inserting a cell at the top or bottom of the notebook, else provide the id of the cell after which a new cell is to be inserted. Remember, if a cellId is provided and editType=insert, then a cell will be inserted after the cell with the provided cellId."}, "newCode": {"anyOf": [{"type": "string", "description": "The code for the new or existing cell to be edited. Code should not be wrapped within <VSCode.Cell> tags"}, {"type": "array", "items": {"type": "string", "description": "The code for the new or existing cell to be edited. Code should not be wrapped within <VSCode.Cell> tags"}}]}, "language": {"type": "string", "description": "The language of the cell. `markdown`, `python`, `javascript`, `julia`, etc."}, "editType": {"type": "string", "enum": ["insert", "delete", "edit"], "description": "The operation peformed on the cell, whether `insert`, `delete` or `edit`.\nUse the `editType` field to specify the operation: `insert` to add a new cell, `edit` to modify an existing cell's content, and `delete` to remove a cell."}}, "required": ["filePath", "explanation", "editType"]}}, {"name": "copilot_runNotebookCell", "displayName": "Run Notebook Cell", "toolReferenceName": "runCell", "icon": "$(play)", "modelDescription": "This is a tool for running a code cell in a notebook file directly in the notebook editor. The output from the execution will be returned. Code cells should be run as they are added or edited when working through a problem to bring the kernel state up to date and ensure the code executes successfully. Code cells are ready to run and don't require any pre-processing. If asked to run the first cell in a notebook, you should run the first code cell since markdown cells cannot be executed. NOTE: Avoid executing Markdown cells or providing Markdown cell IDs, as Markdown cells cannot be  executed.", "userDescription": "Trigger the execution of a cell in a notebook file", "tags": ["enable_other_tool_copilot_getNotebookSummary"], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file with the cell to run, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1.ipynb"}, "reason": {"type": "string", "description": "An optional explanation of why the cell is being run. This will be shown to the user before the tool is run and is not necessary if it's self-explanatory."}, "cellId": {"type": "string", "description": "The ID for the code cell to execute. Avoid providing markdown cell IDs as nothing will be executed."}, "continueOnError": {"type": "boolean", "description": "Whether or not execution should continue for remaining cells if an error is encountered. Default to false unless instructed otherwise."}}, "required": ["filePath", "cellId"]}}, {"name": "copilot_getNotebookSummary", "toolReferenceName": "getNotebookSummary", "displayName": "Get the structure of a notebook", "modelDescription": "This is a tool returns the list of the Notebook cells along with the id, cell types, language, execution information and output mime types for each cell. This is useful to get Cell Ids when executing a notebook or determine what cells have been executed and what order, or what cells have outputs. Requery this tool if the contents of the notebook change.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file with the cell to run, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1.ipynb"}}, "required": ["filePath"]}}, {"name": "copilot_readNotebookCellOutput", "displayName": "Get Notebook Cell Output", "toolReferenceName": "readCellOutput", "canBeReferencedInPrompt": true, "icon": "$(notebook-render-output)", "modelDescription": "This tool will retrieve the output for a notebook cell from its most recent execution or restored from disk. The cell may have output even when it has not been run in the current kernel session. This tool has a higher token limit for output length than the runNotebookCell tool.", "userDescription": "Read the output of a previously executed cell", "when": "userHasOpenedNotebook", "tags": [], "inputSchema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "An absolute path to the notebook file with the cell to run, or the URI of a untitled, not yet named, file, such as `untitled:Untitled-1.ipynb"}, "cellId": {"type": "string", "description": "The ID of the cell for which output should be retrieved."}}, "required": ["filePath", "cellId"]}}, {"name": "copilot_fetchWebPage", "displayName": "Fetch Web Page", "toolReferenceName": "fetch", "when": "!isWeb", "canBeReferencedInPrompt": true, "icon": "$(globe)", "userDescription": "Fetch the main content from a web page. You should include the URL of the page you want to fetch.", "modelDescription": "Fetches the main content from a web page. This tool is useful for summarizing or analyzing the content of a webpage. You should use this tool when you think the user is looking for information from a specific webpage.", "tags": [], "inputSchema": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}, "description": "An array of URLs to fetch content from."}, "query": {"type": "string", "description": "The query to search for in the web page's content. This should be a clear and concise description of the content you want to find."}}, "required": ["urls", "query"]}}, {"name": "copilot_findTestFiles", "displayName": "Find Test Files", "icon": "$(beaker)", "canBeReferencedInPrompt": true, "toolReferenceName": "findTestFiles", "userDescription": "For a source code file, find the file that contains the tests. For a test file, find the file that contains the code under test", "modelDescription": "For a source code file, find the file that contains the tests. For a test file find the file that contains the code under test.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePaths": {"type": "array", "items": {"type": "string"}}}, "required": ["filePaths"]}}, {"name": "copilot_getDocInfo", "displayName": "Doc Info", "icon": "$(beaker)", "canBeReferencedInPrompt": false, "toolReferenceName": "docInfo", "userDescription": "For a symbol like a class or function, find the information about how to document it", "modelDescription": "Find information about how to document it a symbol like a class or function. This tool is useful for generating documentation comments for code symbols. You should use this tool when you think the user is looking for information about how to document a specific code symbol.", "tags": [], "inputSchema": {"type": "object", "properties": {"filePaths": {"type": "array", "items": {"type": "string"}, "description": "The file paths for which documentation information is needed."}}, "required": ["filePaths"]}}, {"name": "copilot_getSearchResults", "toolReferenceName": "searchResults", "displayName": "Search View Results", "icon": "$(search)", "userDescription": "The results from the search view", "modelDescription": "The results from the search view", "canBeReferencedInPrompt": true}, {"name": "copilot_githubRepo", "toolReferenceName": "githubRepo", "displayName": "Search GitHub Repository", "modelDescription": "Searches a GitHub repository for relevant source code snippets. Only use this tool if the user is very clearly asking for code snippets from a specific GitHub repository. Do not use this tool for Github repos that the user has open in their workspace.", "userDescription": "Searches a GitHub repository for relevant source code snippets. You can specify a repository using `owner/repo`", "icon": "$(repo)", "canBeReferencedInPrompt": true, "inputSchema": {"type": "object", "properties": {"repo": {"type": "string", "description": "The name of the Github repository to search for code in. Should must be formatted as '<owner>/<repo>'."}, "query": {"type": "string", "description": "The query to search for repo. Should contain all relevant context."}}, "required": ["repo", "query"]}}], "languageModelToolSets": [{"name": "editFiles", "description": "Edit files in your workspace", "icon": "$(pencil)", "tools": ["insertEdit", "replaceString", "applyPatch", "createFile", "createDirectory", "editNotebook", "newJupyterNotebook"]}, {"name": "runNotebooks", "description": "Run notebook cells", "icon": "$(notebook)", "tools": ["runCell", "getNotebookSummary", "readCellOutput"]}, {"name": "runCommands", "description": "Run commands in the terminal", "icon": "$(terminal)", "tools": ["runInTerminal", "getTerminalOutput"]}, {"name": "search", "description": "Search and read files in your workspace", "icon": "$(search)", "tools": ["fileSearch", "textSearch", "listDirectory", "readCellOutput", "readFile"]}, {"name": "new", "description": "Scaffold a new workspace in VS Code", "icon": "$(new-folder)", "tools": ["newWorkspace", "runVscodeCommand", "getProjectSetupInfo", "installExtension"]}, {"name": "runTasks", "description": "Run tasks in your workspace", "tools": ["createAndRunTask", "runTask", "getTaskOutput"]}], "chatParticipants": [{"id": "github.copilot.default", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "Ask Copilot", "isDefault": true, "locations": ["panel"], "modes": ["ask"], "disambiguation": [{"category": "generate_code_sample", "description": "The user wants to generate code snippets without referencing the contents of the current workspace. This category does not include generating entire projects.", "examples": ["Write an example of computing a SHA256 hash."]}, {"category": "add_feature_to_file", "description": "The user wants to change code in a file that is provided in their request, without referencing the contents of the current workspace. This category does not include generating entire projects.", "examples": ["Add a refresh button to the table widget."]}, {"category": "question_about_specific_files", "description": "The user has a question about a specific file or code snippet that they have provided as part of their query, and the question does not require additional workspace context to answer.", "examples": ["What does this file do?"]}]}, {"id": "github.copilot.editingSession", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "Edit files in your workspace", "isDefault": true, "locations": ["panel"], "modes": ["edit"], "when": "!config.chat.edits2.enabled"}, {"id": "github.copilot.editingSessionEditor", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "Edit files in your workspace", "isDefault": true, "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "locations": ["editor", "notebook"], "commands": [{"name": "fix", "description": "Propose a fix for the problems in the selected code", "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "fix", "description": "Propose a fix for the problems in the selected code", "examples": ["There is a problem in this code. Rewrite the code to show it with the bug fixed."]}]}, {"name": "tests", "description": "Generate unit tests for the selected code", "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "tests", "description": "Help writing tests for the selected code", "examples": ["Help me write tests for the selected code."]}]}, {"name": "doc", "description": "Add documentation comment for this symbol", "when": "config.inlineChat.enableV2 || config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "doc", "description": "Add documentation comment for this symbol", "examples": ["Add jsdoc to this method"]}]}]}, {"id": "github.copilot.editingSession2", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "Edit files in your workspace", "isDefault": true, "locations": ["panel"], "modes": ["edit"], "when": "config.chat.edits2.enabled"}, {"id": "github.copilot.editsAgent", "name": "agent", "fullName": "GitHub Copilot", "description": "Edit files in your workspace in agent mode", "locations": ["panel"], "modes": ["agent"], "isDefault": true, "isAgent": true, "when": "config.chat.agent.enabled", "commands": [{"name": "list"}, {"name": "error", "when": "github.copilot.chat.debug"}]}, {"id": "github.copilot.editor", "name": "Copilot", "fullName": "GitHub Copilot", "description": "Ask Copilot", "isDefault": true, "locations": ["editor"], "when": "!config.inlineChat.enableV2 && !config.github.copilot.chat.advanced.inlineChat2", "disambiguation": [{"category": "unknown", "description": "Intent of this command is unclear or is not related to information technologies", "examples": ["Add a dog to this comment."]}], "commands": [{"name": "generate", "description": "Generate new code", "disambiguation": [{"category": "generate", "description": "Generate new code", "examples": ["Add a function that returns the sum of two numbers"]}]}, {"name": "edit", "description": "Edit the selected code in your active editor", "disambiguation": [{"category": "edit", "description": "Make changes to existing code", "examples": ["Change this method to use async/await"]}]}, {"name": "doc", "description": "Add documentation comment for this symbol", "disambiguation": [{"category": "doc", "description": "Add documentation comment for this symbol", "examples": ["Add jsdoc to this method"]}]}, {"name": "fix", "description": "Propose a fix for the problems in the selected code", "disambiguation": [{"category": "fix", "description": "Propose a fix for the problems in the selected code", "examples": ["There is a problem in this code. Rewrite the code to show it with the bug fixed."]}]}, {"name": "explain", "description": "Explain how the code in your active editor works", "disambiguation": [{"category": "explain", "description": "Explain how the code in your active editor works", "examples": ["Write an explanation for the code above as paragraphs of text."]}]}, {"name": "review", "description": "Review the selected code in your active editor", "when": "github.copilot.advanced.review.intent"}, {"name": "tests", "description": "Generate unit tests for the selected code", "disambiguation": [{"category": "tests", "description": "Generate unit tests for the selected code. The user does not want to fix their existing tests.", "examples": ["Write a set of detailed unit test functions for the code above."]}]}]}, {"id": "github.copilot.notebook", "name": "GitHubCopilot", "fullName": "GitHub Copilot", "description": "Ask Copilot", "isDefault": true, "locations": ["notebook"], "when": "!config.inlineChat.enableV2 && !config.github.copilot.chat.advanced.inlineChat2", "commands": [{"name": "fix", "description": "Propose a fix for the problems in the selected code"}, {"name": "explain", "description": "Explain how the code in your active editor works"}]}, {"id": "github.copilot.workspace", "name": "workspace", "fullName": "Workspace", "description": "Ask about your workspace", "sampleRequest": "How do I build this project?", "locations": ["panel"], "disambiguation": [{"category": "workspace_project_questions", "description": "The user wants to learn about or update the code or files in their current workspace. Questions in this category may be about understanding what the whole workspace does or locating the implementation of some code. This does not include generating or updating tests.", "examples": ["What does this project do?"]}, {"category": "find_code_in_workspace", "description": "The user wants to locate the implementation of some functionality in their current workspace.", "examples": ["Where is the tree widget implemented?"]}, {"category": "generate_with_workspace_context", "description": "The user wants to generate code based on multiple files in the workspace and did not specify which files to reference.", "examples": ["Create a README for this project."]}], "commands": [{"name": "explain", "description": "Explain how the code in your active editor works"}, {"name": "review", "description": "Review the selected code in your active editor", "when": "github.copilot.advanced.review.intent"}, {"name": "tests", "description": "Generate unit tests for the selected code", "disambiguation": [{"category": "create_tests", "description": "The user wants to generate unit tests.", "examples": ["Generate tests for my selection using pytest."]}]}, {"name": "fix", "description": "Propose a fix for the problems in the selected code", "sampleRequest": "There is a problem in this code. Rewrite the code to show it with the bug fixed."}, {"name": "new", "description": "Scaffold code for a new file or project in a workspace", "sampleRequest": "Create a RESTful API server using typescript", "isSticky": true, "disambiguation": [{"category": "create_new_workspace_or_extension", "description": "The user wants to create a complete Visual Studio Code workspace from scratch, such as a new application or a Visual Studio Code extension. Use this category only if the question relates to generating or creating new workspaces in Visual Studio Code. Do not use this category for updating existing code or generating sample code snippets", "examples": ["Scaffold a Node server.", "Create a sample project which uses the fileSystemProvider API.", "react application"]}]}, {"name": "newNotebook", "description": "Create a new Jupyter Notebook", "sampleRequest": "How do I create a notebook to load data from a csv file?", "disambiguation": [{"category": "create_jupyter_notebook", "description": "The user wants to create a new <PERSON><PERSON>ter notebook in Visual Studio Code.", "examples": ["Create a notebook to analyze this CSV file."]}]}, {"name": "semanticSearch", "description": "Find relevant code to your query", "sampleRequest": "Where is the toolbar code?", "when": "config.github.copilot.semanticSearch.enabled"}, {"name": "setupTests", "description": "Set up tests in your project (Experimental)", "sampleRequest": "add playwright tests to my project", "when": "config.github.copilot.chat.setupTests.enabled", "disambiguation": [{"category": "set_up_tests", "description": "The user wants to configure project test setup, framework, or test runner. The user does not want to fix their existing tests.", "examples": ["Set up tests for this project."]}]}]}, {"id": "github.copilot.vscode", "name": "vscode", "fullName": "VS Code", "description": "Ask questions about VS Code", "sampleRequest": "What is the command to open the integrated terminal?", "locations": ["panel"], "disambiguation": [{"category": "vscode_configuration_questions", "description": "The user wants to learn about, use, or configure the Visual Studio Code. Use this category if the users question is specifically about commands, settings, keybindings, extensions and other features available in Visual Studio Code. Do not use this category to answer questions about generating code or creating new projects including Visual Studio Code extensions.", "examples": ["Switch to light mode.", "Keyboard shortcut to toggle terminal visibility.", "Settings to enable minimap.", "Whats new in the latest release?"]}, {"category": "configure_python_environment", "description": "The user wants to set up their Python environment.", "examples": ["Create a virtual environment for my project."]}], "commands": [{"name": "search", "description": "Generate query parameters for workspace search", "sampleRequest": "Search for 'foo' in all files under my 'src' directory"}, {"name": "startDebugging", "description": "Generate launch config and start debugging in VS Code (Experimental)", "sampleRequest": "Attach to node app at port 9229", "when": "config.github.copilot.chat.startDebugging.enabled"}]}, {"id": "github.copilot.terminal", "name": "terminal", "fullName": "Terminal", "description": "Ask Copilot", "sampleRequest": "How do I view all files within a directory including sub-directories?", "isDefault": true, "locations": ["terminal"], "commands": [{"name": "explain", "description": "Explain something in the terminal", "sampleRequest": "Explain the last command"}]}, {"id": "github.copilot.terminalPanel", "name": "terminal", "fullName": "Terminal", "description": "Ask how to do something in the terminal", "sampleRequest": "How do I view all files within a directory including sub-directories?", "locations": ["panel"], "commands": [{"name": "explain", "description": "Explain something in the terminal", "sampleRequest": "Explain the last command", "disambiguation": [{"category": "terminal_state_questions", "description": "The user wants to learn about specific state such as the selection, command, or failed command in the integrated terminal in Visual Studio Code.", "examples": ["Why did the latest terminal command fail?"]}]}]}], "languageModels": [{"vendor": "copilot"}, {"vendor": "copilot-byok"}], "interactiveSession": [{"label": "GitHub Copilot", "id": "copilot", "icon": "", "when": "!github.copilot.interactiveSession.disabled"}], "viewsWelcome": [{"view": "debug", "when": "github.copilot-chat.activated", "contents": "GitHub Copilot Chat can help you start debugging, either based on a [terminal command](command:github.copilot.chat.startCopilotDebugCommand) or with [interactive chat](command:workbench.action.chat.open?%7B%22query%22%3A%22%40vscode%20%2FstartDebugging%20%22%2C%22isPartialQuery%22%3Atrue%7D)."}], "chatViewsWelcome": [{"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "Sign in to use Copilot AI features.\n\n[Sign in to use Copilot](command:workbench.action.chat.triggerSetup)", "when": "!github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport && !github.copilot.interactiveSession.chatDisabled && !github.copilot.interactiveSession.switchToReleaseChannel"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "Your Copilot subscription has expired.\n\n[Review Copilot Settings](https://github.com/settings/copilot?editor=vscode)", "when": "github.copilot.interactiveSession.individual.expired"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "Contact your GitHub organization administrator to enable Copilot.", "when": "github.copilot.interactiveSession.enterprise.disabled"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "You are currently offline. Please connect to the internet to use GitHub Copilot.\n\n[Retry Connection](command:github.copilot.refreshToken)", "when": "github.copilot.offline"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "There seems to be a problem with your account. Please contact GitHub support.\n\n[Contact Support](https://support.github.com/?editor=vscode)", "when": "github.copilot.interactiveSession.contactSupport"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "GitHub Copilot <PERSON> is currently disabled for your account by an organization administrator. Contact an organization administrator to enable chat.\n\n[Learn More](https://docs.github.com/en/copilot/managing-copilot/managing-github-copilot-in-your-organization/managing-github-copilot-features-in-your-organization/managing-policies-for-copilot-in-your-organization)", "when": "github.copilot.interactiveSession.chatDisabled"}, {"icon": "$(copilot-large)", "title": "Ask Copilot", "content": "The Pre-Release version of the GitHub Copilot Chat extension is not currently supported in the stable version of VS Code. Please switch to the release version for GitHub Copilot Chat or try VS Code Insiders.\n\n[Switch to Release Version and Reload](command:runCommands?%7B%22commands%22%3A%5B%7B%22command%22%3A%22workbench.extensions.action.switchToRelease%22%2C%22args%22%3A%5B%22GitHub.copilot-chat%22%5D%7D%2C%22workbench.action.reloadWindow%22%5D%7D)\n\n[Switch to VS Code Insiders](https://aka.ms/vscode-insiders)", "when": "github.copilot.interactiveSession.switchToReleaseChannel"}], "commands": [{"command": "github.copilot.chat.explain", "title": "Explain", "enablement": "!github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.explain.palette", "title": "Explain", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review", "title": "Review and Comment", "enablement": "github.copilot.chat.reviewSelection.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.apply", "title": "Apply", "icon": "$(sparkle)", "enablement": "commentThread =~ /hasSuggestion/", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.applyAndNext", "title": "Apply and Go to Next", "icon": "$(sparkle)", "enablement": "commentThread =~ /hasSuggestion/", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.discard", "title": "Discard", "icon": "$(close)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.discardAndNext", "title": "Discard and Go to Next", "icon": "$(close)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.discardAll", "title": "Discard All", "icon": "$(close-all)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.stagedChanges", "title": "Copilot Code Review - Staged Changes", "icon": "$(code-review)", "enablement": "github.copilot.chat.reviewDiff.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.unstagedChanges", "title": "Copilot Code Review - Unstaged Changes", "icon": "$(code-review)", "enablement": "github.copilot.chat.reviewDiff.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.changes", "title": "Copilot Code Review - Uncommitted Changes", "icon": "$(code-review)", "enablement": "github.copilot.chat.reviewDiff.enabled && !github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.changes.cancel", "title": "Copilot Code Review - Cancel", "icon": "$(stop-circle)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.previous", "title": "Previous Suggestion", "icon": "$(arrow-up)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.next", "title": "Next Suggestion", "icon": "$(arrow-down)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.continueInInlineChat", "title": "<PERSON>ard and <PERSON><PERSON> to Inline Chat", "icon": "$(comment-discussion)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.continueInChat", "title": "View in Chat Panel", "icon": "$(comment-discussion)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.review.markHelpful", "title": "Helpful", "icon": "$(thumbsup)", "enablement": "!(commentThread =~ /markedAsHelpful/)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.openUserPreferences", "title": "Open User Preferences", "category": "GitHub Copilot", "enablement": "config.github.copilot.chat.enableUserPreferences"}, {"command": "github.copilot.chat.review.markUnhelpful", "title": "Unhelpful", "icon": "$(thumbsdown)", "enablement": "!(commentThread =~ /markedAsUnhelpful/)", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.generate", "title": "Generate This", "icon": "$(sparkle)", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.generateDocs", "title": "Generate Docs", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.generateTests", "title": "Generate Tests", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.fix", "title": "Fix", "enablement": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "category": "GitHub Copilot"}, {"command": "github.copilot.interactiveSession.feedback", "title": "<PERSON> <PERSON><PERSON>", "enablement": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled", "icon": "$(feedback)", "category": "GitHub Copilot"}, {"command": "github.copilot.debug.workbenchState", "title": "Log Workbench State", "category": "Developer"}, {"command": "github.copilot.debug.showChatLogView", "title": "Show Chat Debug View", "category": "Developer"}, {"command": "github.copilot.terminal.explainTerminalSelection", "title": "Explain Terminal Selection", "category": "GitHub Copilot"}, {"command": "github.copilot.terminal.explainTerminalSelectionContextMenu", "title": "Explain", "category": "GitHub Copilot"}, {"command": "github.copilot.terminal.explainTerminalLastCommand", "title": "Explain Last Terminal Command", "category": "GitHub Copilot"}, {"command": "github.copilot.terminal.attachTerminalSelection", "title": "Add Terminal Selection to Chat", "category": "GitHub Copilot"}, {"command": "github.copilot.git.generateCommitMessage", "title": "Generate Commit Message with Copilot", "icon": "$(sparkle)", "enablement": "!github.copilot.interactiveSession.disabled", "category": "GitHub Copilot"}, {"command": "github.copilot.devcontainer.generateDevContainerConfig", "title": "Generate Dev Container Configuration with Copilot", "category": "GitHub Copilot"}, {"command": "github.copilot.tests.fixTestFailure", "icon": "$(sparkle)", "title": "Fix Test Failure", "category": "GitHub Copilot"}, {"command": "github.copilot.tests.fixTestFailure.fromInline", "icon": "$(sparkle)", "title": "Fix Test Failure"}, {"command": "github.copilot.chat.attachSelection", "title": "Add Selection to Chat", "icon": "$(comment-discussion)", "category": "GitHub Copilot"}, {"command": "github.copilot.debug.collectDiagnostics", "title": "GitHub Copilot Chat Diagnostics", "category": "Developer"}, {"command": "github.copilot.debug.generateSTest", "title": "Generate STest From Last Chat Request", "enablement": "github.copilot.debugReportFeedback", "category": "GitHub Copilot Developer"}, {"command": "github.copilot.debug.generateConfiguration", "title": "Generate Debug Configuration with GitHub Copilot", "category": "GitHub Copilot", "enablement": "config.github.copilot.chat.startDebugging.enabled", "tags": ["experimental"]}, {"command": "github.copilot.open.walkthrough", "title": "Open Walkthrough", "category": "GitHub Copilot"}, {"command": "github.copilot.debug.generateInlineEditTests", "title": "Generate Inline Edit Tests", "category": "GitHub Copilot", "enablement": "resourceScheme == 'ccreq'"}, {"command": "github.copilot.buildLocalWorkspaceIndex", "title": "Build Local Workspace Index", "category": "GitHub Copilot", "enablement": "github.copilot-chat.activated"}, {"command": "github.copilot.buildRemoteWorkspaceIndex", "title": "Build Remote Workspace Index", "category": "GitHub Copilot", "enablement": "github.copilot-chat.activated"}, {"command": "github.copilot.report", "title": "Report Issue", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.rerunWithCopilotDebug", "title": "Debug Last Terminal Command", "category": "GitHub Copilot"}, {"command": "github.copilot.chat.startCopilotDebugCommand", "title": "Start Copilot Debug"}, {"command": "github.copilot.chat.clearTemporalContext", "title": "Clear Temporal Context", "category": "GitHub Copilot (Developer)"}, {"command": "github.copilot.search.markHelpful", "title": "Helpful", "icon": "$(thumbsup)", "enablement": "!github.copilot.search.feedback.sent"}, {"command": "github.copilot.search.markUnhelpful", "title": "Unhelpful", "icon": "$(thumbsdown)", "enablement": "!github.copilot.search.feedback.sent"}, {"command": "github.copilot.search.feedback", "title": "<PERSON><PERSON><PERSON>", "icon": "$(feedback)", "enablement": "!github.copilot.search.feedback.sent"}, {"command": "github.copilot.chat.manageModels", "title": "Manage Models...", "icon": "$(settings-gear)", "category": "GitHub Copilot", "enablement": "github.copilot.byokEnabled"}, {"command": "github.copilot.chat.debug.showElements", "title": "Show Rendered Elements"}, {"command": "github.copilot.chat.debug.hideElements", "title": "Hide Rendered Elements"}, {"command": "github.copilot.chat.debug.showTools", "title": "Show Tools"}, {"command": "github.copilot.chat.debug.hideTools", "title": "<PERSON><PERSON>"}, {"command": "github.copilot.chat.debug.exportLogItem", "title": "Export as...", "icon": "$(export)"}, {"command": "github.copilot.chat.debug.exportPromptArchive", "title": "Export All as Archive...", "icon": "$(archive)"}, {"command": "github.copilot.debug.collectWorkspaceIndexDiagnostics", "title": "Collect Workspace Index Diagnostics", "category": "Developer"}, {"command": "github.copilot.chat.mcp.setup.check", "title": "MCP Check: is supported"}, {"command": "github.copilot.chat.mcp.setup.validatePackage", "title": "MCP Check: validate package"}, {"command": "github.copilot.chat.mcp.setup.flow", "title": "MCP Check: do prompts"}, {"command": "github.copilot.chat.generateAltText", "title": "Generate/Refine Alt Text"}, {"command": "github.copilot.chat.notebook.enableFollowCellExecution", "title": "Enable Follow Cell Execution from Chat", "shortTitle": "Follow", "icon": "$(pinned)"}, {"command": "github.copilot.chat.notebook.disableFollowCellExecution", "title": "Disable Follow Cell Execution from Chat", "shortTitle": "Unfollow", "icon": "$(pinned-dirty)"}], "configuration": [{"title": "GitHub Copilot Chat", "id": "stable", "properties": {"github.copilot.chat.codeGeneration.useInstructionFiles": {"type": "boolean", "default": true, "markdownDescription": "Controls whether code instructions from `.github/copilot-instructions.md` are added to Copilot requests.\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance. [Learn more](https://aka.ms/github-copilot-custom-instructions) about customizing Copilot."}, "github.copilot.editor.enableCodeActions": {"type": "boolean", "default": true, "description": "Controls if Copilot commands are shown as Code Actions when available"}, "github.copilot.renameSuggestions.triggerAutomatically": {"type": "boolean", "default": true, "description": "Controls whether Copilot generates suggestions for renaming"}, "github.copilot.chat.localeOverride": {"type": "string", "enum": ["auto", "en", "fr", "it", "de", "es", "ru", "zh-CN", "zh-TW", "ja", "ko", "cs", "pt-br", "tr", "pl"], "enumDescriptions": ["Use VS Code's configured display language", "English", "français", "italiano", "De<PERSON>ch", "español", "русский", "中文(简体)", "中文(繁體)", "日本語", "한국어", "čeština", "português", "Türkçe", "polski"], "default": "auto", "markdownDescription": "Specify a locale that <PERSON>pi<PERSON> should respond in, e.g. `en` or `fr`. By default, Copilot will respond using VS Code's configured display language locale."}, "github.copilot.chat.terminalChatLocation": {"type": "string", "default": "chatView", "markdownDescription": "Controls where chat queries from the terminal should be opened.", "markdownEnumDescriptions": ["Open the chat view.", "Open quick chat.", "Open terminal inline chat"], "enum": ["chatView", "quickChat", "terminal"]}, "github.copilot.chat.scopeSelection": {"type": "boolean", "default": false, "markdownDescription": "Whether to prompt the user to select a specific symbol scope if the user uses `/explain` and the active editor has no selection."}, "github.copilot.chat.useProjectTemplates": {"type": "boolean", "default": true, "markdownDescription": "Use relevant GitHub projects as starter projects when using `/new`"}, "github.copilot.chat.agent.runTasks": {"type": "boolean", "default": true, "description": "Configures whether Copilot Edits can run workspace tasks in agent mode."}, "github.copilot.nextEditSuggestions.enabled": {"type": "boolean", "default": false, "tags": ["nextEditSuggestions", "onExp"], "markdownDescription": "Whether to enable next edit suggestions (NES).\n\nNES can propose a next edit based on your recent changes. [Learn more](https://aka.ms/vscode-nes) about next edit suggestions.", "scope": "language-overridable"}, "github.copilot.nextEditSuggestions.fixes": {"type": "boolean", "default": true, "tags": ["nextEditSuggestions", "onExp"], "markdownDescription": "Whether to offer fixes for diagnostics via next edit suggestions (NES).", "scope": "language-overridable"}, "github.copilot.chat.agent.autoFix": {"type": "boolean", "default": true, "description": "Automatically fix diagnostics for edited files."}}}, {"title": "Preview", "id": "preview", "properties": {"github.copilot.chat.startDebugging.enabled": {"type": "boolean", "default": true, "markdownDescription": "Enables the `/startDebugging` intent in panel chat. Generates or finds launch config to match the query (if any), project structure, and more.", "tags": ["preview"]}, "github.copilot.chat.reviewSelection.enabled": {"type": "boolean", "default": true, "description": "Enables code review on current selection.", "tags": ["preview"]}, "github.copilot.chat.reviewSelection.instructions": {"type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "A path to a file that will be added to Copilot requests that provide code review for the current selection. Optionally, you can specify a language for the instruction.", "properties": {"file": {"type": "string", "examples": [".copilot-review-instructions.md"]}, "language": {"type": "string"}}, "examples": [{"file": ".copilot-review-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "A text instruction that will be added to Copilot requests that provide code review for the current selection. Optionally, you can specify a language for the instruction.", "properties": {"text": {"type": "string", "examples": ["Use underscore for field names."]}, "language": {"type": "string"}}, "required": ["text"], "examples": [{"text": "Use underscore for field names."}, {"text": "Resolve all TODO tasks."}]}]}, "default": [], "markdownDescription": "A set of instructions that will be added to Copilot requests that provide code review for the current selection.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use underscore for field names.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's effectiveness.", "examples": [[{"file": ".copilot-review-instructions.md"}, {"text": "Resolve all TODO tasks."}]], "tags": ["preview"]}, "github.copilot.chat.copilotDebugCommand.enabled": {"type": "boolean", "default": true, "tags": ["preview"], "description": "Whether the `copilot-debug` command is enabled in the terminal."}, "github.copilot.chat.codesearch.enabled": {"type": "boolean", "default": false, "tags": ["preview"], "markdownDescription": "Whether to enable agentic codesearch when using `#codebase`."}, "github.copilot.chat.edits.codesearch.enabled": {"type": "boolean", "default": false, "tags": ["preview"], "markdownDeprecationMessage": "This setting is deprecated in favor of `#github.copilot.chat.codesearch.enabled#`."}, "github.copilot.chat.byok.ollamaEndpoint": {"type": "string", "default": "http://localhost:11434", "tags": ["preview"], "markdownDescription": "The endpoint to use for the Ollama when accessed via bring your own key. Defaults to localhost."}}}, {"title": "Experimental", "id": "experimental", "properties": {"github.copilot.chat.agent.thinkingTool": {"type": "boolean", "default": false, "tags": ["experimental"], "markdownDescription": "Enables the thinking tool that allows <PERSON><PERSON><PERSON> to think deeply about your request before generating a response in agent mode."}, "github.copilot.chat.edits.suggestRelatedFilesFromGitHistory": {"type": "boolean", "default": true, "tags": ["experimental"], "markdownDescription": "Whether to suggest related files from git history for the Copilot Edits working set."}, "github.copilot.chat.edits.suggestRelatedFilesForTests": {"type": "boolean", "default": true, "tags": ["experimental"], "markdownDescription": "Whether to suggest source files from test files for the Copilot Edits working set."}, "github.copilot.chat.codeGeneration.instructions": {"markdownDeprecationMessage": "Use instructions files instead. See https://aka.ms/vscode-ghcp-custom-instructions for more information.", "type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "A path to a file that will be added to Copilot requests that generate code. Optionally, you can specify a language for the instruction.", "properties": {"file": {"type": "string", "examples": [".copilot-codeGeneration-instructions.md"]}, "language": {"type": "string"}}, "examples": [{"file": ".copilot-codeGeneration-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "A text instruction that will be added to Copilot requests that generate code. Optionally, you can specify a language for the instruction.", "properties": {"text": {"type": "string", "examples": ["Use underscore for field names."]}, "language": {"type": "string"}}, "required": ["text"], "examples": [{"text": "Use underscore for field names."}, {"text": "Always add a comment: 'Generated by <PERSON><PERSON><PERSON>'."}]}]}, "default": [], "markdownDescription": "A set of instructions that will be added to Copilot requests that generate code.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use underscore for field names.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "examples": [[{"file": ".copilot-codeGeneration-instructions.md"}, {"text": "Always add a comment: 'Generated by <PERSON><PERSON><PERSON>'."}]], "tags": ["experimental"]}, "github.copilot.chat.testGeneration.instructions": {"markdownDeprecationMessage": "Use instructions files instead. See https://aka.ms/vscode-ghcp-custom-instructions for more information.", "type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "A path to a file that will be added to Copilot requests that generate tests. Optionally, you can specify a language for the instruction.", "properties": {"file": {"type": "string", "examples": [".copilot-test-instructions.md"]}, "language": {"type": "string"}}, "examples": [{"file": ".copilot-test-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "A text instruction that will be added to Copilot requests that generate tests. Optionally, you can specify a language for the instruction.", "properties": {"text": {"type": "string", "examples": ["Use suite and test instead of describe and it."]}, "language": {"type": "string"}}, "required": ["text"], "examples": [{"text": "Always try uniting related tests in a suite."}]}]}, "default": [], "markdownDescription": "A set of instructions that will be added to Copilot requests that generate tests.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use underscore for field names.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "examples": [[{"file": ".copilot-test-instructions.md"}, {"text": "Always try uniting related tests in a suite."}]], "tags": ["experimental"]}, "github.copilot.chat.commitMessageGeneration.instructions": {"type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "A path to a file with instructions that will be added to Copilot requests that generate commit messages.", "properties": {"file": {"type": "string", "examples": [".copilot-commit-message-instructions.md"]}}, "examples": [{"file": ".copilot-commit-message-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "Text instructions that will be added to Copilot requests that generate commit messages.", "properties": {"text": {"type": "string", "examples": ["Use conventional commit message format."]}}, "required": ["text"], "examples": [{"text": "Use conventional commit message format."}]}]}, "default": [], "markdownDescription": "A set of instructions that will be added to Copilot requests that generate commit messages.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use conventional commit message format.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "examples": [[{"file": ".copilot-commit-message-instructions.md"}, {"text": "Use conventional commit message format."}]], "tags": ["experimental"]}, "github.copilot.chat.pullRequestDescriptionGeneration.instructions": {"type": "array", "items": {"oneOf": [{"type": "object", "markdownDescription": "A path to a file with instructions that will be added to Copilot requests that generate pull request titles and descriptions.", "properties": {"file": {"type": "string", "examples": [".copilot-pull-request-description-instructions.md"]}}, "examples": [{"file": ".copilot-pull-request-description-instructions.md"}], "required": ["file"]}, {"type": "object", "markdownDescription": "Text instructions that will be added to Copilot requests that generate pull request titles and descriptions.", "properties": {"text": {"type": "string", "examples": ["Include every commit message in the pull request description."]}}, "required": ["text"], "examples": [{"text": "Include every commit message in the pull request description."}]}]}, "default": [], "markdownDescription": "A set of instructions that will be added to Copilot requests that generate pull request titles and descriptions.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Always include a list of key changes.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "examples": [[{"file": ".copilot-pull-request-description-instructions.md"}, {"text": "Use conventional commit message format."}]], "tags": ["experimental"]}, "github.copilot.chat.generateTests.codeLens": {"type": "boolean", "default": false, "description": "Show 'Generate tests' code lens for symbols that are not covered by current test coverage information.", "tags": ["experimental"]}, "github.copilot.chat.edits.temporalContext.enabled": {"type": "boolean", "default": false, "description": "When making edits request whether to include recently viewed and edited files with Copilot requests.", "tags": ["experimental", "onExp"]}, "github.copilot.chat.editor.temporalContext.enabled": {"type": "boolean", "default": false, "description": "When making inline chat request whether to include recently viewed and edited files with Copilot requests.", "tags": ["experimental", "onExp"]}, "github.copilot.chat.setupTests.enabled": {"type": "boolean", "default": true, "markdownDescription": "Enables the `/setupTests` intent and prompting in `/tests` generation.", "tags": ["experimental"]}, "github.copilot.chat.completionContext.typescript.mode": {"type": "string", "scope": "resource", "default": "off", "enum": ["off", "sidecar", "on"], "tags": ["experimental"], "markdownDescription": "The execution mode of the TypeScript Copilot context provider."}, "github.copilot.chat.languageContext.typescript.enabled": {"type": "boolean", "default": false, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "Enables the TypeScript language context provider for inline completions"}, "github.copilot.chat.languageContext.typescript.cacheTimeout": {"type": "number", "default": 500, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "The cache population timeout for the TypeScript language context provider in milliseconds. The default is 500 milliseconds."}, "github.copilot.chat.languageContext.fix.typescript.enabled": {"type": "boolean", "default": false, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "Enables the TypeScript language context provider for /fix commands"}, "github.copilot.chat.languageContext.inline.typescript.enabled": {"type": "boolean", "default": false, "scope": "resource", "tags": ["experimental", "onExP"], "markdownDescription": "Enables the TypeScript language context provider for inline chats (both generate and edit)"}, "github.copilot.chat.newWorkspaceCreation.enabled": {"type": "boolean", "default": true, "tags": ["experimental"], "description": "Whether to enable new agentic workspace creation."}, "github.copilot.chat.agent.currentEditorContext.enabled": {"type": "boolean", "default": true, "tags": ["experimental"], "description": "When enabled, Copilot will include the name of the current active editor in the context for agent mode."}, "github.copilot.chat.agent.terminal.allowList": {"type": "object", "default": {}, "additionalProperties": {"type": "boolean", "enum": [true, false], "enumDescriptions": ["Allow the pattern.", "Do not allow the pattern."], "description": "The start of a command to match against. A regular expression can be provided by wrapping the string in `/` characters."}, "tags": ["experimental"], "markdownDescription": "A list of commands or regular expressions that allow the run in terminal tool commands to run without explicit approval. These will be matched against the start of a command. A regular expression can be provided by wrapping the string in `/` characters.\n\nExamples:\n- `\"mkdir\"` Will allow all command lines starting with `mkdir`\n- `\"npm run build\"` Will allow all command lines starting with `npm run build`\n- `\"/^git (status|show\\b.*)$/\"` will allow `git status` and all command lines starting with `git show`\n- `\"/.*/\"` will allow all command lines\n\nThis will be overridden by anything that matches an entry in `#github.copilot.config.agent.terminal.denyList#`."}, "github.copilot.chat.agent.terminal.denyList": {"type": "object", "default": {"rm": true, "rmdir": true, "del": true, "kill": true, "curl": true, "wget": true, "eval": true, "chmod": true, "chown": true, "Remove-Item": true}, "additionalProperties": {"type": "boolean", "enum": [true, false], "enumDescriptions": ["Deny the pattern.", "Do not deny the pattern."], "description": "The start of a command to match against. A regular expression can be provided by wrapping the string in `/` characters."}, "tags": ["experimental"], "markdownDescription": "A list of commands or regular expressions that override matches in `#github.copilot.config.agent.terminal.allowList#` and force a command line to require explicit approval. This will be matched against the start of a command. A regular expression can be provided by wrapping the string in `/` characters.\n\nExamples:\n- `\"rm\"` will require explicit approval for any command starting with `rm`\n- `\"/^git (push|pull)/\"` will require explicit approval for any command starting with `git push` or `git pull` \n\nThis provides basic protection by preventing certain commands from running automatically, especially those a user would likely want to approve first. It is not intended as a comprehensive security measure or a defense against prompt injection."}, "github.copilot.chat.edits.newNotebook.enabled": {"type": "boolean", "default": true, "tags": ["experimental", "onExp"], "description": "Whether to enable the new notebook tool in Copilot Edits."}, "github.copilot.chat.notebook.followCellExecution.enabled": {"type": "boolean", "default": false, "tags": ["experimental"], "description": "Controls whether the currently executing cell is revealed into the viewport upon execution from Copilot."}, "github.copilot.chat.summarizeAgentConversationHistory.enabled": {"type": "boolean", "default": true, "tags": ["experimental", "onExp"], "description": "Whether to auto-summarize agent conversation history once the context window is filled."}}}], "submenus": [{"id": "copilot/reviewComment/additionalActions/applyAndNext", "label": "Apply and Go to Next"}, {"id": "copilot/reviewComment/additionalActions/discardAndNext", "label": "Discard and Go to Next"}, {"id": "copilot/reviewComment/additionalActions/discard", "label": "Discard"}, {"id": "github.copilot.chat.debug.filter", "label": "Filter", "icon": "$(filter)"}], "menus": {"chat/modelPicker": [{"command": "github.copilot.chat.manageModels", "when": "github.copilot.byokEnabled"}], "editor/title": [{"command": "github.copilot.debug.generateInlineEditTests", "when": "resourceScheme == 'ccreq'"}, {"command": "github.copilot.chat.notebook.enableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && !github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && !config.notebook.globalToolbar", "group": "navigation@10"}, {"command": "github.copilot.chat.notebook.disableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && !config.notebook.globalToolbar", "group": "navigation@10"}], "editor/context/chat": [{"command": "github.copilot.chat.explain", "when": "!github.copilot.interactiveSession.disabled", "group": "copilotAction@1"}, {"command": "github.copilot.chat.fix", "when": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "group": "copilotAction@2"}, {"command": "github.copilot.chat.review", "when": "github.copilot.chat.reviewSelection.enabled && !github.copilot.interactiveSession.disabled && resourceScheme != 'vscode-chat-code-block'", "group": "copilotAction@3"}, {"command": "github.copilot.chat.generateDocs", "when": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "group": "copilotAction@4"}, {"command": "github.copilot.chat.generateTests", "when": "!github.copilot.interactiveSession.disabled && !editor<PERSON><PERSON><PERSON><PERSON>", "group": "copilotAction@5"}], "terminal/context/chat": [{"command": "github.copilot.terminal.explainTerminalSelectionContextMenu", "group": "copilotAction@1"}, {"command": "github.copilot.terminal.attachTerminalSelection", "group": "zEditContext@1"}], "testing/item/result": [{"command": "github.copilot.tests.fixTestFailure.fromInline", "when": "testResultState == failed && !testResultOutdated", "group": "inline@2"}], "testing/item/context": [{"command": "github.copilot.tests.fixTestFailure.fromInline", "when": "testResultState == failed && !testResultOutdated", "group": "inline@2"}], "commandPalette": [{"command": "github.copilot.interactiveSession.feedback", "when": "github.copilot-chat.activated && !github.copilot.interactiveSession.disabled"}, {"command": "github.copilot.debug.workbenchState", "when": "true"}, {"command": "github.copilot.chat.rerunWithCopilotDebug", "when": "false"}, {"command": "github.copilot.chat.startCopilotDebugCommand", "when": "false"}, {"command": "github.copilot.terminal.explainTerminalSelectionContextMenu", "when": "false"}, {"command": "github.copilot.git.generateCommitMessage", "when": "false"}, {"command": "github.copilot.chat.explain", "when": "false"}, {"command": "github.copilot.chat.review", "when": "!github.copilot.interactiveSession.disabled"}, {"command": "github.copilot.chat.review.apply", "when": "false"}, {"command": "github.copilot.chat.review.applyAndNext", "when": "false"}, {"command": "github.copilot.chat.review.discard", "when": "false"}, {"command": "github.copilot.chat.review.discardAndNext", "when": "false"}, {"command": "github.copilot.chat.review.discardAll", "when": "false"}, {"command": "github.copilot.chat.review.stagedChanges", "when": "false"}, {"command": "github.copilot.chat.review.unstagedChanges", "when": "false"}, {"command": "github.copilot.chat.review.changes", "when": "false"}, {"command": "github.copilot.chat.review.changes.cancel", "when": "false"}, {"command": "github.copilot.chat.review.previous", "when": "false"}, {"command": "github.copilot.chat.review.next", "when": "false"}, {"command": "github.copilot.chat.review.continueInInlineChat", "when": "false"}, {"command": "github.copilot.chat.review.continueInChat", "when": "false"}, {"command": "github.copilot.chat.review.markHelpful", "when": "false"}, {"command": "github.copilot.chat.review.markUnhelpful", "when": "false"}, {"command": "github.copilot.devcontainer.generateDevContainerConfig", "when": "false"}, {"command": "github.copilot.tests.fixTestFailure", "when": "false"}, {"command": "github.copilot.tests.fixTestFailure.fromInline", "when": "false"}, {"command": "github.copilot.search.markHelpful", "when": "false"}, {"command": "github.copilot.search.markUnhelpful", "when": "false"}, {"command": "github.copilot.search.feedback", "when": "false"}, {"command": "github.copilot.chat.debug.showElements", "when": "false"}, {"command": "github.copilot.chat.debug.hideElements", "when": "false"}, {"command": "github.copilot.chat.debug.showTools", "when": "false"}, {"command": "github.copilot.chat.debug.hideTools", "when": "false"}, {"command": "github.copilot.chat.debug.exportLogItem", "when": "false"}, {"command": "github.copilot.chat.debug.exportPromptArchive", "when": "false"}, {"command": "github.copilot.chat.mcp.setup.check", "when": "false"}, {"command": "github.copilot.chat.mcp.setup.validatePackage", "when": "false"}, {"command": "github.copilot.chat.mcp.setup.flow", "when": "false"}], "view/title": [{"submenu": "github.copilot.chat.debug.filter", "when": "view == copilot-chat", "group": "navigation"}], "view/item/context": [{"command": "github.copilot.chat.debug.exportLogItem", "when": "view == copilot-chat && (viewItem == toolcall || viewItem == request)", "group": "export@1"}, {"command": "github.copilot.chat.debug.exportPromptArchive", "when": "view == copilot-chat && viewItem == chatprompt", "group": "export@2"}], "searchPanel/aiResults/commands": [{"command": "github.copilot.search.markHelpful", "group": "inline@0", "when": "aiResultsTitle && aiResultsRequested"}, {"command": "github.copilot.search.markUnhelpful", "group": "inline@1", "when": "aiResultsTitle && aiResultsRequested"}, {"command": "github.copilot.search.feedback", "group": "inline@2", "when": "aiResultsTitle && aiResultsRequested && github.copilot.debugReportFeedback"}], "comments/comment/title": [{"command": "github.copilot.chat.review.markHelpful", "group": "inline@0", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.markUnhelpful", "group": "inline@1", "when": "commentController == github-copilot-review"}], "commentsView/commentThread/context": [{"command": "github.copilot.chat.review.apply", "group": "context@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discard", "group": "context@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discardAll", "group": "context@3", "when": "commentController == github-copilot-review"}], "comments/commentThread/additionalActions": [{"submenu": "copilot/reviewComment/additionalActions/applyAndNext", "group": "inline@1", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments > 1"}, {"command": "github.copilot.chat.review.apply", "group": "inline@1", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments == 1"}, {"submenu": "copilot/reviewComment/additionalActions/discardAndNext", "group": "inline@2", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments > 1"}, {"submenu": "copilot/reviewComment/additionalActions/discard", "group": "inline@2", "when": "commentController == github-copilot-review && github.copilot.chat.review.numberOfComments == 1"}], "copilot/reviewComment/additionalActions/applyAndNext": [{"command": "github.copilot.chat.review.applyAndNext", "group": "inline@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.apply", "group": "inline@2", "when": "commentController == github-copilot-review"}], "copilot/reviewComment/additionalActions/discardAndNext": [{"command": "github.copilot.chat.review.discardAndNext", "group": "inline@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discard", "group": "inline@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.continueInInlineChat", "group": "inline@3", "when": "commentController == github-copilot-review"}], "copilot/reviewComment/additionalActions/discard": [{"command": "github.copilot.chat.review.discard", "group": "inline@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.continueInInlineChat", "group": "inline@3", "when": "commentController == github-copilot-review"}], "comments/commentThread/title": [{"command": "github.copilot.chat.review.previous", "group": "inline@1", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.next", "group": "inline@2", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.continueInChat", "group": "inline@3", "when": "commentController == github-copilot-review"}, {"command": "github.copilot.chat.review.discardAll", "group": "inline@4", "when": "commentController == github-copilot-review"}], "scm/title": [{"command": "github.copilot.chat.review.changes", "group": "navigation", "when": "github.copilot.chat.reviewDiff.enabled && !github.copilot.chat.review.sourceControlProgress && scmProvider == git && scmProviderRootUri in github.copilot.chat.reviewDiff.enabledRootUris"}, {"command": "github.copilot.chat.review.changes.cancel", "group": "navigation", "when": "github.copilot.chat.reviewDiff.enabled && github.copilot.chat.review.sourceControlProgress && scmProvider == git && scmProviderRootUri in github.copilot.chat.reviewDiff.enabledRootUris"}], "scm/inputBox": [{"command": "github.copilot.git.generateCommitMessage", "when": "scmProvider == git"}], "testing/message/context": [{"command": "github.copilot.tests.fixTestFailure", "when": "testing.testItemHasUri", "group": "inline@1"}], "debug/createConfiguration": [{"command": "github.copilot.debug.generateConfiguration", "group": "z_commands", "when": "config.github.copilot.chat.startDebugging.enabled"}], "issue/reporter": [{"command": "github.copilot.report"}], "github.copilot.chat.debug.filter": [{"command": "github.copilot.chat.debug.showElements", "when": "github.copilot.chat.debug.elementsHidden", "group": "commands@0"}, {"command": "github.copilot.chat.debug.hideElements", "when": "!github.copilot.chat.debug.elementsHidden", "group": "commands@0"}, {"command": "github.copilot.chat.debug.showTools", "when": "github.copilot.chat.debug.toolsHidden", "group": "commands@1"}, {"command": "github.copilot.chat.debug.hideTools", "when": "!github.copilot.chat.debug.toolsHidden", "group": "commands@1"}], "notebook/toolbar": [{"command": "github.copilot.chat.notebook.enableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && !github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && config.notebook.globalToolbar", "group": "navigation/execute@15"}, {"command": "github.copilot.chat.notebook.disableFollowCellExecution", "when": "config.github.copilot.chat.notebook.followCellExecution.enabled && github.copilot.notebookFollowInSessionEnabled && github.copilot.notebookAgentModeUsage && config.notebook.globalToolbar", "group": "navigation/execute@15"}]}, "icons": {"copilot-logo": {"description": "GitHub Copilot icon", "default": {"fontPath": "assets/copilot.woff", "fontCharacter": "\\0041"}}, "copilot-warning": {"description": "GitHub Copilot icon", "default": {"fontPath": "assets/copilot.woff", "fontCharacter": "\\0042"}}, "copilot-notconnected": {"description": "GitHub Copilot icon", "default": {"fontPath": "assets/copilot.woff", "fontCharacter": "\\0043"}}}, "iconFonts": [{"id": "copilot-font", "src": [{"path": "assets/copilot.woff", "format": "woff"}]}], "terminalQuickFixes": [{"id": "copilot-chat.fixWithCopilot", "commandLineMatcher": ".+", "commandExitResult": "error", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": ".+", "offset": 0}, "kind": "explain"}, {"id": "copilot-chat.generateCommitMessage", "commandLineMatcher": "git add .+", "commandExitResult": "success", "kind": "explain", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": ".+", "offset": 0}}, {"id": "copilot-chat.terminalToDebugging", "commandLineMatcher": ".+", "kind": "explain", "commandExitResult": "error", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": "", "offset": 0}}, {"id": "copilot-chat.terminalToDebuggingSuccess", "commandLineMatcher": ".+", "kind": "explain", "commandExitResult": "success", "outputMatcher": {"anchor": "bottom", "length": 1, "lineMatcher": "", "offset": 0}}], "languages": [{"id": "ignore", "filenamePatterns": [".copilotignore"], "aliases": []}, {"id": "markdown", "extensions": [".copilotmd"]}], "views": {"copilot-chat": [{"id": "copilot-chat", "name": "Co<PERSON><PERSON> Chat Debug", "icon": "assets/debug-icon.svg", "when": "github.copilot.chat.showLogView"}]}, "viewsContainers": {"activitybar": [{"id": "copilot-chat", "title": "Co<PERSON><PERSON> Chat Debug", "icon": "assets/debug-icon.svg"}]}, "configurationDefaults": {"workbench.editorAssociations": {"*.copilotmd": "vscode.markdown.preview.editor"}}, "keybindings": [{"command": "github.copilot.chat.rerunWithCopilotDebug", "key": "ctrl+alt+.", "mac": "cmd+alt+.", "when": "github.copilot-chat.activated && terminalShellIntegrationEnabled && terminalFocus && !terminalAltBufferActive"}], "walkthroughs": [{"id": "copilotWelcome", "title": "GitHub Copilot", "description": "Your AI pair programmer to write code faster and smarter", "when": "!isWeb", "steps": [{"id": "copilot.setup.signIn", "title": "Sign in to use Copilot for free", "description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.\n\n[Use Copilot for Free](command:workbench.action.chat.triggerSetup)", "when": "chatEntitlementSignedOut && !view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.disabled && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "The user invokes @workspace in the Copilot Chat panel in the secondary sidebar to understand the code base. Copilot retrieves the relevant information and provides a response with links to the files"}}, {"id": "copilot.setup.signInNoAction", "title": "Sign in to use Copilot for free", "description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.", "when": "chatEntitlementSignedOut && view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && !github.copilot.interactiveSession.individual.disabled && !github.copilot.interactiveSession.individual.expired && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "The user invokes @workspace in the Copilot Chat panel in the secondary sidebar to understand the code base. Copilot retrieves the relevant information and provides a response with links to the files"}}, {"id": "copilot.setup.signUp", "title": "Get started with Copilot for free", "description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.\n\n[Use Copilot for Free](command:workbench.action.chat.triggerSetup)", "when": "chatPlanCanSignUp && !view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && (github.copilot.interactiveSession.individual.disabled || github.copilot.interactiveSession.individual.expired) && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "The user invokes @workspace in the Copilot Chat panel in the secondary sidebar to understand the code base. Copilot retrieves the relevant information and provides a response with links to the files"}}, {"id": "copilot.setup.signUpNoAction", "title": "Get started with Copilot for free", "description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.", "when": "chatPlanCanSignUp && view.workbench.panel.chat.view.copilot.visible && !github.copilot-chat.activated && !github.copilot.offline && (github.copilot.interactiveSession.individual.disabled || github.copilot.interactiveSession.individual.expired) && !github.copilot.interactiveSession.enterprise.disabled && !github.copilot.interactiveSession.contactSupport", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "The user invokes @workspace in the Copilot Chat panel in the secondary sidebar to understand the code base. Copilot retrieves the relevant information and provides a response with links to the files"}}, {"id": "copilot.panelChat", "title": "Chat about your code", "description": "Ask Copilot programming questions or get help with your code using **@workspace**.\n Type **@** to see all available chat participants that you can chat with directly, each with their own expertise.\n[Chat with Copilot](command:workbench.action.chat.open?%7B%22mode%22%3A%22ask%22%7D)", "when": "!chatEntitlementSignedOut || chatIsEnabled ", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/workspace-hclight.mp4"}, "altText": "The user invokes @workspace in the Copilot Chat panel in the secondary sidebar to understand the code base. Copilot retrieves the relevant information and provides a response with links to the files"}}, {"id": "copilot.edits", "title": "Make changes using natural language", "description": "Use **Copilot Edits** to select files you want to work with and describe changes you want to make. Copilot applies them directly to your files.\n[Edit with Copilot](command:workbench.action.chat.open?%7B%22mode%22%3A%22edit%22%7D)", "when": "!chatEntitlementSignedOut || chatIsEnabled ", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/edits-hclight.mp4"}, "altText": "The video shows the user dragging and dropping files into the Copilot Edits input box located in the secondary sidebar. Copilot then updates the file according to the user’s request"}}, {"id": "copilot.firstSuggest", "title": "AI-suggested code completions", "description": "As you type in the editor, <PERSON><PERSON><PERSON> suggests code to help you complete what you started.", "when": "!chatEntitlementSignedOut || chatIsEnabled ", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/ghost-text-hclight.mp4"}, "altText": "The video shows different Copilot completions, where <PERSON><PERSON><PERSON> suggests code to help the user complete their code"}}, {"id": "copilot.inlineChatNotMac", "title": "Use natural language in your files", "description": "Sometimes, it's easier to describe the code you want to write directly within a file.\nPlace your cursor or make a selection and use **``Ctrl+I``** to open **Inline Chat**.", "when": "!isMac && (!chatEntitlementSignedOut || chatIsEnabled )", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hclight.mp4"}, "altText": "Inline Chat view in the editor. The video shows the user invoking the inline chat widget and asking <PERSON><PERSON><PERSON> to make a change in the file using natural language. <PERSON><PERSON><PERSON> then makes the requested change"}}, {"id": "copilot.inlineChatMac", "title": "Use natural language in your files", "description": "Sometimes, it's easier to describe the code you want to write directly within a file.\nPlace your cursor or make a selection and use **``Cmd+I``** to open **Inline Chat**.", "when": "isMac && (!chatEntitlementSignedOut || chatIsEnabled )", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/inline-hclight.mp4"}, "altText": "The video shows the user invoking the inline chat widget and asking <PERSON><PERSON><PERSON> to make a change in the file using natural language. <PERSON><PERSON><PERSON> then makes the requested change"}}, {"id": "copilot.sparkle", "title": "Look out for smart actions", "description": "Copilot enhances your coding experience with AI-powered smart actions throughout the VS Code interface.\nLook for $(sparkle) icons, such as in the [Source Control view](command:workbench.view.scm), where <PERSON><PERSON><PERSON> generates commit messages and PR descriptions based on code changes.\n\n[Discover Tips and Tricks](https://code.visualstudio.com/docs/copilot/copilot-vscode-features)", "when": "!chatEntitlementSignedOut || chatIsEnabled", "media": {"video": {"dark": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit.mp4", "light": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit-light.mp4", "hc": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit-hc.mp4", "hcLight": "https://vscodewalkthroughs.z1.web.core.windows.net/v0.26/git-commit-hclight.mp4"}, "altText": "The video shows the sparkle icon in the source control input box being clicked, triggering GitHub Copilot to generate a commit message automatically"}}]}], "jsonValidation": [{"fileMatch": "settings.json", "url": "ccsettings://root/schema.json"}], "typescriptServerPlugins": [{"name": "@vscode/copilot-typescript-server-plugin", "enableForWorkspaceTypeScriptVersions": true}]}, "extensionPack": ["GitHub.copilot"], "prettier": {"useTabs": true, "tabWidth": 4, "singleQuote": true}, "overrides": {"@aminya/node-gyp-build": "npm:node-gyp-build@4.8.1", "string_decoder": "npm:string_decoder@1.2.0", "node-gyp": "npm:node-gyp@10.3.1"}, "isPreRelease": false, "originalEnabledApiProposals": ["extensionsAny", "newSymbolNamesProvider", "interactive", "codeActionAI", "activeComment", "commentReveal", "contribCommentThreadAdditionalMenu", "contribCommentsViewThreadMenus", "documentFiltersExclusive", "embeddings", "findTextInFiles", "findTextInFiles2", "findFiles2@2", "textSearchProvider", "terminalDataWriteEvent", "terminalExecuteCommandEvent", "terminalSelection", "terminalQuickFixProvider", "mappedEditsProvider", "aiRelatedInformation", "chatParticipantAdditions", "chatEditing", "defaultChatParticipant@4", "contribSourceControlInputBoxMenu", "authLearnMore", "testObserver", "aiTextSearchProvider@2", "chatParticipantPrivate@9", "chat<PERSON>rovider", "contribDebugCreateConfiguration", "chatReferenceDiagnostic", "textSearchProvider2", "chatReferenceBinaryData", "languageModelSystem", "languageModelCapabilities", "inlineCompletionsAdditions", "languageModelDataPart@3", "chatStatusItem", "taskProblemMatcherStatus", "contribLanguageModelToolSets", "textDocumentChangeReason", "resolvers"]}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.29.1", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "GitHub", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1754240329513, "pinned": false, "source": "gallery", "id": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 28918228}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "njpwerner.autodocstring", "uuid": "2d6fea35-f68e-461d-9b7b-5cd05be99451"}, "manifest": {"name": "autodocstring", "displayName": "autoDocstring - Python Docstring Generator", "description": "Generates python docstrings automatically", "version": "0.6.1", "publisher": "njpwerner", "license": "SEE LICENSE IN LICENSE", "icon": "images/icon.png", "extensionKind": ["workspace"], "repository": {"type": "git", "url": "https://github.com/NilsJPWerner/autoDocstring"}, "bugs": {"url": "https://github.com/NilsJPWerner/autoDocstring/issues"}, "categories": ["Snippets", "Formatters", "Programming Languages"], "keywords": ["python", "docstring", "google", "numpy", "sphinx", "generator", "autodocstring", "doc<PERSON>r", "documentation", "pydoc<PERSON><PERSON>"], "galleryBanner": {}, "engines": {"vscode": "^1.53.0"}, "activationEvents": ["onLanguage:python", "onLanguage:starlark"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "autoDocstring.generateDocstring", "title": "Generate Docstring"}], "keybindings": [{"command": "autoDocstring.generateDocstring", "key": "ctrl+shift+2", "mac": "cmd+shift+2", "when": "editorTextFocus"}], "menus": {"editor/context": [{"when": "resourceLangId == python", "command": "autoDocstring.generateDocstring", "group": "1_modification"}]}, "configuration": {"type": "object", "title": "Python Docstring Generator configuration", "properties": {"autoDocstring.docstringFormat": {"type": "string", "default": "google", "enum": ["doc<PERSON>r", "pep257", "google", "google-notypes", "sphinx", "sphinx-notypes", "numpy", "numpy-notypes", "one-line-sphinx"], "description": "Which docstring format to use."}, "autoDocstring.customTemplatePath": {"type": "string", "default": "", "description": "Path to custom docstring template (overrides docstringFormat). Path can be absolute or relative to the project root."}, "autoDocstring.generateDocstringOnEnter": {"type": "boolean", "default": true, "description": "Generate docstring on pressing enter after opening a docstring (use quoteStyle)"}, "autoDocstring.includeExtendedSummary": {"type": "boolean", "default": false, "description": "Include [extended_summary] tag"}, "autoDocstring.includeName": {"type": "boolean", "default": false, "description": "Include function names at the start of docstrings"}, "autoDocstring.startOnNewLine": {"type": "boolean", "default": false, "description": "Start docstring on new line"}, "autoDocstring.guessTypes": {"type": "boolean", "default": true, "description": "Guess the type of parameters and return values"}, "autoDocstring.quoteStyle": {"type": "string", "default": "\"\"\"", "enum": ["\"\"\"", "'''"], "description": "Style of quote used with generate docstring command"}, "autoDocstring.logLevel": {"type": "string", "default": "Info", "enum": ["None", "Info", "Debug"], "scope": "window", "description": "Output log information"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "copy_templates": "copyfiles --up 1 \"src/docstring/templates/*.mustache\" out/", "copy_python_test_files": "copyfiles --up 1 \"src/test/integration/python_test_files/*\" out/", "compile": "npm run copy_templates && tsc -p ./", "watch": "npm run copy_templates && tsc -watch -p ./", "unit_test": "mocha -r ts-node/register \"src/test/**/*.spec.ts\"", "prepare_integration_tests": "npm run copy_python_test_files && npm run compile", "integration_test": "node out/test/run_integration_tests.js"}, "devDependencies": {"@types/chai": "^4.3.0", "@types/mocha": "^9.1.0", "@types/mustache": "^4.1.2", "@types/node": "^17.0.17", "@types/vscode": "^1.53.0", "@vscode/test-electron": "^2.1.2", "chai": "^4.3.6", "copyfiles": "^2.4.1", "mocha": "^9.2.0", "ts-node": "^10.5.0", "typescript": "^4.5.5"}, "dependencies": {"@types/stack-trace": "^0.0.29", "mustache": "^4.2.0", "stack-trace": "^0.0.10", "ts-dedent": "^2.2.0"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/njpwerner.autodocstring-0.6.1", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "<PERSON><PERSON>", "metadata": {"installedTimestamp": 1754245443987, "source": "gallery", "id": "2d6fea35-f68e-461d-9b7b-5cd05be99451", "publisherId": "b32683f0-ccf4-4db1-9dc3-8873e1e045c0", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 667282}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "teamsdevapp.vscode-ai-foundry", "uuid": "2554eed3-bb63-46ed-9a7f-880a95dbd2a5"}, "manifest": {"name": "vscode-ai-foundry", "displayName": "Azure AI Foundry", "description": "Visual Studio Code extension for Azure AI Foundry", "version": "0.8.0", "preview": true, "publisher": "TeamsDevApp", "author": "Microsoft Corporation", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"vscode": "^1.95.0"}, "icon": "icons/logo.png", "license": "MIT", "categories": ["Azure", "Other"], "repository": {"type": "git", "url": "https://github.com/microsoft/ai-foundry-for-vscode"}, "bugs": {"url": "https://github.com/microsoft/ai-foundry-for-vscode/issues"}, "extensionDependencies": ["ms-azuretools.vscode-azureresourcegroups", "ms-windows-ai-studio.windows-ai-studio"], "activationEvents": ["onView:azureResourceGroups", "onView:azureResourceGroupsV2", "workspaceContains:**/*.agent.yaml", "workspaceContains:**/*.agent.yml"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "azure-ai-foundry", "title": "Azure AI Foundry", "icon": "icons/dark/foundry.svg"}]}, "views": {"azure-ai-foundry": [{"id": "azure-ai-foundry-resources", "name": "Resources"}, {"id": "azure-ai-foundry-tools", "name": "Tools"}, {"id": "azure-ai-foundry-help-and-feedback", "name": "Help and Feedback"}]}, "viewsWelcome": [{"view": "azure-ai-foundry-resources", "contents": "To begin using the Azure AI Foundry Extension, please set a default project in your Azure extension, or create a new project.\n\n[Set Default Project](command:azure-ai-foundry.commandPalette.setDefault)\n\n[Create Project](command:azure-ai-foundry.commandPalette.createProject)", "enablement": "azure-ai-foundry.initialized"}], "x-azResources": {"azure": {"branches": [{"type": "AiFoundry"}]}, "commands": [{"command": "azure-ai-foundry.llmDeploy", "title": "Deploy a LLM model"}], "activation": {"onFetch": ["microsoft.machinelearningservices/workspaces"], "onResolve": ["microsoft.machinelearningservices/workspaces"]}}, "commands": [{"command": "azure-ai-foundry.commandPalette.setDefault", "title": "Set Default Project", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.clearDefault", "title": "Clear Default Project", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.createProject", "title": "Create Project", "category": "Azure AI Foundry", "icon": "$(add)"}, {"command": "azure-ai-foundry.commandPalette.llmDeploy", "title": "Deploy a new AI model", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.viewContext.llmDeploy", "title": "Deploy new AI model", "icon": "$(add)"}, {"command": "azure-ai-foundry.viewContext.createAgent", "title": "Create new agent", "icon": "$(add)"}, {"command": "azure-ai-foundry.viewContext.createBingConnection", "title": "Create new Bing Connection", "category": "Azure AI Foundry", "icon": "$(add)"}, {"command": "azure-ai-foundry.viewContext.createVectorStore", "title": "Create new vector store", "category": "Azure AI Foundry", "icon": "$(add)"}, {"command": "azure-ai-foundry.viewContext.model.copyEndpoint", "title": "Copy Endpoint"}, {"command": "azure-ai-foundry.viewContext.model.copyApiKey", "title": "Copy API Key"}, {"command": "azure-ai-foundry.viewContext.model.delete", "title": "Delete"}, {"command": "azure-ai-foundry.viewContext.agent.delete", "title": "Delete"}, {"command": "azure-ai-foundry.commandPalette.model.copyEndpoint", "title": "Copy Model Endpoint", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.viewContext.agent.copyEndpoint", "title": "Copy Agent Endpoint", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.viewContext.agent.copyConnectionString", "title": "Copy Agent Connection String", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.model.copyApiKey", "title": "Copy Model API Key", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.model.delete", "title": "Delete Model Deployment", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.model.edit", "title": "Edit Model Deployment", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.openModelCodeFile", "title": "Open Model Code File", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.openAgentCodeFile", "title": "Open Agent Code File", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.viewContext.openModelCodeFile", "title": "Open Code File"}, {"command": "azure-ai-foundry.viewContext.openAgentCodeFile", "title": "Open Code File"}, {"command": "azure-ai-foundry.viewContext.model.openInPlayground", "title": "Open in Playground"}, {"command": "azure-ai-foundry.openAIFoundry", "title": "Open in Azure AI Foundry"}, {"command": "azure-ai-foundry.viewContext.viewModel", "title": "Model Details"}, {"command": "azure-ai-foundry.commandPalette.openCatalog", "title": "Open Model Catalog", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.commandPalette.runAgent", "title": "Open Playground", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.explorerContext.openAgentDesigner", "title": "Open Agent Designer", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.editorTitle.openAgentDesigner", "title": "Open Agent Designer", "icon": {"light": "icons/light/foundry.svg", "dark": "icons/dark/foundry.svg"}}, {"command": "azure-ai-foundry.openInVSCodeAzure", "title": "Open Sample in vscode.dev/azure"}, {"command": "azure-ai-foundry.viewContext.openInFoundryView", "title": "Open in Azure AI Foundry Extension"}, {"command": "azure-ai-foundry.viewContext.viewThreadDetails", "title": "View Thread Details"}, {"command": "azure-ai-foundry.viewContext.loadMoreThreads", "title": "Load More Threads"}, {"command": "azure-ai-foundry.viewContext.refreshResources", "title": "Refresh", "icon": "$(refresh)"}, {"command": "azure-ai-foundry.reportIssue", "title": "Report Issue...", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.viewContext.switchDefaultProject", "title": "Switch Default Project in Azure Extension", "icon": "$(list-filter)"}, {"command": "azure-ai-foundry.viewContext.refreshNode", "title": "Refresh"}, {"command": "azure-ai-foundry.commandPalette.deleteThread", "title": "Delete", "category": "Azure AI Foundry"}, {"command": "azure-ai-foundry.listModels", "title": "List Models", "category": "Azure AI Foundry"}], "menus": {"view/title": [{"command": "azure-ai-foundry.viewContext.refreshResources", "title": "Refresh", "group": "navigation@3", "when": "view == azure-ai-foundry-resources"}, {"command": "azure-ai-foundry.commandPalette.createProject", "title": "Create Project", "group": "navigation@3", "when": "view == azure-ai-foundry-resources"}], "view/item/context": [{"command": "azure-ai-foundry.viewContext.llmDeploy", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIModelRoot", "group": "inline"}, {"command": "azure-ai-foundry.viewContext.createAgent", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIAgentRoot", "group": "inline"}, {"command": "azure-ai-foundry.viewContext.createBingConnection", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIConnectionRoot", "group": "inline"}, {"command": "azure-ai-foundry.viewContext.createVectorStore", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIVectorStoreRoot", "group": "inline"}, {"command": "azure-ai-foundry.viewContext.llmDeploy", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIModelRoot", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.createAgent", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIAgentRoot", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.createBingConnection", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIConnectionRoot", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.createVectorStore", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIVectorStoreRoot", "group": "1@1"}, {"command": "azure-ai-foundry.commandPalette.runAgent", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIAgentItem/", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.agent.delete", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIAgentItem/", "group": "9@1"}, {"command": "azure-ai-foundry.viewContext.openModelCodeFile", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIModelItem/", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.openAgentCodeFile", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIAgentItem/", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.model.openInPlayground", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIModelItem/", "group": "1@2"}, {"command": "azure-ai-foundry.viewContext.model.copyApiKey", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIModelItem/", "group": "3@1"}, {"command": "azure-ai-foundry.viewContext.model.copyEndpoint", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIModelItem/", "group": "3@2"}, {"command": "azure-ai-foundry.viewContext.agent.copyEndpoint", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIAgentItemFoundryProject/", "group": "3@1"}, {"command": "azure-ai-foundry.viewContext.agent.copyConnectionString", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIAgentItemHubBasedProject/", "group": "3@1"}, {"command": "azure-ai-foundry.viewContext.model.delete", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIModelItem/", "group": "9@1"}, {"command": "azure-ai-foundry.viewContext.openInFoundryView", "when": "view == azureResourceGroups && viewItem =~ /AzureAIProject/", "group": "2@1"}, {"command": "azure-ai-foundry.viewContext.switchDefaultProject", "when": "view == azure-ai-foundry-resources && viewItem == AzureAIProject", "group": "1@1"}, {"command": "azure-ai-foundry.viewContext.switchDefaultProject", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIProject/", "group": "inline"}, {"command": "azure-ai-foundry.viewContext.refreshNode", "when": "view == azure-ai-foundry-resources && (viewItem =~ /AzureAI(Project|.*Root)/)", "group": "9@1"}, {"command": "azure-ai-foundry.commandPalette.deleteThread", "when": "view == azure-ai-foundry-resources && viewItem =~ /AzureAIThreadItem/", "group": "1@1"}], "commandPalette": [{"command": "azure-ai-foundry.openInVSCodeAzure", "when": "false"}, {"command": "azure-ai-foundry.viewContext.llmDeploy", "when": "false"}, {"command": "azure-ai-foundry.viewContext.model.delete", "when": "false"}, {"command": "azure-ai-foundry.commandPalette.model.edit", "when": "false"}, {"command": "azure-ai-foundry.viewContext.agent.delete", "when": "false"}, {"command": "azure-ai-foundry.viewContext.model.copyEndpoint", "when": "false"}, {"command": "azure-ai-foundry.viewContext.model.copyApiKey", "when": "false"}, {"command": "azure-ai-foundry.viewContext.agent.copyEndpoint", "when": "false"}, {"command": "azure-ai-foundry.viewContext.agent.copyConnectionString", "when": "false"}, {"command": "azure-ai-foundry.viewContext.openModelCodeFile", "when": "false"}, {"command": "azure-ai-foundry.viewContext.openAgentCodeFile", "when": "false"}, {"command": "azure-ai-foundry.openAIFoundry", "when": "false"}, {"command": "azure-ai-foundry.viewContext.model.openInPlayground", "when": "false"}, {"command": "azure-ai-foundry.viewContext.viewModel", "when": "false"}, {"command": "azure-ai-foundry.commandPalette.runAgent", "when": "false"}, {"command": "azure-ai-foundry.viewContext.createAgent", "when": "false"}, {"command": "azure-ai-foundry.explorerContext.openAgentDesigner", "when": "false"}, {"command": "azure-ai-foundry.editorTitle.openAgentDesigner", "when": "false"}, {"command": "azure-ai-foundry.viewContext.loadMoreThreads", "when": "false"}, {"command": "azure-ai-foundry.viewContext.refreshResources", "when": "false"}, {"command": "azure-ai-foundry.viewContext.viewThreadDetails", "when": "false"}, {"command": "azure-ai-foundry.viewContext.openInFoundryView", "when": "false"}, {"command": "azure-ai-foundry.viewContext.switchDefaultProject", "when": "false"}, {"command": "azure-ai-foundry.viewContext.refreshNode", "when": "false"}, {"command": "azure-ai-foundry.commandPalette.deleteThread", "when": "false"}], "explorer/context": [{"command": "azure-ai-foundry.explorerContext.openAgentDesigner", "when": "resourceLangId == yaml && resourceFilename =~ /\\.agent\\.(yaml|yml)$/"}], "editor/title": [{"command": "azure-ai-foundry.editorTitle.openAgentDesigner", "when": "resourceLangId == yaml && resourceFilename =~ /\\.agent\\.(yaml|yml)$/", "group": "navigation@1"}]}}, "scripts": {"vscode:prepublish": "npm run clean && npm run package && cd language-server && npm run vscode:prepublish && cd .. && cd schema-validator && npm run vscode:prepublish && cd ..", "compile": "webpack", "build": "tsc", "clean": "rimraf ./dist && rimraf ./out", "watch": "npm run build:webview && tsc -watch -p .", "package": "npm run build:webview && webpack --mode production --devtool hidden-source-map", "pretest": "npm run build && npm run lint", "lint": "eslint src", "unittest": "jest", "test": "vscode-test", "test:coverage": "jest --coverage", "ui-test": "playwright test -c ./src/test/ui", "prepare": "husky", "preinstall": "cd webview-ui && npm install", "postinstall": "cd language-server && npm install && cd .. && cd schema-validator && npm install && cd ..", "build:webview": "cd webview-ui && npm run build"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@playwright/test": "^1.42.1", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.13", "@types/mustache": "^4.2.6", "@types/node": "22.x", "@types/qs": "^6.9.18", "@types/sinon": "^17.0.3", "@types/vscode": "^1.95.0", "@typescript-eslint/eslint-plugin": "^8.10.0", "@typescript-eslint/parser": "^8.7.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "chai": "^4.2.0", "copy-webpack-plugin": "^12.0.2", "eslint": "^9.13.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-mock-vscode": "^4.4.0", "lint-staged": "^15.2.10", "nyc": "^15.1.0", "prettier": "^3.4.1", "rimraf": "^6.0.1", "sass-embedded": "^1.89.0", "sinon": "^19.0.2", "ts-jest": "^29.3.4", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "webpack": "^5.95.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@azure/ai-agents": "1.0.0-alpha.20250505.3", "@azure/ai-projects": "1.0.0-alpha.20250505.4", "@azure/arm-cognitiveservices": "^7.5.0", "@azure/arm-machinelearning": "^3.0.0", "@azure/arm-resources": "^6.0.0", "@azure/identity": "^4.5.0", "@azure/ms-rest-azure-js": "^2.1.0", "@azure/opentelemetry-instrumentation-azure-sdk": "^1.0.0-beta.8", "@microsoft/vscode-azext-azureauth": "^3.0.0", "@microsoft/vscode-azext-utils": "^2.5.11", "@microsoft/vscode-azureresources-api": "^2.4.0", "axios": "^1.8.2", "fast-safe-stringify": "^2.1.1", "fs-extra": "^11.2.0", "inversify": "^6.1.4", "inversify-inject-decorators": "^3.1.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "mustache": "^4.2.0", "qs": "^6.14.0", "reflect-metadata": "^0.2.2", "vscode-jsonrpc": "^8.2.1", "vscode-languageclient": "^9.0.1", "yaml": "^2.8.0"}, "lint-staged": {"*.{js,ts,json,mjs}": ["prettier --write"]}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "reporter": ["lcov", "html", "text", "json-summary", "cobertura"], "include": ["src/**/*.ts"], "exclude": ["src/test/**/*.ts"], "extension": [".ts"], "all": true}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/teamsdevapp.vscode-ai-foundry-0.8.0", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Microsoft", "metadata": {"installedTimestamp": 1754245445244, "source": "gallery", "id": "2554eed3-bb63-46ed-9a7f-880a95dbd2a5", "publisherId": "e3c289db-0715-45e7-94ad-71c2cd459a8a", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": true, "size": 16539188}, "isValid": true, "validations": [], "preRelease": true}, {"type": 1, "identifier": {"id": "ms-azuretools.vscode-azureresourcegroups", "uuid": "260e9ed1-160d-4f7e-807c-2eb076ba3846"}, "manifest": {"name": "vscode-azureresourcegroups", "displayName": "Azure Resources", "description": "An extension for viewing and managing Azure resources.", "version": "0.11.1", "publisher": "ms-azuretools", "icon": "resources/resourceGroup.png", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"vscode": "^1.95.0"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-azureresourcegroups"}, "galleryBanner": {"color": "#3a3d41", "theme": "dark"}, "homepage": "https://github.com/Microsoft/vscode-azureresourcegroups/blob/main/README.md", "license": "SEE LICENSE IN LICENSE.md", "categories": ["Azure"], "keywords": ["Azure", "Resource Groups", "multi-root ready"], "preview": true, "activationEvents": ["onFileSystem:azureResourceGroups", "onWalkthrough:azure-get-started", "onTerminalProfile:azureResourceGroups.cloudShellBash", "onTerminalProfile:azureResourceGroups.cloudShellPowerShell"], "main": "./main.js", "contributes": {"chatParticipants": [{"id": "ms-azuretools.azure-agent-stand-in", "name": "azure", "fullName": "Azure", "description": "GitHub Copilot for Azure", "isSticky": false, "when": "config.azureResourceGroups.enableChatStandIn", "disambiguation": [{"category": "azure_learn", "description": "The user has a conceptual question about Azure.", "examples": ["What is the difference between a blob container and a file share?", "Does A<PERSON> let me choose where my resources are located?", "What are the pros and cons of function apps vs container apps?", "How does Azure compare to other cloud offerings?"]}, {"category": "azure_resources", "description": "The user wants information about their Azure resources.", "examples": ["What is the application URL of my container app?", "How many of my virtual machines are currently in a running state?", "How many cosmos databases have I created in west us 2?"]}, {"category": "azure_troubleshoot", "description": "The user wants help troubleshooting issues.", "examples": ["How many failed requests did my app receive in the last hour?", "Why is my app service slow?"]}, {"category": "azure_deploy", "description": "The user wants to deploy to Azure.", "examples": ["Help me deploy my azd template to the cloud", "I'd like to create and deploy an AI image generator app", "How can I deploy this project to Azure?"]}, {"category": "azure_cost", "description": "The user asks a question about costs incurred by Azure resources.", "examples": ["Show me my Azure costs of the last month", "How much did I spend in Azure this month so far?", "Show me my Azure costs in the past 3 months and compare them.", "What's the cost of my App Service in January, February and March?"]}, {"category": "azure_template", "description": "The user wants to create a new web app in Azure from a template.", "examples": ["I want to create a new web app in Azure with Python and MongoDB"]}]}], "languageModelTools": [{"displayName": "Azure Resources: Get Azure Activity Log", "icon": "$(azure)", "inputSchema": {}, "modelDescription": "Gets the Azure activity log", "name": "azureResources_getAzureActivityLog", "canBeReferencedInPrompt": true, "toolReferenceName": "azureActivityLog", "tags": ["azure"]}], "terminal": {"profiles": [{"id": "azureResourceGroups.cloudShellBash", "title": "Azure Cloud Shell (Bash)", "icon": "$(azure)"}, {"id": "azureResourceGroups.cloudShellPowerShell", "title": "Azure Cloud Shell (PowerShell)", "icon": "$(azure)"}]}, "x-azResources": {"commands": [{"command": "azureResourceGroups.createResourceGroup", "title": "Create Resource Group...", "detail": "The logical grouping for your resources.", "type": "ResourceGroup"}]}, "commands": [{"command": "azureResourceGroups.uploadFileCloudConsole", "title": "Upload to Cloud Shell", "category": "Azure"}, {"command": "azureResourceGroups.signInToTenant", "title": "Sign in to Tenant (Directory)...", "category": "Azure"}, {"command": "azureFocusView.refreshTree", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureFocusView.refresh", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureResourceGroups.focusGroup", "title": "Focus Group", "category": "Azure", "icon": "$(zoom-in)"}, {"command": "azureResourceGroups.loadAllSubscriptionRoleAssignments", "title": "Load all role assignments across subscriptions...", "category": "Azure", "icon": "$(sync)"}, {"command": "azureResourceGroups.unfocusGroup", "title": "Unfocus Group", "category": "Azure", "icon": "$(close)"}, {"command": "azureResourceGroups.logIn", "title": "Sign In", "category": "Azure"}, {"command": "azureTenantsView.addAccount", "title": "Add account", "category": "Azure", "icon": "$(add)"}, {"command": "azureResourceGroups.selectSubscriptions", "title": "Select Subscriptions...", "category": "Azure", "icon": "$(filter)"}, {"command": "azureResourceGroups.createResourceGroup", "title": "Create Resource Group...", "category": "Azure"}, {"command": "azureResourceGroups.installExtension", "title": "Install Azure extension", "icon": "$(extensions)", "category": "Azure"}, {"command": "azureResourceGroups.createResource", "title": "Create Resource...", "category": "Azure", "icon": "$(add)"}, {"command": "azureResourceGroups.deleteResourceGroupV2", "title": "Delete Resource Group...", "category": "Azure"}, {"command": "azureResourceGroups.editTags", "title": "Edit Tags...", "category": "Azure"}, {"command": "azureResourceGroups.loadMore", "title": "Load More", "category": "Azure"}, {"command": "azureResourceGroups.openInPortal", "title": "Open in Portal", "category": "Azure"}, {"command": "azureResourceGroups.refreshTree", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureResourceGroups.refresh", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureResourceGroups.revealResource", "title": "Reveal Resource", "category": "Azure"}, {"command": "azureResourceGroups.viewProperties", "title": "View Properties", "category": "Azure"}, {"command": "azureResourceGroups.reportIssue", "title": "Report Issue...", "category": "Azure"}, {"command": "ms-azuretools.getStarted", "title": "Get Started...", "category": "Azure"}, {"command": "ms-azuretools.reportIssue", "title": "Report Issue...", "category": "Azure"}, {"command": "ms-azuretools.reviewIssues", "title": "Review Issues...", "category": "Azure"}, {"command": "azureResourceGroups.groupBy.resourceGroup", "title": "Group by Resource Group", "category": "Azure", "icon": "$(gear)"}, {"command": "azureResourceGroups.groupBy.resourceType", "title": "Group by Resource Type", "category": "Azure", "icon": "$(gear)"}, {"command": "azureResourceGroups.groupBy.location", "title": "Group by Location", "category": "Azure", "icon": "$(gear)"}, {"command": "azureResourceGroups.groupBy.armTag", "title": "Group by ARM Tag...", "category": "Azure", "icon": "$(gear)"}, {"command": "azureWorkspace.refresh", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureWorkspace.refreshTree", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureResourceGroups.clearActivities", "title": "Clear Completed Activities", "category": "Azure", "icon": "$(clear-all)"}, {"command": "azureResourceGroups.showGroupOptions", "title": "Show Grouping Options", "category": "Azure"}, {"command": "azureResourceGroups.askAzure", "title": "Ask @azure", "category": "Azure"}, {"command": "azureResourceGroups.askAgentAboutActivityLog", "title": "Ask Copilot about the Azure Activity Log", "category": "Azure", "icon": "$(copilot)"}, {"command": "azureTenantsView.refresh", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureTenantsView.refreshTree", "title": "Refresh", "category": "Azure", "icon": "$(refresh)"}, {"command": "azureTenantsView.signInToTenant", "title": "Sign in to Tenant", "category": "Azure", "icon": "$(sign-in)"}, {"command": "azureTenantsView.configureSovereignCloud", "title": "Configure Sovereign Cloud", "category": "Azure", "icon": "$(gear)"}, {"command": "azureResourceGroups.askAgentAboutResource", "title": "Ask @azure"}], "viewsContainers": {"activitybar": [{"id": "azure", "title": "Azure", "icon": "$(azure)"}], "panel": [{"id": "azurePanel", "title": "Azure", "icon": "$(azure)"}]}, "views": {"azure": [{"id": "azureFocusView", "name": "Focused Resources", "visibility": "hidden", "when": "ms-azuretools.vscode-azureresourcegroups.hasFocusedGroup == true"}, {"id": "azureResourceGroups", "name": "Resources", "visibility": "visible"}, {"id": "azureWorkspace", "name": "Workspace", "visibility": "visible"}, {"id": "azureTenantsView", "name": "Accounts & Tenants", "visibility": "collapsed"}, {"id": "ms-azuretools.helpAndFeedback", "name": "Help and Feedback", "visibility": "collapsed"}], "azurePanel": [{"id": "azureActivityLog", "name": "Activity Log", "visibility": "collapsed", "icon": "$(azure)", "type": "tree"}]}, "viewsWelcome": [{"view": "azureActivityLog", "contents": "View all of your recent activities and quickly access resources you've recently created. \n [Create an Azure resource](command:azureResourceGroups.createResource)"}, {"view": "azureWorkspace", "contents": "No local workspace resources exist.", "when": "azureWorkspace.state == 'noWorkspaceResources'"}, {"view": "azureWorkspace", "contents": "No local workspace resource providers exist.", "when": "azureWorkspace.state == 'noWorkspaceResourceProviders'"}, {"view": "azureResourceGroups", "contents": "Please sign in to a specific tenant (directory) to continue. \n [Sign in to Tenant (Directory)...](command:azureResourceGroups.signInToTenant)\n[View Accounts & Tenants](command:azureTenantsView.focus)", "when": "azureResourceGroups.needsTenantAuth == true"}], "menus": {"explorer/context": [{"command": "azureResourceGroups.uploadFileCloudConsole", "when": "resourceScheme == file && !explorerResourceIsFolder && isWorkspaceTrusted && azureResourcesOpenCloudConsoleCount && azureResourcesOpenCloudConsoleCount != 0", "group": "999_cloudConsole"}], "editor/title/context": [{"command": "azureResourceGroups.uploadFileCloudConsole", "when": "resourceScheme == file && isWorkspaceTrusted && azureResourcesOpenCloudConsoleCount && azureResourcesOpenCloudConsoleCount != 0", "group": "999_cloudConsole"}], "view/title": [{"command": "azureResourceGroups.createResource", "when": "view == azureResourceGroups", "group": "navigation@1"}, {"command": "azureResourceGroups.unfocusGroup", "when": "view == azureFocusView", "group": "navigation@4"}, {"submenu": "azureResourceGroups.groupBy", "when": "view == azureResourceGroups", "group": "navigation@2"}, {"command": "azureFocusView.refreshTree", "when": "view == azureFocusView", "group": "navigation@3"}, {"command": "azureResourceGroups.refreshTree", "when": "view == azureResourceGroups", "group": "navigation@3"}, {"command": "azureResourceGroups.askAgentAboutActivityLog", "when": "view == azureActivityLog", "group": "navigation@1"}, {"command": "azureWorkspace.refreshTree", "when": "view == azureWorkspace", "group": "navigation@10"}, {"command": "azureTenantsView.refreshTree", "when": "view == azureTenantsView", "group": "navigation@10"}, {"command": "azureResourceGroups.clearActivities", "when": "view == azureActivityLog", "group": "navigation@1"}, {"submenu": "azureWorkspaceCreate", "when": "view == azureWorkspace", "group": "navigation@1"}, {"command": "azureTenantsView.configureSovereignCloud", "when": "view == azureTenantsView", "group": "navigation@2"}, {"command": "azureTenantsView.addAccount", "when": "view == azureTenantsView", "group": "navigation@1"}], "view/item/context": [{"command": "azureResourceGroups.loadAllSubscriptionRoleAssignments", "when": "view == azureResourceGroups && viewItem =~ /targetServiceRoleAssignmentItem(?!allLoaded)/", "group": "inline"}, {"command": "azureResourceGroups.loadAllSubscriptionRoleAssignments", "when": "view == azureResourceGroups && viewItem =~ /targetServiceRoleAssignmentItem(?!allLoaded)/", "group": "1@1"}, {"command": "azureResourceGroups.focusGroup", "when": "view == azureResourceGroups && viewItem =~ /canFocus/", "group": "inline"}, {"command": "azureResourceGroups.selectSubscriptions", "when": "view == azureResourceGroups && viewItem == azureextensionui.azureSubscription", "group": "inline"}, {"command": "azureResourceGroups.createResource", "when": "view == azureResourceGroups && viewItem == azureextensionui.azureSubscription", "group": "1@1"}, {"command": "azureResourceGroups.openInPortal", "when": "view == azureResourceGroups && viewItem == azureextensionui.azureSubscription", "group": "9@1"}, {"command": "azureResourceGroups.refresh", "when": "view == azureResourceGroups && viewItem == azureextensionui.azureSubscription", "group": "9@2"}, {"command": "azureResourceGroups.deleteResourceGroupV2", "when": "view == azureResourceGroups && viewItem =~ /azureResourceGroup/", "group": "1@1"}, {"command": "azureResourceGroups.editTags", "when": "view =~ /azureResourceGroups|azureFocusView/ && viewItem =~ /azureResourceGroup/", "group": "2@1"}, {"command": "azureResourceGroups.openInPortal", "when": "view =~ /azureResourceGroups|azureFocusView/ && viewItem =~ /hasPortalUrl/", "group": "9@2"}, {"command": "azureResourceGroups.refresh", "when": "view =~ /azureResourceGroups/ && viewItem =~ /azureResource/", "group": "9@3"}, {"command": "azureFocusView.refresh", "when": "view =~ /azureFocusView/ && viewItem =~ /azureResource/", "group": "9@3"}, {"command": "azureResourceGroups.editTags", "when": "view =~ /azureResourceGroups|azureFocusView/ && viewItem =~ /azureResource(?!Group)(?!Type)/", "group": "8@1"}, {"command": "azureResourceGroups.viewProperties", "when": "view =~ /azureResourceGroups|azureFocusView/ && viewItem =~ /hasProperties/", "group": "9@1"}, {"command": "azureResourceGroups.viewProperties", "when": "view =~ /azureResourceGroups|azureFocusView/ && viewItem =~ /azureResourceGroup/", "group": "9@1"}, {"command": "azureTenantsView.signInToTenant", "when": "view == azureTenantsView && viewItem =~ /tenantNameNotSignedIn/", "group": "inline@1"}, {"command": "azureTenantsView.signInToTenant", "when": "view == azureTenantsView && viewItem =~ /tenantNameNotSignedIn/", "group": "1@1"}, {"command": "azureResourceGroups.askAgentAboutResource", "when": "view =~ /azureResourceGroups|azureFocusView/ && viewItem =~ /hasPortalUrl/ && github.copilot-chat.activated && config.azureResourceGroups.enableChatStandIn", "group": "1@9"}, {"command": "azureResourceGroups.askAgentAboutResource", "when": "view == kubernetes.cloudExplorer && viewItem =~ /aks\\.cluster/i && github.copilot-chat.activated && config.azureResourceGroups.enableChatStandIn", "group": "7@9"}], "commandPalette": [{"command": "azureResourceGroups.uploadFileCloudConsole", "when": "isWorkspaceTrusted"}, {"command": "azureResourceGroups.showGroupOptions", "when": "never"}, {"command": "azureResourceGroups.refresh", "when": "never"}, {"command": "azureWorkspace.refresh", "when": "never"}, {"command": "azureTenantsView.refresh", "when": "never"}, {"command": "azureFocusView.refreshTree", "when": "never"}, {"command": "azureResourceGroups.loadMore", "when": "never"}, {"command": "azureResourceGroups.revealResource", "when": "never"}, {"command": "azureResourceGroups.installExtension", "when": "never"}, {"command": "azureTenantsView.addAccount", "when": "never"}, {"command": "azureTenantsView.signInToTenant", "when": "never"}, {"command": "azureResourceGroups.askAgentAboutResource", "when": "never"}], "azureResourceGroups.groupBy": [{"command": "azureResourceGroups.groupBy.resourceGroup", "group": "1_default@1"}, {"command": "azureResourceGroups.groupBy.resourceType", "group": "1_default@2"}, {"command": "azureResourceGroups.groupBy.location", "group": "1_default@3"}, {"command": "azureResourceGroups.groupBy.armTag", "group": "1_default@4"}]}, "submenus": [{"id": "azureResourceGroups.groupBy", "label": "Group By", "icon": "$(group-by-ref-type)"}, {"id": "azureWorkspaceCreate", "icon": "$(add)", "label": "Add..."}], "keybindings": [{"command": "workbench.view.extension.azure", "win": "ctrl+shift+a", "mac": "cmd+shift+a", "linux": "shift+alt+a"}], "configuration": [{"title": "Azure", "properties": {"azureResourceGroups.enableOutputTimestamps": {"type": "boolean", "description": "Prepends each line displayed in the output channel with a timestamp.", "default": true}, "azureResourceGroups.deleteConfirmation": {"type": "string", "enum": ["EnterName", "ClickButton"], "description": "The behavior to use when confirming delete of a resource group.", "enumDescriptions": ["Prompts with an input box where you enter the resource group name to delete.", "Prompts with a warning dialog where you click a button to delete."], "default": "EnterName", "scope": "machine"}, "azureResourceGroups.showHiddenTypes": {"type": "boolean", "description": "Show some ancillary resources that are created/managed by Azure infrastructure. Displaying them is typically useful when you want to clean up your resource groups or subscriptions.", "default": false}, "azureResourceGroups.groupBy": {"type": "string", "description": "The setting to control how Azure resources are grouped in the tree view", "default": "resourceType", "scope": "machine"}, "azureResourceGroups.suppressActivityNotifications": {"type": "boolean", "description": "Suppress Activity notifications", "default": true}, "azureResourceGroups.autoOpenActivityPanel": {"type": "boolean", "description": "Open the Azure Activity panel when an Activity starts", "default": true}, "azureResourceGroups.selectedSubscriptions": {"type": "array", "description": "Selected Subscriptions", "items": {"type": "string"}}, "azureResourceGroups.enableChatStandIn": {"type": "boolean", "description": "Enable the @azure chat stand-in", "default": true}}}], "walkthroughs": [{"id": "vscodeDevWalkthrough", "when": "isWeb", "title": "Deploy your application with VS Code for the Web - Azure", "description": "**Note:** your work will not be saved unless you commit your code to GitHub. Follow the 'Saving your work' step for more information.", "steps": [{"id": "deploy", "title": "Deploy your template", "description": "This environment includes the [Azure Developer CLI](https://learn.microsoft.com/en-us/azure/developer/azure-developer-cli/overview), you can use it to run, debug, and deploy Azure applications. Your app has been initialized for you already (``azd init``).\nProvision and deploy your app to Azure (``azd up``) by right clicking your azure.yaml file and selecting up from the context menu (pictured) or by clicking the button below.\n[One-click provision + deploy](command:azure-dev.commands.cli.up)\nTip: You can also run the ``azd up`` command in the [Command Palette](command:workbench.action.quickOpen?%5B%22%3E%20Azure%20Developer%20CLI%20%28azd%29%3A%20up%22%5D) or your terminal to provision and deploy your app.", "media": {"svg": "resources/vscode-dev-walkthrough/azdUp.svg", "altText": "Right click azure.yaml to deploy"}}, {"id": "chatWithCopilot", "title": "Chat with Copilot", "description": "Ask Copilot general programming questions with chat. <PERSON>pi<PERSON> can also fix problems, scaffold tests, or refactor your code. Type ``/`` while in Copilot to view all available chat commands.\n[Chat with GitHub Copilot](command:workbench.panel.chat.view.copilot.focus)\nTip: You can use [GitHub Copilot for Azure](https://aka.ms/GetGitHubCopilotForAzure) (``@azure``) to view and manage your Azure resources from within GitHub Copilot.", "media": {"svg": "resources/vscode-dev-walkthrough/chatWithCopilot.svg", "altText": "Chat with Copilot"}}, {"id": "azureExtensions", "title": "Leverage Azure extensions", "description": "The environment is fully equipped with Azure VS Code extensions. These extensions make Azure development easier to create, run and deploy your applications.\n[View Azure Extensions](command:workbench.view.extension.azure)", "media": {"svg": "resources/vscode-dev-walkthrough/azureExtensions.svg", "altText": "Chat with Copilot"}}, {"id": "github", "title": "Saving your work", "description": "In order to pick up where you left off, commit your code to GitHub. Through GitHub, you can share your project easily and collaborate with others.\n[Publish to GitHub](command:workbench.view.scm)", "media": {"svg": "resources/vscode-dev-walkthrough/publishToGitHub.svg", "altText": "Chat with Copilot"}}]}, {"id": "azure-get-started", "title": "Get Started with Azure in VS Code", "description": "Setup your account and get to know the Azure extension view.", "when": "false", "steps": [{"id": "sign-in", "title": "Sign into your Azure account", "description": "Sign in or create an Azure Account to see all of the resources within your Azure subscription.\n[Sign In](command:azureResourceGroups.logIn)\n", "media": {"svg": "resources/walkthrough/signin.svg", "altText": "Sign into your Azure Account."}}, {"id": "azure-view", "title": "The Azure view & manage resources", "description": "Use the Resources explorer to create and manage Azure resources. Use the Workspace explorer to create files and deploy.\n[Open the Azure view](command:workbench.view.extension.azure)\n", "media": {"svg": "resources/walkthrough/sidebarview.svg", "altText": "The Azure Sidebar view."}}, {"id": "grouping", "title": "Grouping your resources", "description": "Change the way resources are grouped to fit your workflow.\n[Show grouping options](command:azureResourceGroups.showGroupOptions)", "media": {"svg": "resources/walkthrough/groupby.svg", "altText": "Group resources."}}, {"id": "activity-log", "title": "The Azure activity log", "description": "View all of your recent activities and quickly access resources you've recently created.\n[Show the Activity Log](command:azureActivityLog.focus)", "media": {"svg": "resources/walkthrough/activitylog.svg", "altText": "The Azure Activity Log."}}, {"id": "create-resource", "title": "Create an Azure resource", "description": "Create an Azure resource from your installed extensions directly in VS Code.\n[Create a resource](command:azureResourceGroups.createResource)", "completionEvents": [""], "media": {"svg": "resources/walkthrough/createresource.svg", "altText": "Create Azure resources."}}, {"id": "ask-azure", "title": "Ask @azure", "when": "github.copilot-chat.activated", "description": "Enter @azure in the GitHub Copilot Chat to start a conversation.\n @azure can help with learning about Azure, deploying resources, troubleshooting web app problems, querying Azure costs and more without having to switch to the Azure Portal.\n[Chat with @azure](command:azureResourceGroups.askAzure?%5B%22What%20kinds%20of%20things%20can%20you%20help%20me%20with?%22%5D)", "media": {"image": "https://github.com/microsoft/vscode-azureresourcegroups/raw/main/resources/walkthrough/askAzure.gif", "altText": "Enter @azure in GitHub Copilot Chat"}}]}]}, "scripts": {"vscode:prepublish": "npm run webpack-prod", "build": "tsc", "cleanReadme": "gulp cleanReadme", "compile": "tsc -watch", "package": "vsce package --githubBranch main", "lint": "eslint --ext .ts .", "lint-fix": "eslint --ext .ts . --fix", "listIcons": "gulp listIcons", "test": "node ./out/test/runTest.js", "pretest": "webpack", "webpack": "tsc && gulp webpack-dev", "webpack-prod": "npm run build && gulp webpack-prod", "webpack-profile": "webpack --profile --json --mode production > webpack-stats.json && echo Use http://webpack.github.io/analyse to analyze the stats", "all": "npm i && npm run lint && npm test", "api-extractor": "tsc -p ./api && api-extractor run -c ./api/api-extractor.json"}, "devDependencies": {"@azure/arm-authorization": "^9.0.0", "@azure/arm-msi": "^2.1.0", "@azure/arm-resources-subscriptions": "^2.1.0", "@azure/identity": "^4.2.1", "@microsoft/api-extractor": "^7.33.8", "@microsoft/eslint-config-azuretools": "0.2.1", "@microsoft/vscode-azext-dev": "^2.0.5", "@types/fs-extra": "^11.0.4", "@types/gulp": "^4.0.6", "@types/mocha": "^7.0.2", "@types/node": "18.19.x", "@types/request-promise": "^4.1.51", "@types/semver": "^7.3.12", "@types/uuid": "^9.0.1", "@types/vscode": "1.95.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^5.59.11", "@vscode/test-electron": "^2.3.8", "@vscode/vsce": "^2.19.0", "chokidar": "^3.6.0", "copy-webpack-plugin": "^12.0.2", "eslint": "^8.42.0", "eslint-plugin-import": "^2.27.5", "glob": "^7.1.6", "gulp": "^5.0.0", "mocha": "^10.8.2", "mocha-junit-reporter": "^1.23.1", "mocha-multi-reporters": "^1.1.7", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.0", "typescript": "^5.1.3", "webpack": "^5.95.0", "webpack-cli": "^4.6.0"}, "dependencies": {"@azure/arm-resources": "^5.2.0", "@azure/arm-resources-profile-2020-09-01-hybrid": "^2.1.0", "@azure/ms-rest-js": "^2.7.0", "@microsoft/vscode-azext-azureauth": "^4.0.3", "@microsoft/vscode-azext-azureutils": "^3.3.1", "@microsoft/vscode-azext-utils": "^3.3.1", "buffer": "^6.0.3", "form-data": "^4.0.4", "fs-extra": "^11.3.0", "jsonc-parser": "^2.2.1", "node-fetch": "^3.3.2", "request-promise": "^4.2.6", "uuid": "^9.0.0", "vscode-nls": "^5.0.1", "vscode-uri": "^3.0.7", "ws": "^8.17.1"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/ms-azuretools.vscode-azureresourcegroups-0.11.1", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Microsoft", "metadata": {"installedTimestamp": 1754245445244, "pinned": false, "source": "gallery", "id": "260e9ed1-160d-4f7e-807c-2eb076ba3846", "publisherId": "52b787f2-79a9-4f32-99b4-393afe3005d3", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": true, "size": 5539675}, "isValid": true, "validations": [], "preRelease": true}, {"type": 1, "identifier": {"id": "ms-dotnettools.vscode-dotnet-runtime", "uuid": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f"}, "manifest": {"name": "vscode-dotnet-runtime", "repository": {"type": "git", "url": "https://github.com/dotnet/vscode-dotnet-runtime.git"}, "bugs": {"url": "https://github.com/dotnet/vscode-dotnet-runtime/issues"}, "license": "MIT", "author": "Microsoft Corporation", "displayName": ".NET Install Tool", "description": "This extension installs and manages different versions of the .NET SDK and Runtime.", "connectionString": "InstrumentationKey=02dc18e0-7494-43b2-b2a3-18ada5fcb522;IngestionEndpoint=https://westus2-0.in.applicationinsights.azure.com/;LiveEndpoint=https://westus2.livediagnostics.monitor.azure.com/;ApplicationId=e8e56970-a18a-4101-b7d1-1c5dd7c29eeb", "icon": "images/dotnetIcon.png", "version": "2.3.7", "publisher": "ms-dot<PERSON><PERSON>s", "engines": {"vscode": "^1.101.0"}, "categories": ["Other"], "keywords": [".NET", ".NET Core", "dotnet", "Extension Authoring", "runtime"], "capabilities": {"untrustedWorkspaces": {"supported": true}, "virtualWorkspaces": true}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "types": "./dist/extension.d.ts", "contributes": {"commands": [{"command": "dotnet.reportIssue", "title": "Report an issue with the .NET Install Tool.", "category": ".NET Install Tool"}, {"command": "dotnet.acquireGlobalSDKPublic", "title": "Install the .NET SDK System-Wide.", "category": ".NET Install Tool"}, {"command": "dotnet.uninstallPublic", "title": "Uninstall .NET.", "category": ".NET Install Tool"}, {"command": "dotnet.resetData", "title": "Reset Extension Data (Uninstalls All Installations, Which May Cause Other Extensions To Fail. Uninstall Dependent Extensions First)", "category": ".NET Install Tool", "enablement": "config.dotnetAcquisitionExtension.showResetDataCommand"}], "configuration": {"title": ".NET Install Tool", "properties": {"dotnetAcquisitionExtension.enableTelemetry": {"type": "boolean", "default": true, "description": "Enable Telemetry for the .NET Install Tool. Restart VS Code to apply changes."}, "dotnetAcquisitionExtension.enablePreviewFeatures": {"type": "boolean", "default": false, "description": "Enable Preview Features for the Extension. Restart VS Code to apply changes."}, "dotnetAcquisitionExtension.installTimeoutValue": {"type": "number", "default": 600, "description": "Timeout for installing .NET in seconds."}, "dotnetAcquisitionExtension.existingDotnetPath": {"type": "array", "markdownDescription": "The path to an existing .NET host executable for an extension's code to run under, not for your project to run under.\nRestart VS Code to apply changes.\n\n⚠️ This is NOT the .NET Runtime that your project will use to run. Extensions such as `C#`, `C# DevKit`, and more have components written in .NET. This .NET PATH is the `dotnet.exe` that these extensions will use to run their code, not your code.\n\nUsing a path value in which .NET does not meet the requirements of a specific extension will cause that extension to fail.\n\n🚀 The version of .NET that is used for your project is determined by the .NET host, or dotnet.exe. The .NET host picks a runtime based on your project. To use a specific version of .NET for your project, install the .NET SDK using the `.NET Install Tool - Install SDK System-Wide` command, install .NET manually using [our instructions](https://dotnet.microsoft.com/download), or edit your PATH environment variable to point to a `dotnet.exe` that has an `/sdk/` folder with only one SDK.", "description": "The path to an existing .NET host executable for an extension's code to run under, not for your project to run under.\nRestart VS Code to apply changes.\n\n⚠️ This is NOT the .NET Runtime that your project will use to run. Extensions such as 'C#', 'C# DevKit', and more have components written in .NET. This .NET PATH is the 'dotnet.exe' that these extensions will use to run their code, not your code.\n\nUsing a path value in which .NET does not meet the requirements of a specific extension will cause that extension to fail.\n\n🚀 The version of .NET that is used for your project is determined by the .NET host, or dotnet.exe. The .NET host picks a runtime based on your project. To use a specific version of .NET for your project, install the .NET SDK using the '.NET Install Tool - Install SDK System-Wide' command, use the instructions at https://dotnet.microsoft.com/download to manually install the .NET SDK, or edit your PATH environment variable to point to a 'dotnet.exe' that has an '/sdk/' folder with only one SDK.", "examples": ["C:\\Program Files\\dotnet\\dotnet.exe", "/usr/local/share/dotnet/dotnet", "/usr/lib/dotnet/dotnet"]}, "dotnetAcquisitionExtension.sharedExistingDotnetPath": {"type": "string", "description": "The path of the preexisting .NET Runtime you'd like to use for ALL extensions. Restart VS Code to apply changes.", "examples": ["C:\\Program Files\\dotnet\\dotnet.exe", "/usr/local/share/dotnet/dotnet", "/usr/lib/dotnet/dotnet"]}, "dotnetAcquisitionExtension.proxyUrl": {"type": "string", "description": "URL to a proxy if you use one, such as: https://proxy:port"}, "dotnetAcquisitionExtension.allowInvalidPaths": {"type": "boolean", "description": "If you'd like to continue using a .NET path that is not meant to be used for an extension and may cause instability (please read above about the existingDotnetPath setting) then set this to true and restart."}, "dotnetAcquisitionExtension.cacheTimeToLiveMultiplier": {"type": "number", "default": 1, "description": "To improve performance, the results of checking .NET Installations may be cached. If you're facing issues with an install not being detected, try setting this to 0.5, or 0; or increasing the number to improve performance. Restart to change."}, "dotnetAcquisitionExtension.showResetDataCommand": {"type": "boolean", "default": false, "description": "Show the command to reset extension data in the command palette. Restart VS Code and remove dependent extensions first."}, "dotnetAcquisitionExtension.suppressOutput": {"type": "boolean", "default": false, "description": "Hide all messages in the output window from our extension. Error pop-ups from APIs that other extensions request we display will still appear."}}}}, "scripts": {"vscode:prepublish": "npm run compile-all && npm install && webpack --mode production && dotnet build ../msbuild/signJs --property jsOutputPath=..\\..\\vscode-dotnet-runtime-extension\\dist -bl -v:normal", "compile": "npm run clean && tsc -p ./", "watch": "npm run compile && tsc -watch -p ./", "test": "npm run compile --silent && node ./dist/test/functional/runTest.js", "clean": "<PERSON><PERSON><PERSON> dist", "compile-all": "cd ../vscode-dotnet-runtime-library && npm install && npm run compile && cd ../vscode-dotnet-runtime-extension && npm install && npm run compile", "lint": "eslint -c .eslintrc.js --ext=.ts vscode-dotnet-runtime-library/src/**/*.ts vscode-dotnet-runtime-extension/src/**/*.ts --ignore-pattern \"**/test/\" --fix", "webpack": "webpack --mode development"}, "dependencies": {"@types/chai-as-promised": "^7.1.8", "@vscode/test-electron": "^2.3.9", "axios": "^1.7.4", "axios-cache-interceptor": "^1.0.1", "axios-retry": "^3.4.0", "chai": "4.3.4", "glob": "^7.2.0", "https-proxy-agent": "^7.0.2", "mocha": "^11.7.0", "open": "^8.4.0", "rimraf": "3.0.2", "shelljs": "^0.8.5", "ts-loader": "^9.5.1", "typescript": "^5.5.4", "vscode-dotnet-runtime-library": "file:../vscode-dotnet-runtime-library", "webpack-permissions-plugin": "^1.0.9"}, "devDependencies": {"@types/chai": "^4.3.5", "@types/mocha": "^9.0.0", "@types/node": "^20.0.0", "@types/rimraf": "3.0.2", "@types/source-map-support": "^0.5.10", "@types/vscode": "1.74.0", "copy-webpack-plugin": "^9.0.1", "webpack": "^5.88.2", "webpack-cli": "^4.9.1"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.7", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Microsoft", "metadata": {"installedTimestamp": 1754245446942, "source": "gallery", "id": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 5584640}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "marxism.ai-token-count", "uuid": "e0252541-5b6c-477d-b9c6-fb3d75cf4b59"}, "manifest": {"name": "ai-token-count", "displayName": " Token Count in Status Bar for your active file and selection", "description": "GPT-4, GPT-3.5, <PERSON><PERSON><PERSON>, LLaMA token counts live in your status bar! token count file status bar.", "version": "1.1.1", "publisher": "marxism", "private": true, "license": "AGPL-3.0", "icon": "icon.png", "repository": {"type": "git", "url": "https://github.com/PeoplesGrocers/vscode-count-tokens-statusbar"}, "engines": {"vscode": "^1.73.0"}, "categories": ["Programming Languages", "Machine Learning", "Education", "Other"], "keywords": ["ai", "assistant", "chatgpt", "gpt4", "GPT-3.5", "GPT-4", "LLaMA", "tokenzier", "intellisense", "refactor", "token", "count", "status bar", "bar", "toolbar", "tool", "shortcut"], "activationEvents": ["onStartupFinished"], "contributes": {"commands": [{"command": "marxism.ai-token-count.changeTokenizer", "title": "Count <PERSON><PERSON>s in Status Bar: Change Token<PERSON>"}]}, "main": "./out/extension.js", "browser": "./out/extension.js", "scripts": {"vscode:prepublish": "npm run esbuild-base --", "esbuild-base": "esbuild ./src/extension.ts  --bundle --outfile=out/extension.js --external:vscode --format=cjs --platform=node", "esbuild": "npm run esbuild-base -- --sourcemap", "esbuild-watch": "npm run esbuild-base -- --sourcemap --watch", "test-compile": "tsc -p ./", "compile": "tsc -p ./", "lint": "eslint \"src/**/*.ts\" ", "lint-fix": "eslint \"src/**/*.ts\" --fix", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.73.0", "esbuild": "^0.19.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.0.3", "typescript": "^5.1.3"}, "dependencies": {"eslint": "^8.50.0", "js-tiktoken": "^1.0.7", "llama-tokenizer-js": "^1.1.3"}, "eslintIgnore": ["out", "node_modules"]}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/marxism.ai-token-count-1.1.1", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "<PERSON>", "metadata": {"installedTimestamp": 1754245447899, "source": "gallery", "id": "e0252541-5b6c-477d-b9c6-fb3d75cf4b59", "publisherId": "806bef03-55a7-4e1d-8f09-1f52c93b238a", "publisherDisplayName": "<PERSON>", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 10735543}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "almenon.arepl", "uuid": "88eb8072-5ab1-457b-9835-6d92196388a3"}, "manifest": {"name": "arepl", "displayName": "AREPL for python", "description": "real-time python scratchpad", "version": "3.0.0", "publisher": "almenon", "engines": {"vscode": "^1.40.0"}, "categories": ["Other", "Debuggers"], "activationEvents": ["onCommand:extension.currentAREPLSession", "onCommand:extension.newAREPLSession", "onCommand:extension.newAREPLSessionOnHighlightedCode"], "main": "./out/src/extension", "contributes": {"menus": {"editor/title": [{"when": "editorLangId == python && !arepl && editorHasSelection && config.AREPL.showIcons", "command": "extension.newAREPLSession", "alt": "extension.currentAREPLSession", "group": "navigation"}, {"when": "editorLangId == python && !arepl && !editorHasSelection && config.AREPL.showIcons", "command": "extension.currentAREPLSession", "alt": "extension.newAREPLSession", "group": "navigation"}, {"when": "arepl && config.AREPL.showIcons", "command": "extension.closeAREPL", "group": "navigation"}], "editor/title/context": [{"command": "extension.currentAREPLSession"}, {"command": "extension.newAREPLSession"}], "editor/context": [{"command": "extension.newAREPLSessionOnHighlightedCode", "when": "editorHasSelection"}]}, "configuration": {"type": "object", "title": "AREPL configuration", "properties": {"AREPL.showIcons": {"type": "boolean", "default": true}, "AREPL.skipLandingPage": {"type": "boolean", "default": false}, "AREPL.showFooter": {"type": "boolean", "default": true}, "AREPL.customCSS": {"type": "string", "default": null, "description": "injected into AREPL each run. Use 'Developer: Open Webview Developer Tools' command to inspect arepl html"}, "AREPL.delay": {"type": "number", "default": 350, "description": "delay in milliseconds before executing code after typing"}, "AREPL.inlineResults": {"type": "boolean", "default": true, "description": "whether to show errors / results / prints inline. (Currently just error icons)"}, "AREPL.whenToExecute": {"type": "string", "enum": ["after<PERSON>elay", "onSave", "onKeybinding"], "enumDescriptions": ["after a delay (change AREPL.delay to control the amount)", "on save", "on keyboard shortcut"], "default": "after<PERSON>elay", "description": "When to execute your code"}, "AREPL.show_to_level": {"type": "number", "default": 2, "description": "2 shows x=1 and x=[1,2], provides option to expand deeply nested data like x=[[1]]"}, "AREPL.max_string_length": {"type": "number", "default": 70, "description": "strings over X characters are truncated with an option to expand"}, "AREPL.printResultPlacement": {"type": "string", "enum": ["top", "bottom"], "enumDescriptions": ["print results appear on top", "print results appear on bottom"], "default": "top"}, "AREPL.pythonPath": {"type": "string", "default": null, "description": "AREPL uses the path you have defined in the official python extension. If not present then this setting will be used. If this setting is also empty then on windows py will be used, or python3 otherwise"}, "AREPL.envFile": {"type": "string", "default": null, "description": "Absolute path to a file containing environment variable definitions. If not set the setting from the python extension will be used, or ${workspaceFolder}/.env if the python extension is not installed"}, "AREPL.pythonOptions": {"type": "array", "default": ["-u"], "description": "default -u to see prints in real-time. See https://docs.python.org/3/using/cmdline.html#miscellaneous-options for other options. Changing this setting not reccomended"}, "AREPL.defaultFilterVars": {"type": "array", "default": [], "description": "Any variables with these names are not shown in arepl variable view. You can use the arepl_filter variable in arepl to play around with this setting in real-time"}, "AREPL.defaultFilterTypes": {"type": "array", "default": ["<class 'typing._SpecialForm'>", "<class 'typing._SpecialGenericAlias'>", "<class 'module'>", "<class 'function'>", "<class 'builtin_function_or_method'>"], "description": "Any variables with these types are not shown in arepl variable view. You can use the arepl_filter_type variable in arepl to play around with this setting in real-time"}, "AREPL.defaultImports": {"type": "array", "default": ["from arepl_dump import dump"], "description": "Default imports for new AREPL sessions. dump is included by default so you can use it to inspect variables & local scopes"}, "AREPL.unsafeKeywords": {"type": "array", "default": ["rmdir\\(", "rmtree\\("], "description": "AREPL will not evaulate your code in real-time mode if one of these keywords are detected. Keywords are applied via regex."}, "AREPL.telemetry": {"type": "boolean", "default": true, "description": "Whether to report useful anonymous data back to developer."}, "AREPL.showSyntaxErrors": {"type": "boolean", "default": true, "description": "Whether to show syntax errors."}, "AREPL.showNameErrors": {"type": "boolean", "default": true, "description": "Whether to show name errors."}, "AREPL.showGlobalVars": {"type": "boolean", "default": true, "description": "Whether to show global vars. Potential speed improvement if turned off - you can still inspect vars with dump()"}, "AREPL.shell_plus": {"type": "boolean", "default": false, "description": "Whether to automatically load django models. This setting doesn't actually do anything yet. See https://github.com/Almenon/AREPL-vscode/issues/279"}}}, "commands": [{"command": "extension.currentAREPLSession", "title": "eval python in real time (current doc)", "category": "AREPL", "icon": "./media/happy_cat.svg"}, {"command": "extension.newAREPLSession", "title": "eval python in real time (new doc)", "category": "AREPL", "icon": "./media/happy_cat.svg"}, {"command": "extension.closeAREPL", "title": "closes AREPL", "category": "AREPL", "icon": "./media/angry_cat_filled.svg"}, {"command": "extension.newAREPLSessionOnHighlightedCode", "title": "open highlighted code in new AREPL session", "category": "AREPL"}, {"command": "extension.executeAREPL", "title": "trigger a run in the current AREPL session", "category": "AREPL"}, {"command": "extension.executeAREPLBlock", "title": "execute the current block of code", "category": "AREPL"}], "keybindings": [{"command": "extension.currentAREPLSession", "key": "ctrl+shift+a", "mac": "cmd+shift+a", "when": "!inQuickOpen && !terminalFocus"}, {"command": "extension.newAREPLSession", "key": "ctrl+shift+q", "mac": "cmd+shift+r", "when": "!inQuickOpen && !terminalFocus"}, {"command": "extension.executeAREPL", "key": "ctrl+shift+;", "mac": "cmd+shift+;", "when": "!inQuickOpen && !terminalFocus && editorLangId == python"}, {"command": "extension.executeAREPLBlock", "key": "ctrl+enter", "mac": "cmd+enter", "when": "editorTextFocus && editorLangId == python"}, {"command": "extension.printDir", "key": "alt+enter", "mac": "alt+enter", "when": "editorTextFocus && editorLangId == python"}]}, "scripts": {"vscode:prepublish": "tsc -p ./", "compile": "tsc -watch -p ./", "compileOnce": "tsc -p ./", "test": "vscode-test", "unitTests": "mocha -ui tdd -r ts-node/register test/*.spec.ts", "visualize": "depcruise --exclude 'vscode|^[a-zA-Z0-9\\_]+$' --output-type dot src | dot -T svg > dependencygraph.svg", "vscode:uninstall": "node ./out/src/uninstallSurvey"}, "devDependencies": {"@types/mocha": "^10.0.9", "@types/node": "^20.17.6", "@types/vscode": "^1.40.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "mocha": "^10.8.2", "source-map-support": "^0.5.13", "ts-node": "^10.9.2", "typescript": "^5.6.3", "vscode-uri": "^2.1.2"}, "dependencies": {"@vscode/extension-telemetry": "^0.9.7", "arepl-backend": "^3.0.5"}, "bugs": {"url": "https://github.com/almenon/arepl-vscode-wordcount/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/almenon/arepl-vscode.git"}, "license": "SEE LICENSE IN <filename>", "keywords": ["python", "real-time", "scratchpad"], "icon": "media/happy_cat_filled.png"}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/almenon.arepl-3.0.0", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Almenon", "metadata": {"installedTimestamp": 1754245446357, "source": "gallery", "id": "88eb8072-5ab1-457b-9835-6d92196388a3", "publisherId": "79f77d66-53ca-4048-8a36-21dab21d64bc", "publisherDisplayName": "Almenon", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 17438259}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "google.geminicodeassist", "uuid": "51643712-2cb2-4384-b7cc-d55b01b8274b"}, "manifest": {"name": "geminicodeassist", "displayName": "Gemini Code Assist", "description": "AI-assisted development powered by Gemini", "version": "2.43.0", "publisher": "google", "engines": {"vscode": "^1.97.0"}, "license": "SEE LICENSE IN LICENSE", "categories": ["AI", "Programming Languages", "Machine Learning", "Cha<PERSON>"], "keywords": ["AI Assistant", "Code Completion", "Code Generation", "Duet", "Google", "LLM", "Gemini", "Cha<PERSON>", "Code Assist", "Pair Programmer"], "preview": false, "qna": false, "icon": "images/gemini_logo.png", "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"languages": [{"id": "ignore", "filenames": [".gcloudignore"]}, {"id": "Log", "aliases": ["log"], "extensions": [".log"], "configuration": "./log.configuration.json", "mimetypes": ["log", "text/log", "text/x-log", "text/x-code-output", "x-code-output"]}], "grammars": [{"language": "Log", "scopeName": "code.log", "path": "./third_party/vscode-log-output-colorizer/src/syntaxes/log.tmLanguage"}], "configurationDefaults": {"[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 2, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}, "editor.autoIndent": "none"}}, "configuration": {"type": "object", "title": "Gemini Code Assist", "properties": {"cloudcode.debug.telemetry": {"type": "array", "default": ["*"], "deprecationMessage": "This setting is used in development mode to debug telemetry events", "items": {"type": "string", "enum": ["*", "cloudcode.aipp.inlinecompletion.offered", "cloudcode.aipp.inlinecompletion.offered.response", "cloudcode.aipp.inlinecompletion.accepted", "cloudcode.aipp.conversation.offered", "cloudcode.aipp.conversation.accepted", "cloudcode.aipp.conversation.reset", "cloudcode.aipp.conversation.explain", "cloudcode.aipp.conversation.generatetests", "cloudcode.aipp.conversation.thumbsup", "cloudcode.aipp.conversation.thumbsdown", "cloudcode.aipp.conversation.expandcode", "cloudcode.duetAI.interactiveEditorProvider.bug", "cloudcode.duetAI.interactiveEditorProvider.feedback", "cloudcode.duetAI.interactiveEditorProvider.accept", "cloudcode.duetAI.interactiveEditorProvider.undone", "cloudcode.duetAI.interactiveEditorProvider.thumbsup", "cloudcode.duetAI.interactiveEditorProvider.thumbsdown", "cloudcode.aipp.conversation.agentmode.enabled", "cloudcode.aipp.conversation.agentmode.disabled", "cloudcode.duetAI.interactiveEditorProvider.handle%s", "cloudcode.duetAI.languageServer.async.error", "cloudcode.duetAI.inlinecompletion.tracksuffix"]}}, "geminicodeassist.beta.forceOobLogin": {"type": "boolean", "default": false, "markdownDescription": "Force first party auth to use OOB login (non localhost callback)", "deprecationMessage": "Internal feature flag."}, "geminicodeassist.beta.enableSidebarChatDiff": {"type": "boolean", "default": true, "markdownDescription": "Enable sidebar chat diff", "deprecationMessage": "Internal feature flag."}, "geminicodeassist.enable": {"type": "boolean", "default": true, "markdownDescription": "Enable Gemini Code Assist. Gemini Code Assist is your AI-powered collaborator to help you accomplish tasks more efficiently. [Learn more](https://developers.google.com/gemini-code-assist/ui/docs)\n\n**Note:**  Changing this setting requires a reload of VS Code. [Click here](command:workbench.action.reloadWindow) to reload."}, "geminicodeassist.project": {"type": "string", "markdownDescription": "Enter a Google Cloud project to use for Gemini Code Assist Standard or Enterprise.\n\nThis does not apply to Gemini Code Assist for individuals. If you are using Gemini Code Assist for individuals, and you'd like to use Google Code Assist with your Google Cloud Account, you can upgrade to Gemini Code Assist Standard or Enterprise. [Learn more](https://developers.google.com/gemini-code-assist/ui/versions)"}, "geminicodeassist.verboseLogging": {"type": "boolean", "default": false, "description": "Display extension debug information in the 'Gemini Code Assist Debug' output window."}, "geminicodeassist.enableTelemetry": {"type": "boolean", "default": true, "markdownDescription": "Enable sending usage statistics and crash reports to Google to improve its products and services.\n\nUsage statistics are subject to the [Google Privacy Policy](https://policies.google.com/privacy).\n\nThis setting does not implicate Google's use of your prompts, context code, and responses to train AI models.\n\nGemini Code Assist Standard and Enterprise editions never use your prompts, context code, or responses to train machine learning models.\n\nIf you're using Gemini Code Assist for individuals, you can opt out of Google's use of your prompts, context code, and responses to train AI models in the [Gemini Code Assist for individuals privacy settings](command:geminicodeassist.privacyNotice)."}, "geminicodeassist.recitation.maxCitedLength": {"type": "number", "default": -1, "markdownDescription": "This will block code suggestions which cite snippets greater than the length below.\n\n(-1 : disable blocking; 0 : block all code suggestions with citations)."}, "geminicodeassist.inlineSuggestions.enableAuto": {"type": "boolean", "default": true, "markdownDescription": "Enable automatically providing inline suggestions. If set to false, inline suggestions can still be obtained manually with the 'Trigger Inline Suggestion' command (creating a keybind is recommended)."}, "geminicodeassist.inlineSuggestions.suggestionSpeed": {"type": "string", "enum": ["Fast", "Moderate", "Slow"], "default": "Moderate", "description": "Controls the delay of displaying inline suggestions in code editor. Choose how quickly code suggestions appear as you type."}, "geminicodeassist.codeGenerationPaneViewEnabled": {"type": "boolean", "markdownDescription": "Enable the panel view interface shown when generating code via the 'Ctrl+Enter' hotkey. When enabled, this will result in code generation suggestions being opened in a separate tab in the editor. While disabled, this will result in suggestions rendering in the editor via ghost text.", "default": false}, "geminicodeassist.contextExclusionGitignore": {"type": "boolean", "default": true, "markdownDescription": "Use .gitignore to specify files for AI to ignore. If .aiexclude and .gitignore are both in use, and there is a conflict, .aiexclude will take priority. [Learn more.](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file)"}, "geminicodeassist.contextExclusionFile": {"type": "string", "default": ".aiexclude", "markdownDescription": "Specify a file containing patterns for files and folders that AI should ignore. Use the same format as a .gitignore file. Anything that matches will not be available for use in chat context. [Learn more.](https://developers.google.com/gemini-code-assist/docs/create-aiexclude-file)"}, "geminicodeassist.localCodebaseAwareness": {"type": "boolean", "default": true, "markdownDescription": "Local codebase awareness improves the relevance of Gemini Code Assist's responses through indexing and supporting techniques."}, "geminicodeassist.customCommands": {"type": "object", "markdownDescription": "Custom commands available in smart actions. Defined with key value pairs.\n\n **Usage Example:** \n\n **Item**: `add-comments` \n\n **Value**: `add some comments to my code` \n\n This will result in an `/add-comments` command becoming available in the inline chat menu and smart actions menu.\n\n Invoking this command will then trigger a chat request with the provided value as the prompt.", "additionalProperties": {"type": "string"}}, "geminicodeassist.rules": {"type": "string", "markdownDescription": "Rules that Gemini Code Assist should use.\n\n**Note**: If this setting is modified at workspace level, both user-level and workspace-level rules are used.", "editPresentation": "multilineText", "default": "\n\n\n\n", "scope": "language-overridable"}, "geminicodeassist.chat.automaticScrolling": {"type": "boolean", "markdownDescription": "Automatically scroll the chat during Gemini response streaming.", "default": "true"}, "geminicodeassist.languages": {"type": "array", "markdownDescription": "The list of language identifiers or file glob based specifiers to use for providing inline suggestions.  The default is to provide inline suggestions for all languages.\n\n**Note:**  Changing this setting requires a reload of VS Code. [Click here](command:workbench.action.reloadWindow) to reload.", "uniqueItems": true, "default": ["*"], "items": {"type": "string", "enum": ["*", "abap", "bat", "bibtex", "c", "clojure", "coffeescript", "cpp", "csharp", "css", "cuda-cpp", "dart", "diff", "dockercompose", "dockerfile", "fsharp", "git-commit", "git-rebase", "go", "groovy", "haml", "handlebars", "html", "ini", "jade", "java", "javascript", "javascriptreact", "json", "jsonc", "jsonl", "kotlin", "latex", "less", "lookml", "lua", "makefile", "markdown", "objective-c", "objective-cpp", "oraclesql", "perl", "perl6", "php", "plaintext", "powershell", "pug", "python", "r", "razor", "ruby", "rust", "sass", "scala", "scss", "shaderlab", "shellscript", "slim", "sql", "stylus", "swift", "systemverilog", "terraform", "terraform-vars", "tex", "tf", "tfvars", "typescript", "typescriptreact", "verilog", "vue", "vue-html", "xml", "xsl", "yaml"]}}, "geminicodeassist.updateChannel": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Insiders"], "description": "Automatically install the insiders version of Gemini Code Assist by changing this value to 'Insiders', or set the value as 'Default' to use the stable released version from the marketplace."}, "geminicodeassist.chat.defaultCodeBlockDisplay": {"type": "string", "enum": ["Expanded", "Preview", "Collapsed"], "default": "Preview", "markdownDescription": "Choose the default display mode for code blocks in chat.\n\n**Note:**  Changing this setting requires a reload of VS Code. [Click here](command:workbench.action.reloadWindow) to reload."}, "geminicodeassist.chat.enableStreaming": {"type": "boolean", "default": true, "markdownDescription": "Enable streaming for chat responses. When enabled, chat responses will appear incrementally as they are generated. When disabled, the chat will wait for the entire response to be generated before displaying it.\n\n**Note:**  Changing this setting requires a reload of VS Code. [Click here](command:workbench.action.reloadWindow) to reload."}, "geminicodeassist.chat.changeView": {"type": "string", "enum": ["Inline suggestions", "Default diff view"], "default": "Inline suggestions", "description": "Sets the default layout for viewing Gemini’s code changes: display them directly in your current file or use default VS Code diff layout."}, "cloudcode.duetAI.project": {"type": "string", "markdownDescription": "Enter a Google Cloud project to use for Gemini Code Assist Standard or Enterprise.\n\nThis does not apply to Gemini Code Assist for individuals. If you are using Gemini Code Assist for individuals, and you'd like to use Google Code Assist with your Google Cloud Account, you can upgrade to Gemini Code Assist Standard or Enterprise. [Learn more](https://developers.google.com/gemini-code-assist/ui/versions)", "deprecationMessage": "This setting has been moved to geminicodeassist.project"}, "geminicodeassist.a2a.address": {"type": "string", "markdownDescription": "The address for the A2A (Agent-to-Agent) server used by Gemini Code Assist.", "deprecationMessage": "Internal feature flag."}}}, "commands": [{"command": "geminicodeassist.signin", "title": "Sign In", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.signout", "title": "Sign Out", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.startagent", "title": "Start Agent", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.stopagent", "title": "Stop Agent", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.take<PERSON>urvey", "title": "Take Survey", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.crashFeedback", "title": "Send Feedback", "category": "Gemini Code Assist", "icon": "$(feedback)"}, {"command": "geminicodeassist.sdlcagents.agentBoard", "title": "Kanban Board", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.generateCode", "category": "Gemini Code Assist", "title": "Generate code"}, {"command": "geminicodeassist.addFileToContext", "category": "Gemini Code Assist", "title": "Add file to Gemini context"}, {"command": "geminicodeassist.addFilesToContext", "category": "Gemini Code Assist", "title": "Add files to Gemini context"}, {"command": "geminicodeassist.addFolderToContext", "category": "Gemini Code Assist", "title": "Add folder to Gemini context"}, {"command": "geminicodeassist.addFoldersToContext", "category": "Gemini Code Assist", "title": "Add folders to Gemini context"}, {"command": "geminicodeassist.addSnippetToContext", "category": "Gemini Code Assist", "title": "Add Snippet to Gemini Context"}, {"command": "geminicodeassist.rejectCompletion", "category": "Gemini Code Assist", "title": "Reject Suggestion"}, {"command": "geminicodeassist.codelens.accept", "category": "Gemini Code Assist (Quickpick Chat)", "icon": "$(pass)", "title": "Accept"}, {"command": "geminicodeassist.showinEditor", "category": "Gemini Code Assist (Quickpick Chat)", "title": "Show Quickpick Chat"}, {"command": "_cloudcode.duetAI.codelens.acceptNav", "category": "Gemini Code Assist (Quickpick Chat)", "title": " Accept "}, {"command": "_cloudcode.duetAI.diffView.prevSuggestion", "category": "Gemini Code Assist (Quickpick Chat)", "title": " Previous Suggestion "}, {"command": "_cloudcode.duetAI.diffView.nextSuggestion", "category": "Gemini Code Assist (Quickpick Chat)", "title": " Next Suggestion "}, {"command": "_cloudcode.duetAI.codelens.rejectNav", "category": "Gemini Code Assist (Quickpick Chat)", "title": " Decline "}, {"command": "geminicodeassist.codelens.reject", "category": "Gemini Code Assist (Quickpick Chat)", "icon": "$(error)", "title": "Decline"}, {"command": "_cloudcode.duetAI.codelens.spacer", "category": "Cloud Code", "title": "|"}, {"command": "cloudcode.duetAI.actionsMenu", "category": "Cloud Code", "title": "Gemini Code Assist: Smart Actions", "icon": {"light": "images/light/spark.svg", "dark": "images/dark/spark.svg"}, "enablement": "!subscriptionRequired"}, {"command": "geminicodeassist.sdlcagents.agentBoard", "category": "Gemini Code Assist", "title": "Open Kanban Board", "icon": "$(project)"}, {"command": "geminicodeassist.viewCodeDocumentation", "category": "Gemini Code Assist", "title": "Code Documentation", "icon": "$(library)", "when": "false"}, {"command": "cloudcode.duetAI.openKeybindings", "category": "Gemini Code Assist", "title": "Edit Keyboard Shortcuts", "icon": "$(record-keys)"}, {"command": "geminicodeassist.editor.addSelectionToChatContext", "category": "Gemini Code Assist", "title": "Add Selection to Chat Context"}, {"command": "geminicodeassist.terminal.addSelectionToChatContext", "category": "Gemini Code Assist", "title": "Gemini Code Assist: Add Selection to Chat Context"}, {"command": "cloudcode.duetAI.rightClick.chatGenerateTests", "category": "Gemini Code Assist", "title": "Generate Unit Tests", "icon": "images/chat_spark.svg"}, {"command": "geminicodeassist.privacyNotice", "category": "Gemini Code Assist", "title": "Privacy Settings"}, {"command": "geminicodeassist.releaseNotes", "category": "Gemini Code Assist", "title": "What's New (Release Notes)"}, {"command": "geminicodeassist.showUpgradeWebUi", "category": "Gemini Code Assist", "title": "Upgrade to Gemini Code Assist Standard"}, {"command": "geminicodeassist.manageSubscription", "category": "Gemini Code Assist", "title": "Manage Subscription"}, {"command": "geminicodeassist.subscriptionRequired", "category": "Gemini Code Assist", "title": "Gemini Code Assist"}, {"command": "cloudcode.gemini.chatView.focus", "category": "Gemini Code Assist", "title": "Open Chat"}, {"command": "geminicodeassist.chat.resume", "category": "Gemini Code Assist (Chat)", "title": "Resume Previous Chat...", "icon": "$(history)", "enablement": "geminicodeassist.chat.userHasPreviousThreads && !geminicodeassist.chat.isConversationRequestInProgress && !geminicodeassist.chat.isToolApprovalPending"}, {"command": "geminicodeassist.chat.new", "category": "Gemini Code Assist (Chat)", "title": "New Chat", "icon": "$(plus)", "enablement": "geminicodeassist.chat.isThreadActive && !geminicodeassist.chat.isConversationRequestInProgress && !geminicodeassist.chat.isToolApprovalPending"}, {"command": "geminicodeassist.chat.fork", "title": "Fork Chat"}, {"command": "geminicodeassist.softwareLicensesPrivacyPolicy", "title": "Show Software Licenses and Privacy", "category": "Gemini Code Assist"}, {"command": "gemini.inlineSuggestion.acceptAll", "title": "Accept file", "category": "Gemini Code Assist"}, {"command": "gemini.inlineSuggestion.rejectAll", "title": "Reject file", "category": "Gemini Code Assist"}, {"command": "gemini.inlineSuggestion.diffView", "title": "Diff view", "category": "Gemini Code Assist"}, {"command": "geminicodeassist.intentAwareCompletion.jumpAndAccept", "title": "Jump and accept intent-aware completions"}], "keybindings": [{"command": "geminicodeassist.saveKeysClicked", "key": "cmd+s", "win": "ctrl+s", "mac": "cmd+s", "when": "inGeminiDiffView || isInDiffEditor"}, {"command": "geminicodeassist.generateCode", "key": "ctrl+enter", "when": "config.geminicodeassist.enable && editorTextFocus && authLoggedIn && !inGeminiDiffView && !isInDiffEditor", "args": {"source": "keybind"}}, {"command": "geminicodeassist.showinEditor", "mac": "cmd+i", "win": "ctrl+i", "linux": "ctrl+i", "when": "config.geminicodeassist.enable && editorTextFocus && authLoggedIn && shouldRegisterInEditorCommand && !inGeminiDiffView && !isInDiffEditor"}, {"command": "geminicodeassist.codelens.accept", "key": "alt+a", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor"}, {"command": "geminicodeassist.codelens.reject", "key": "alt+d", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor"}, {"command": "geminicodeassist.rejectCompletion", "key": "escape", "when": "config.geminicodeassist.enable && authLoggedIn && inlineSuggestionVisible"}, {"command": "workbench.view.extension.geminiChat", "win": "alt+g", "linux": "alt+g", "mac": "cmd+alt+g", "when": "!runningOnCloudShell"}, {"command": "cloudcode.gemini.chatView.focus", "mac": "alt+g", "when": "!runningOnCloudShell && !inputFocus"}, {"command": "geminicodeassist.editor.addSelectionToChatContext", "win": "ctrl+alt+x", "linux": "ctrl+alt+x", "mac": "cmd+alt+x", "when": "!terminalTextSelectedInFocused"}, {"command": "geminicodeassist.terminal.addSelectionToChatContext", "win": "ctrl+alt+x", "linux": "ctrl+alt+x", "mac": "cmd+alt+x", "when": "terminalTextSelectedInFocused"}, {"command": "geminicodeassist.intentAwareCompletion.jumpAndAccept", "key": "tab", "when": "config.geminicodeassist.enable && editorTextFocus && authLoggedIn && geminicodeassist.intentAwareCompletion.isJumpOrAcceptAvailable"}, {"command": "geminicodeassist.intentAwareCompletion.escape", "key": "escape", "when": "config.geminicodeassist.enable && editorTextFocus && authLoggedIn && geminicodeassist.intentAwareCompletion.hasSuggestions"}], "icons": {"material-spark-off": {"description": "Material Spark Off", "default": {"fontPath": "fonts/material/material.woff", "fontCharacter": "\\e900"}}, "material-spark": {"description": "Material Spark", "default": {"fontPath": "fonts/material/material.woff", "fontCharacter": "\\e901"}}, "codelens-material-spark": {"description": "Codelens Material Spark", "default": {"fontPath": "fonts/material/codelens_material.woff", "fontCharacter": "\\e900"}}, "material-spark-disabled": {"description": "Material Spark Disabled", "default": {"fontPath": "fonts/material/material-spark-disabled.woff", "fontCharacter": "\\e900"}}}, "menus": {"commandPalette": [{"command": "geminicodeassist.signin", "when": "!runningOnCloudShell && !authLoggedIn"}, {"command": "geminicodeassist.startagent", "when": "geminicodeassist.overrides.enableLocalAgent"}, {"command": "geminicodeassist.stopagent", "when": "geminicodeassist.overrides.enableLocalAgent"}, {"command": "geminicodeassist.signout", "when": "!runningOnCloudShell && authLoggedIn"}, {"command": "cloudcode.duetAI.actionsMenu", "when": "config.geminicodeassist.enable && authLoggedIn && !inGeminiDiffView && !isInDiffEditor"}, {"command": "geminicodeassist.generateCode", "when": "config.geminicodeassist.enable && authLoggedIn && !inGeminiDiffView && !isInDiffEditor"}, {"command": "cloudcode.gemini.chatView.focus", "when": "config.geminicodeassist.enable"}, {"command": "geminicodeassist.editor.addSelectionToChatContext", "when": "false"}, {"command": "geminicodeassist.terminal.addSelectionToChatContext", "when": "false"}, {"command": "geminicodeassist.addSnippetToContext", "when": "false"}, {"command": "geminicodeassist.addFileToContext", "when": "false"}, {"command": "geminicodeassist.addFilesToContext", "when": "false"}, {"command": "geminicodeassist.addFolderToContext", "when": "false"}, {"command": "geminicodeassist.addFoldersToContext", "when": "false"}, {"command": "cloudcode.duetAI.rightClick.chatGenerateTests", "when": "config.geminicodeassist.enable && authLoggedIn"}, {"command": "geminicodeassist.sdlcagents.agentBoard", "when": "config.geminicodeassist.enable && authLoggedIn && enableAgentBoard"}, {"command": "geminicodeassist.privacyNotice", "when": "geminicodeassist.showNotice"}, {"command": "geminicodeassist.chat.resume", "when": "config.geminicodeassist.enable && authLoggedIn"}, {"command": "geminicodeassist.chat.new", "when": "config.geminicodeassist.enable && authLoggedIn"}, {"command": "geminicodeassist.chat.fork", "when": "false"}, {"command": "geminicodeassist.codelens.accept", "when": "false"}, {"command": "_cloudcode.duetAI.codelens.acceptNav", "when": "false"}, {"command": "geminicodeassist.codelens.reject", "when": "false"}, {"command": "_cloudcode.duetAI.codelens.rejectNav", "when": "false"}, {"command": "_cloudcode.duetAI.codelens.spacer", "when": "false"}, {"command": "_cloudcode.duetAI.diffView.prevSuggestion", "when": "false"}, {"command": "_cloudcode.duetAI.diffView.nextSuggestion", "when": "false"}, {"command": "gemini.inlineSuggestion.acceptAll", "when": "geminicodeassist.suggestionActive"}, {"command": "gemini.inlineSuggestion.rejectAll", "when": "geminicodeassist.suggestionActive"}, {"command": "gemini.inlineSuggestion.diffView", "when": "geminicodeassist.suggestionActive"}], "editor/title": [{"command": "cloudcode.duetAI.actionsMenu", "group": "navigation", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && !inGeminiDiffView && !isInDiffEditor && !geminicodeassist.suggestionActive"}, {"command": "_cloudcode.duetAI.diffView.prevSuggestion", "group": "navigation@0", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor && inGeminiDiffViewMultiSuggestion"}, {"command": "_cloudcode.duetAI.diffView.nextSuggestion", "group": "navigation@1", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor && inGeminiDiffViewMultiSuggestion"}, {"command": "_cloudcode.duetAI.codelens.spacer", "group": "navigation@2", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor && inGeminiDiffViewMultiSuggestion"}, {"command": "_cloudcode.duetAI.codelens.acceptNav", "group": "navigation@3", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor && !geminicodeassist.suggestionActive"}, {"command": "_cloudcode.duetAI.codelens.rejectNav", "group": "navigation@4", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor && !geminicodeassist.suggestionActive"}, {"command": "_cloudcode.duetAI.codelens.spacer", "group": "navigation@5", "when": "config.geminicodeassist.enable && cloudcode.duetAI.documentMatch && authLoggedIn && inGeminiDiffView && isInDiffEditor"}, {"command": "_cloudcode.duetAI.codelens.spacer", "group": "navigation@1", "when": "geminicodeassist.suggestionActive"}, {"command": "gemini.inlineSuggestion.acceptAll", "group": "navigation@2", "when": "geminicodeassist.suggestionActive"}, {"command": "gemini.inlineSuggestion.rejectAll", "group": "navigation@3", "when": "geminicodeassist.suggestionActive"}, {"command": "_cloudcode.duetAI.codelens.spacer", "group": "navigation@4", "when": "geminicodeassist.suggestionActive && !geminicodeassist.diffViewActive"}, {"command": "gemini.inlineSuggestion.diffView", "group": "navigation@5", "when": "geminicodeassist.suggestionActive && !geminicodeassist.diffViewActive"}], "view/title": [{"command": "geminicodeassist.viewCodeDocumentation", "group": "navigation@1", "when": "config.geminicodeassist.beta.enableCodeDocumentationButton && view == cloudcode.gemini.chatView"}, {"command": "geminicodeassist.sdlcagents.agentBoard", "group": "navigation@2", "when": "config.geminicodeassist.enable && authLoggedIn && enableAgentBoard && view == cloudcode.gemini.chatView"}, {"command": "geminicodeassist.crashFeedback", "group": "navigation@3", "when": "view == cloudcode.gemini.chatView"}, {"command": "geminicodeassist.chat.new", "group": "navigation@4", "when": "view == cloudcode.gemini.chatView"}, {"command": "geminicodeassist.chat.resume", "group": "navigation@5", "when": "view == cloudcode.gemini.chatView"}, {"submenu": "cloudcode.gemini.chatView.submenu", "group": "navigation@6", "when": "view == cloudcode.gemini.chatView"}], "explorer/context": [{"command": "geminicodeassist.addFileToContext", "when": "config.geminicodeassist.enable && authLoggedIn && resourceScheme == 'file' && !listMultiSelection && !explorerResourceIsFolder", "group": "geminicodeassist"}, {"command": "geminicodeassist.addFilesToContext", "when": "config.geminicodeassist.enable && authLoggedIn && resourceScheme == 'file' && listMultiSelection && !explorerResourceIsFolder", "group": "geminicodeassist"}, {"command": "geminicodeassist.addFolderToContext", "when": "config.geminicodeassist.enable && authLoggedIn && resourceScheme == 'file' && !listMultiSelection && explorerResourceIsFolder", "group": "geminicodeassist"}, {"command": "geminicodeassist.addFoldersToContext", "when": "config.geminicodeassist.enable && authLoggedIn && resourceScheme == 'file' && listMultiSelection && explorerResourceIsFolder", "group": "geminicodeassist"}], "editor/context": [{"submenu": "cloudcode.gemini.submenu", "when": "config.geminicodeassist.enable && editorTextFocus && authLoggedIn", "group": "cloudcode@0"}, {"command": "geminicodeassist.subscriptionRequired", "group": "cloudcode@0", "when": "subscriptionRequired"}], "terminal/context": [{"command": "geminicodeassist.terminal.addSelectionToChatContext", "when": "config.geminicodeassist.enable && authLoggedIn", "group": "geminicodeassist"}], "cloudcode.gemini.submenu": [{"command": "geminicodeassist.generateCode", "group": "1", "when": "!inGeminiDiffView && !isInDiffEditor && !subscriptionRequired"}, {"command": "geminicodeassist.editor.addSelectionToChatContext", "group": "2", "when": "!subscriptionRequired"}, {"command": "cloudcode.duetAI.rightClick.chatGenerateTests", "group": "1", "when": "!subscriptionRequired"}, {"command": "geminicodeassist.addFileToContext", "group": "3", "when": "config.geminicodeassist.enable && authLoggedIn"}], "cloudcode.gemini.chatView.submenu": [{"command": "cloudcode.duetAI.openKeybindings", "group": "navigation@0", "when": "view == cloudcode.gemini.chatView"}, {"command": "geminicodeassist.privacyNotice", "group": "navigation@1", "when": "geminicodeassist.showNotice"}, {"command": "geminicodeassist.releaseNotes", "group": "navigation@2", "when": "geminicodeassist.overrides.displayReleaseNotes"}, {"command": "geminicodeassist.showUpgradeWebUi", "group": "navigation@3", "when": "geminicodeassist.overrides.enableUpgradeFromFreeTier && geminicodeassist.eligibleForUpgrade"}, {"command": "geminicodeassist.manageSubscription", "group": "navigation@4", "when": "geminicodeassist.overrides.enableUpgradeFromFreeTier && geminicodeassist.hasSubscriptionUri"}, {"command": "geminicodeassist.signout", "group": "navigation@5", "when": "view == cloudcode.gemini.chatView && !runningOnCloudShell && authLoggedIn"}, {"command": "geminicodeassist.signin", "group": "navigation@5", "when": "view == cloudcode.gemini.chatView && !runningOnCloudShell && !authLoggedIn"}]}, "submenus": [{"id": "cloudcode.gemini.submenu", "label": "Gemini Code Assist"}, {"id": "cloudcode.gemini.chatView.submenu", "label": "Gemini Code Assist", "icon": "$(ellipsis)"}], "viewsContainers": {"activitybar": [{"id": "geminiChat", "icon": "fonts/material/characters/material-spark.svg", "title": "Gemini Code Assist"}]}, "views": {"geminiChat": [{"id": "cloudcode.gemini.chatView", "icon": "fonts/material/characters/material-spark.svg", "name": "Cha<PERSON>", "type": "webview", "when": "config.geminicodeassist.enable"}]}}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/google.geminicodeassist-2.43.0", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Google", "metadata": {"installedTimestamp": 1754245465360, "source": "gallery", "id": "51643712-2cb2-4384-b7cc-d55b01b8274b", "publisherId": "93a45bde-b507-401c-9deb-7a098ebcded8", "publisherDisplayName": "Google", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 232917616}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "ms-windows-ai-studio.windows-ai-studio", "uuid": "cf78d6ef-45ff-428f-875b-7fb6251b66af"}, "manifest": {"name": "windows-ai-studio", "displayName": "AI Toolkit for Visual Studio Code", "description": "AI Toolkit for VS Code streamlines generative AI app development by integrating tools and models. Browse and download public and custom models; author, test and evaluate prompts; fine-tune; and use them in your applications.", "version": "0.18.2", "publisher": "ms-windows-ai-studio", "icon": "media/Market_AIToolkit_128.png", "author": {"name": "Microsoft"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/windows-ai-studio.git"}, "bugs": {"url": "https://github.com/Microsoft/windows-ai-studio/issues"}, "license": "SEE LICENSE IN LICENSE.txt", "engines": {"vscode": "^1.98.0"}, "extensionKind": ["workspace"], "categories": ["AI", "Azure", "Cha<PERSON>", "Data Science", "Machine Learning", "Other"], "extensionPack": ["TeamsDevApp.vscode-ai-foundry"], "preview": true, "keywords": ["AI", "Azure", "Cha<PERSON>", "Data Science", "Machine Learning", "studio", "windows", "models", "LLM"], "activationEvents": ["onUri", "workspaceContains:infra/finetuning.config.json", "workspaceContains:infra/inference.config.json", "workspaceContains:prompt.aitk.txt", "workspaceContains:.aitk/*", "workspaceContains:model_lab.workspace.config"], "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "main": "./dist/extension.js", "l10n": "./l10n", "contributes": {"configuration": {"title": "AI Toolkit", "properties": {"windowsaistudio.tracingGrpcPort": {"type": "integer", "default": 4317, "description": "The port to use for the OpenTelemetry OTLP gRPC receiver"}, "windowsaistudio.tracingHttpPort": {"type": "integer", "default": 4318, "description": "The port to use for the OpenTelemetry OTLP HTTP receiver"}, "windowsaistudio.enableRemoteFine-tuningAndInference": {"type": "boolean", "default": false, "description": "Enable to run fine-tuning and inference on Azure Container Apps (Reload Visual Studio Code after changing this setting to take effect)."}, "windowsaistudio.templateRef": {"type": ["string", "null"], "default": null, "description": "Branch to pull from the template repo"}, "windowsaistudio.openAIInferencePort": {"type": "integer", "default": null, "description": "The Open AI inference http local port"}, "windowsaistudio.workspaceAgentPipe": {"type": "string", "default": null, "description": "Workspace Agent Pipe name"}, "windowsaistudio.playgroundAgentPipe": {"type": "string", "default": null, "description": "Playground Agent Pipe name"}, "windowsaistudio.playgroundAgentModelStorage": {"type": "string", "default": null, "description": "Playground Agent model storage path"}, "windowsaistudio.remoteInfereneEndpoints": {"type": "array", "description": "List of remote inference endpoints", "items": {"type": "object", "order": 1, "description": "Remote inference endpoint", "oneOf": [{"type": "object", "properties": {"type": {"const": "Custom"}, "name": {"type": "string", "description": "Name of the inference endpoint", "examples": ["gpt-4o-mini"]}, "displayName": {"type": "string", "description": "Display Name used in the UI", "examples": ["my-remote-gpt-4o-mini"]}, "chatCompletionUrl": {"type": "string", "description": "The endpoint URL", "examples": ["https://YOUR_RESOURCE_NAME.openai.azure.com/openai/deployments/YOUR_DEPLOYMENT_ID/chat/completions?api-version=2024-06-01"]}, "headers": {"type": "object", "patternProperties": {"^.*$": {"type": "string"}}, "description": "Headers for API key authentication", "additionalProperties": false, "examples": [{"api-key": "******"}]}}, "additionalProperties": false, "default": {"type": "Custom", "name": "<Some name>", "chatCompletionUrl": "https://YOUR_RESOURCE_NAME.openai.azure.com/openai/deployments/YOUR_DEPLOYMENT_ID/chat/completions?api-version=2024-06-01", "headers": {"api-key": "******"}}}, {"type": "object", "properties": {"type": {"const": "GitHub"}, "id": {"type": "string", "description": "Model ID for GitHub model", "examples": ["azureml://registries/azure-openai/models/gpt-4o/versions/2"]}}, "additionalProperties": false, "default": {"type": "GitHub", "name": "gpt-4o-mini"}}]}}, "windowsaistudio.externalOpenAIProviders": {"type": "array", "description": "List of external providers to register", "items": {"type": "object", "order": 1, "description": "External OpenAI provider", "properties": {"TypeName": {"type": "string", "description": "Type Name of the external provider"}, "ModelName": {"type": "string", "description": "Name of the AI model to expose"}, "BaseUri": {"type": "string", "description": "The Base URI where the external provider receive http requests"}}, "additionalProperties": false, "default": {"TypeName": "Python", "ModelName": "<Some model>", "BaseUri": "http://localhost:8000"}}, "uniqueItems": true}, "windowsaistudio.modelLabAcceptedDatasets": {"type": "array", "description": "User accepted datasets in Model Lab", "items": {"type": "string"}, "default": [], "uniqueItems": true}, "windowsaistudio.modelLabGlobalCachePath": {"type": "string", "description": "If set, use this global shared cache instead of per project in Model Lab", "default": null}}}, "viewsContainers": {"activitybar": [{"id": "ai-toolkit-explorer", "title": "AI Toolkit", "icon": "media/AIToolkit.svg"}]}, "views": {"ai-toolkit-explorer": [{"id": "aiToolkitMyModels", "name": "My Models", "icon": "media/AIToolkit.svg", "contextualTitle": "My Models"}, {"id": "aiToolkitModels", "name": "Models", "icon": "media/AIToolkit.svg", "contextualTitle": "Models"}, {"id": "aiToolkitTools", "name": "Tools", "icon": "media/AIToolkit.svg", "contextualTitle": "Tools"}, {"id": "aiToolkitHelpAndFeedback", "name": "Help and Feedback", "icon": "media/AIToolkit.svg", "contextualTitle": "Help and Feedback"}]}, "menus": {"view/item/context": [{"command": "ai-mlstudio.treeView.models.loadInPlayground", "when": "view == aiToolkitMyModels && viewItem =~ /<loadInPlayground>/", "group": "1-model-action@1"}, {"command": "ai-mlstudio.treeView.models.loadInAgentBuilder", "when": "view == aiToolkitMyModels && viewItem =~ /<loadInAgentBuilder>/", "group": "1-model-action@2"}, {"command": "ai-mlstudio.treeView.models.createFineTuneProject", "when": "view == aiToolkitMyModels && viewItem =~ /<createFineTuneProject>/", "group": "1-model-action@3"}, {"command": "ai-mlstudio.treeView.models.copyModelName", "when": "view == aiToolkitMyModels && viewItem =~ /<copyModelName>/", "group": "2-model-management@1"}, {"command": "ai-mlstudio.treeView.models.edit", "when": "view == aiToolkitMyModels && viewItem =~ /<edit>/", "group": "2-model-management@2"}, {"command": "ai-mlstudio.treeView.models.refresh", "when": "view == aiToolkitMyModels && viewItem =~ /<refresh>/", "group": "2-model-management@3"}, {"command": "ai-mlstudio.treeView.models.delete", "when": "view == aiToolkitMyModels && viewItem =~ /<delete>/", "group": "2-model-management@4"}, {"command": "ai-mlstudio.treeView.models.cancelDownloading", "when": "view == aiToolkitMyModels && viewItem =~ /<cancelDownloading>/", "group": "2-model-management@5"}, {"command": "ai-mlstudio.treeView.models.about", "when": "view == aiToolkitMyModels && viewItem =~ /<about>/", "group": "3-model-about@1"}, {"command": "ai-mlstudio.treeView.models.startServer", "when": "view == aiToolkitMyModels && viewItem == ONNX", "group": "1-endpoint-management@1"}, {"command": "ai-mlstudio.treeView.models.stopServer", "when": "view == aiToolkitMyModels && viewItem == ONNX", "group": "1-endpoint-management@2"}, {"command": "ai-mlstudio.treeView.models.copyEndpoint", "when": "view == aiToolkitMyModels && viewItem == ONNX", "group": "1-endpoint-management@3"}, {"command": "ai-mlstudio.treeView.models.foundry.selectProject", "when": "view == aiToolkitMyModels && viewItem == 'Azure AI Foundry'", "group": "1-foundry-management@1"}, {"command": "ai-mlstudio.treeView.models.foundry.selectProject", "when": "view == aiToolkitMyModels && viewItem == 'Azure AI Foundry'", "group": "inline"}], "view/title": [{"command": "ai-mlstudio.treeView.models.addRemoteInferenceEndpoint", "when": "view == aiToolkitMyModels", "group": "navigation"}, {"command": "ai-mlstudio.treeView.models.remoteInferenceInPlaygroundDocs", "when": "view == aiToolkitMyModels", "group": "navigation"}], "explorer/context": [{"command": "ai-mlstudio.openDataset", "when": "false", "group": "navigation"}], "commandPalette": [{"command": "ai-mlstudio.treeView.models.refresh", "when": "false"}, {"command": "ai-mlstudio.treeView.models.loadInPlayground", "when": "false"}, {"command": "ai-mlstudio.treeView.models.createFineTuneProject", "when": "false"}, {"command": "ai-mlstudio.treeView.models.loadInAgentBuilder", "when": "false"}, {"command": "ai-mlstudio.treeView.models.addRemoteInferenceEndpoint", "when": "false"}, {"command": "ai-mlstudio.treeView.models.foundry.selectProject", "when": "false"}, {"command": "ai-mlstudio.treeView.models.edit", "when": "false"}, {"command": "ai-mlstudio.treeView.models.delete", "when": "false"}, {"command": "ai-mlstudio.treeView.models.cancelDownloading", "when": "false"}, {"command": "ai-mlstudio.treeView.models.about", "when": "false"}, {"command": "ai-mlstudio.treeView.models.click", "when": "false"}, {"command": "ai-mlstudio.treeView.models.startServer", "when": "false"}, {"command": "ai-mlstudio.treeView.models.stopServer", "when": "false"}, {"command": "ai-mlstudio.treeView.models.copyEndpoint", "when": "false"}, {"command": "ai-mlstudio.setupAIModel", "when": "false"}, {"command": "ai-mlstudio.deleteInferenceModel", "when": "false"}, {"command": "ai-mlstudio.executeAgentPayload", "when": "false"}, {"command": "ai-mlstudio.loadSotaModelList", "when": "false"}, {"command": "ai-mlstudio.copyGHAccessToken", "when": "false"}, {"command": "_ai-mlstudio.comingSoon", "when": "false"}, {"command": "ai-mlstudio.addRemoteInferenceEndpoint", "when": "false"}, {"command": "ai-mlstudio.treeView.models.remoteInferenceInPlaygroundDocs", "when": "false"}, {"command": "ai-mlstudio.promptBuilder", "when": "false"}, {"command": "ai-mlstudio.agentBuilder", "when": "false"}, {"command": "ai-mlstudio.openDataset", "when": "false"}]}, "viewsWelcome": [{"view": "aiToolkitMyModels", "contents": "No models yet\nStart by adding one from the Models Catalog.\n[Open Model Catalog](command:ai-mlstudio.models)"}], "commands": [{"command": "ai-mlstudio.treeView.models.refresh", "title": "Refresh"}, {"command": "ai-mlstudio.treeView.models.loadInPlayground", "title": "Load in Playground", "enablement": "!ai-mlstudio.modelLoading"}, {"command": "ai-mlstudio.treeView.models.copyModelName", "title": "Copy Model Name"}, {"command": "ai-mlstudio.treeView.models.createFineTuneProject", "title": "Create a Fine-Tuning Project"}, {"command": "ai-mlstudio.treeView.models.loadInAgentBuilder", "title": "Load in Agent Builder", "enablement": "!ai-mlstudio.modelLoading"}, {"command": "ai-mlstudio.treeView.models.addRemoteInferenceEndpoint", "title": "Add model for remote inference", "icon": "$(add)"}, {"command": "ai-mlstudio.treeView.models.remoteInferenceInPlaygroundDocs", "title": "Learn more about remote inference in playground", "icon": "$(info)"}, {"command": "ai-mlstudio.treeView.models.edit", "title": "Edit", "enablement": "!ai-mlstudio.modelLoading"}, {"command": "ai-mlstudio.treeView.models.delete", "title": "Delete", "enablement": "!ai-mlstudio.modelLoading"}, {"command": "ai-mlstudio.treeView.models.cancelDownloading", "title": "Cancel", "enablement": "!ai-mlstudio.modelLoading"}, {"command": "ai-mlstudio.treeView.models.about", "title": "About this Model"}, {"command": "ai-mlstudio.treeView.models.click", "title": "Load in Playground"}, {"command": "ai-mlstudio.treeView.models.startServer", "title": "Start Server"}, {"command": "ai-mlstudio.treeView.models.stopServer", "title": "Stop Server"}, {"command": "ai-mlstudio.treeView.models.copyEndpoint", "title": "Copy Endpoint"}, {"command": "ai-mlstudio.treeView.models.foundry.selectProject", "title": "Select Project", "icon": "$(list-filter)"}, {"command": "ai-mlstudio.commandPalette.addRemoteInferenceEndpoint", "title": "AI Toolkit: Add model for remote inference"}, {"command": "ai-mlstudio.addRemoteInferenceEndpoint", "title": "AI Toolkit: Add model for remote inference"}, {"command": "ai-mlstudio.setupAIModel", "title": "Setup", "icon": {"light": "resources/light/edit.svg", "dark": "resources/dark/edit.svg"}}, {"command": "ai-mlstudio.setHFAccessToken", "title": "AI Toolkit: Set HuggingFace access token"}, {"command": "ai-mlstudio.setGHAccessToken", "title": "AI Toolkit: Set a GitHub access token"}, {"command": "ai-mlstudio.copyGHAccessToken", "title": "AI Toolkit: Copy the GitHub access token to the clipboard"}, {"command": "ai-mlstudio.resetAllTokens", "title": "AI Toolkit: Reset all access tokens"}, {"command": "ai-mlstudio.showWhatsNew", "title": "AI Toolkit: Show What's New"}, {"command": "ai-mlstudio.prepareEnvironment", "title": "AI Toolkit: Install environment prerequisites"}, {"command": "ai-mlstudio.loadSotaModelList", "title": "AI Toolkit: Load optimized model list"}, {"command": "_ai-mlstudio.comingSoon", "title": "AI Toolkit: Open Coming Soon"}, {"command": "ai-mlstudio.executeAgentPayload", "title": "AI Toolkit: Execute a raw payload on the AI agent"}, {"command": "_ai-mlstudio.validateRequirements", "title": "AI Toolkit: Validate environment prerequisites"}, {"command": "ai-mlstudio.provision", "title": "AI Toolkit: Provision Azure Container Apps job for fine-tuning", "enablement": "ai-mlstudio.enableRemote"}, {"command": "ai-mlstudio.run", "title": "AI Toolkit: Run fine-tuning", "enablement": "ai-mlstudio.enableRemote"}, {"command": "ai-mlstudio.provisionInference", "title": "AI Toolkit: Provision Azure Container Apps for inference", "enablement": "ai-mlstudio.enableRemote"}, {"command": "ai-mlstudio.deployInference", "title": "AI Toolkit: Deploy for inference", "enablement": "ai-mlstudio.enableRemote"}, {"command": "ai-mlstudio.downloadInferenceModel", "title": "AI Toolkit: Download an inference model"}, {"command": "ai-mlstudio.deleteInferenceModel", "title": "AI Toolkit: Delete an existing inference model"}, {"command": "ai-mlstudio.unloadInferenceModel", "title": "AI Toolkit: Unload an inference model"}, {"command": "ai-mlstudio.shutdownInferenceAgent", "title": "AI Toolkit: Shutdown Inference Agent"}, {"command": "ai-mlstudio.installCudaWslLinux", "title": "AI Toolkit: Install CUDA in Linux"}, {"command": "ai-mlstudio.installCondaWslLinux", "title": "AI Toolkit: Install Conda in Linux"}, {"command": "ai-mlstudio.showOutputChannel", "title": "AI Toolkit: Show Output Channel"}, {"command": "ai-mlstudio.streamingLog", "title": "AI Toolkit: Show the running fine-tuning job streaming logs", "enablement": "ai-mlstudio.enableRemote"}, {"command": "ai-mlstudio.editSecret", "title": "AI Toolkit: Add Azure Container Apps Job secret for fine-tuning", "enablement": "ai-mlstudio.enableRemote"}, {"command": "ai-mlstudio.models", "title": "AI Toolkit: Show model catalog", "icon": "$(library)"}, {"command": "ai-mlstudio.tutorials", "title": "AI Toolkit: Open tutorials", "icon": "$(folder-library)"}, {"command": "ai-mlstudio.promptBuilder", "title": "AI Toolkit: Open Agent Builder"}, {"command": "ai-mlstudio.agentBuilder", "title": "AI Toolkit: Open Agent Builder"}, {"command": "ai-mlstudio.openDataset", "title": "AI Toolkit: Open dataset", "enablement": "false"}, {"command": "ai-mlstudio.encryptCustomModelValue", "title": "AI Toolkit: Encrypt a value for custom model", "enablement": "false"}, {"command": "ai-mlstudio.test.happyPath", "title": "AI Toolkit: A Test Command", "enablement": "false"}, {"command": "ai-mlstudio.mcp.add", "title": "AI Toolkit: Add MCP server..."}, {"command": "ai-mlstudio.mcp.list", "title": "AI Toolkit: List MCP servers"}, {"command": "ai-mlstudio.mcp.reset", "title": "AI Toolkit: Reset cached MCP tools"}, {"command": "ai-mlstudio.mcp.select", "title": "AI Toolkit: Select tools in MCP server"}, {"command": "ai-mlstudio.tracing.startCollector", "title": "AI Toolkit: Start OpenTelemetry Tracing Collector", "enablement": "aiToolkit.tracingSupported"}, {"command": "ai-mlstudio.tracing.stopCollector", "title": "AI Toolkit: Stop OpenTelemetry Tracing Collector", "enablement": "aiToolkit.tracingSupported"}, {"command": "ai-mlstudio.tracing.showStatus", "title": "AI Toolkit: Show OpenTelemetry Tracing Collector Status", "enablement": "aiToolkit.tracingSupported"}], "walkthroughs": [{"id": "aiToolkitGetStarted", "title": "Welcome to AI Toolkit!", "description": "Quickly set up a model, try it in real time, and explore advanced features - all in one place.", "steps": [{"id": "aiToolkitDownloadModel", "title": "Explore AI models", "description": "Explore various AI models from LLM to SLM such as Phi, GPT, or Llama in the Model Catalog.\n[Open Model Catalog](command:ai-mlstudio.models?%5B%22walkthrough%22%5D)\n🚀 If you already have your own model, add it for [remote inference](command:ai-mlstudio.addRemoteInferenceEndpoint?%5B%22walkthrough%22%5D)", "media": {"svg": "resources/walkthrough/model-catalog.svg", "altText": "Explore AI models"}}, {"id": "aiToolkitTryPlayground", "title": "Experiment with AI models", "description": "Run a prompt to see how the model works in real time in the Playground. Note sure where to start?\n[Try GPT-4o mini in Playground](command:ai-mlstudio.walkthrough.tryRemoteModel)", "media": {"svg": "resources/walkthrough/try-playground.svg", "altText": "Experiment with AI models"}}, {"id": "aiToolkitExplore", "title": "Explore more", "description": "Explore all commands from the Command Palette to manage your datasets, run evaluations, and fine-tune your models.\n[Show All Commands](command:ai-mlstudio.walkthrough.showAllCommands)", "media": {"svg": "resources/walkthrough/explore.svg", "altText": "Explore more"}}]}]}, "scripts": {"build-workspace-agent-win-x64": "dotnet publish ../../src/WorkspaceAutomation.Agent/WorkspaceAutomation.Agent.csproj -c Release --runtime win-x64 /p:Platform=x64 /p:DebugSymbols=false /p:DebugType=None", "build-inference-agent-win-x64": "dotnet publish ../../src/Inference.Service/Inference.Service.Agent/Inference.Service.Agent.csproj -c Release --runtime win-x64 /p:Platform=x64 /p:DebugSymbols=false /p:DebugType=None", "build-workspace-agent-win-arm64": "dotnet publish ../../src/WorkspaceAutomation.Agent/WorkspaceAutomation.Agent.csproj -c Release --runtime win-arm64 /p:Platform=arm64 /p:DebugSymbols=false /p:DebugType=None", "build-inference-agent-win-arm64": "dotnet publish ../../src/Inference.Service/Inference.Service.Agent/Inference.Service.Agent.csproj -c Release --runtime win-arm64 /p:Platform=arm64 /p:DebugSymbols=false /p:DebugType=None", "build-workspace-agent-linux-x64": "dotnet publish ../../src/WorkspaceAutomation.Agent/WorkspaceAutomation.Agent.csproj -c Release --runtime linux-x64 /p:Platform=x64 /p:DebugSymbols=false /p:DebugType=None", "build-inference-agent-linux-x64": "dotnet publish ../../src/Inference.Service/Inference.Service.Agent/Inference.Service.Agent.csproj -c Release --runtime linux-x64 /p:Platform=x64 /p:DebugSymbols=false /p:DebugType=None", "build-inference-agent-osx-arm64": "dotnet publish ../../src/Inference.Service/Inference.Service.Agent/Inference.Service.Agent.csproj -c Release --runtime osx-arm64 /p:Platform=arm64 /maxcpucount:1 /p:DebugSymbols=false /p:DebugType=None", "build-workspace-agent-osx-arm64": "dotnet publish ../../src/WorkspaceAutomation.Agent/WorkspaceAutomation.Agent.csproj -c Release --runtime osx-arm64 /p:Platform=arm64 /maxcpucount:1 /p:DebugSymbols=false /p:DebugType=None", "build-inference-agent-osx-x64": "echo no-op", "build-workspace-agent-osx-x64": "echo no-op", "build-inference-agent-universal": "echo no-op", "build-workspace-agent-universal": "echo no-op", "package-linux-x64": "npm run set-version && vsce package --target linux-x64 && npm run reset-version", "package-darwin-arm64": "npm run set-version && vsce package --target darwin-arm64 && npm run reset-version", "package-darwin-x64": "npm run set-version && vsce package --target darwin-x64 && npm run reset-version", "package-win32-x64": "npm run set-version && vsce package --target win32-x64 && npm run reset-version", "package-win32-arm64": "npm run set-version && vsce package --target win32-arm64 && npm run reset-version", "package-universal": "npm run set-version && vsce package && npm run reset-version", "package-pre-release-linux-x64": "npm run set-version && vsce package --pre-release --target linux-x64 && npm run reset-version", "package-pre-release-darwin-arm64": "npm run set-version && vsce package --pre-release --target darwin-arm64 && npm run reset-version", "package-pre-release-darwin-x64": "npm run set-version && vsce package --pre-release --target darwin-x64 && npm run reset-version", "package-pre-release-win32-x64": "npm run set-version && vsce package --pre-release --target win32-x64 && npm run reset-version", "package-pre-release-win32-arm64": "npm run set-version && vsce package --pre-release --target win32-arm64 && npm run reset-version", "package-pre-release-universal": "npm run set-version && vsce package --pre-release && npm run reset-version", "vsce-manifest": "vsce generate-manifest", "vscode:prepublish": "webpack --mode production", "test-compile": "tsc -p ./", "build:test": "tsc --project tsconfig.test.json --incremental", "test:unit": "npm run download-template && npm run build:test && mocha", "lint": "eslint \"src/**/*.ts\"", "watch": "webpack --mode development --watch", "prepare-bin": "npm run make-bin-scripts-dir && npm run copy-workspace-agent && npm run copy-inference-agent && npm run copy-workspace-agent-scripts", "make-bin-scripts-dir": "mkdirp ./bin/scripts", "copy-workspace-agent": "copyfiles --flat ../../artifacts/publish/WorkspaceAutomation.Agent/release*/** -e ../../artifacts/publish/WorkspaceAutomation.Agent/release*/*.pdb -e ../../artifacts/publish/WorkspaceAutomation.Agent/release*/*.lib -e ../../artifacts/publish/WorkspaceAutomation.Agent/release*/*.xml -e ../../artifacts/publish/WorkspaceAutomation.Agent/release*/payload*.json -e ../../artifacts/publish/WorkspaceAutomation.Agent/release*/Scripts/** ./bin", "copy-workspace-agent-scripts": "copyfiles --flat ../../artifacts/publish/WorkspaceAutomation.Agent/release*/Scripts/** ./bin/Scripts", "copy-inference-agent": "copyfiles --flat ../../artifacts/publish/Inference.Service.Agent/release*/** -e ../../artifacts/publish/Inference.Service.Agent/release*/*.pdb -e ../../artifacts/publish/Inference.Service.Agent/release*/*.lib -e ../../artifacts/publish/Inference.Service.Agent/release*/*.xml -e ../../artifacts/publish/Inference.Service.Agent/release*/onnx*.exe ./bin", "compile-development": "webpack --mode development", "bootstrap-developer-win-arm64": "npm run install:all && npm run build:webview && npm run build:model-lab && npm run build-workspace-agent-win-arm64 && npm run build-inference-agent-win-arm64 && npm run prepare-bin && npm run compile-development && npm run download-template", "bootstrap-developer-win-x64": "npm run install:all && npm run build:webview && npm run build:model-lab && npm run build-workspace-agent-win-x64 && npm run build-inference-agent-win-x64 && npm run prepare-bin && npm run compile-development && npm run download-template", "bootstrap-developer-linux-x64": "npm run install:all && npm run build:webview && npm run build:model-lab && npm run build-workspace-agent-linux-x64 && npm run build-inference-agent-linux-x64 && npm run prepare-bin && npm run compile-development && npm run download-template", "install:all": "npm install && cd webview-ui && npm install && cd ../model-lab-ui && npm install", "start:webview": "cd webview-ui && npm run start", "build:webview": "cd webview-ui && npm run build", "build:model-lab": "npm run prettier:model-lab && npm run sync:model-lab && cd model-lab-ui && npm run build", "setup:model-lab-uitest": "npm run build:model-lab && extest setup-tests -e ./model-lab-ui/dist-uitests-ext", "test:model-lab-ui": "extest run-tests ./model-lab-ui/dist-uitests/%npm_config_test%.test.js -m ./model-lab-ui/.mocharc-debug.js -e ./model-lab-ui/dist-uitests-ext", "sync:model-lab": "copyfiles -u 3 -V model-lab-ui/src/rpc/**/* src/model-lab/rpc && copyfiles -u 2 -V model-lab-ui/src/rpc/interface/model.ts model-lab-ui/uitests", "prettier:model-lab": "prettier --write model-lab-ui/src/**/*.ts model-lab-ui/src/**/*.tsx model-lab-ui/uitests/**/*.ts src/model-lab/**/*.ts tests/model-lab/**/*.ts", "pretest": "npm run compile-development && npm run lint && npm run download-template", "set-version": "nbgv-setversion", "reset-version": "nbgv-setversion --reset", "export-l10n": "vscode-l10n-dev export --outDir ./l10n ./src", "lockfile": "^1.0.4", "download-template": "ts-node ./src/utilities/templateUtils.ts -- https://api.github.com/repos/microsoft/windows-ai-studio-templates/releases/latest ./resources", "zip-template": "python ./resources/modelLab/utilities.py --task zipTemplate --input ../../../windows-ai-studio-templates --output ./resources/template.zip", "setup": "npm run install:all && npm run compile-development && npm run build:webview"}, "devDependencies": {"@types/adm-zip": "^0.5.5", "@types/chai": "^4.3.17", "@types/chai-as-promised": "^7.1.3", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/lockfile": "^1.0.4", "@types/mocha": "^10.0.7", "@types/mock-fs": "^4.13.4", "@types/mustache": "^4.2.5", "@types/node": "^16.18.113", "@types/semver": "^7.5.8", "@types/sinon": "^17.0.3", "@types/tmp": "^0.2.6", "@types/uuid": "^9.0.8", "@types/vscode": "^1.98.0", "@typescript-eslint/parser": "^6.7.0", "@vscode/l10n-dev": "^0.0.35", "@vscode/vsce": "^3.0.0", "chai": "^4.5.0", "chai-as-promised": "^7.1.2", "copy-webpack-plugin": "^12.0.2", "copyfiles": "^2.4.1", "dotenv": "^16.4.5", "mkdirp": "^3.0.1", "mocha": "^10.7.3", "mock-fs": "^5.4.1", "nerdbank-gitversioning": "3.6.133", "prettier": "^3.5.3", "sinon": "^18.0.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typemoq": "^2.1.0", "typescript": "^5.2.2", "vscode-extension-tester": "^8.14.1", "webpack": "^5.85.0", "webpack-cli": "^5.1.1"}, "dependencies": {"@anthropic-ai/sdk": "^0.51.0", "@azure-rest/ai-inference": "^1.0.0-beta.4", "@azure/ai-agents": "^1.0.0-beta.3", "@azure/ai-projects": "^1.0.0-beta.8", "@azure/arm-appcontainers": "^2.0.0", "@azure/arm-resources": "^5.2.0", "@azure/arm-storage": "^18.2.0", "@azure/arm-subscriptions": "^5.1.0", "@azure/core-rest-pipeline": "^1.15.2", "@azure/core-sse": "^2.1.3", "@azure/storage-file-share": "^12.24.0", "@google/generative-ai": "^0.22.0", "@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@langchain/community": "^0.3.5", "@langchain/core": "^0.3.10", "@microsoft/1ds-core-js": "^3.2.15", "@microsoft/1ds-post-js": "^3.2.15", "@microsoft/vscode-azext-azureauth": "^2.3.0", "@microsoft/vscode-azext-azureutils": "^3.0.1", "@modelcontextprotocol/sdk": "^1.17.0", "@types/lodash": "^4.17.14", "@vscode/extension-telemetry": "^0.9.6", "@vscode/python-extension": "^1.0.5", "@vscode/sqlite3": "^5.1.6-vscode", "adm-zip": "^0.5.15", "ajv": "^6.12.6", "async-mutex": "^0.5.0", "axios": "^1.7.4", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "epub2": "^3.0.2", "fs-extra": "^11.2.0", "glob": "^11.0.0", "html-to-text": "^9.0.5", "image-size": "^1.1.1", "lockfile": "^1.0.4", "lodash": "^4.17.21", "mammoth": "^1.8.0", "mime": "^4.0.4", "mustache": "^4.2.0", "officeparser": "^4.1.2", "ollama": "^0.5.12", "openai": "^4.104.0", "pdf-parse": "^1.1.1", "promise-socket": "^7.0.0", "semver": "^7.6.3", "systeminformation": "^5.25.11", "tiktoken": "^1.0.17", "tmp": "^0.2.3", "uuid": "^9.0.1", "vscode-jsonrpc": "^8.2.0", "yaml": "^2.5.0", "zod": "^3.23.8"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/ms-windows-ai-studio.windows-ai-studio-0.18.2-linux-x64", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "linux-x64", "publisherDisplayName": "Microsoft", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1754553141532, "pinned": false, "source": "gallery", "id": "cf78d6ef-45ff-428f-875b-7fb6251b66af", "publisherId": "23e1367d-eaec-40e3-95ef-20aa76a5186c", "publisherDisplayName": "Microsoft", "targetPlatform": "linux-x64", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": true, "size": 115986593}, "isValid": true, "validations": [], "preRelease": true}, {"type": 1, "identifier": {"id": "github.copilot", "uuid": "23c4aeee-f844-43cd-b53e-1113e483f1a6"}, "manifest": {"name": "copilot", "displayName": "GitHub Copilot", "description": "Your AI pair programmer", "version": "1.353.1721", "build": "1721", "buildType": "nightly", "publisher": "GitHub", "preview": false, "homepage": "https://github.com/features/copilot?editor=vscode", "license": "https://docs.github.com/en/site-policy/github-terms/github-terms-for-additional-products-and-features", "bugs": {"url": "https://github.com/microsoft/vscode/issues"}, "qna": "https://github.com/github-community/community/discussions/categories/copilot", "icon": "assets/Copilot-App-Icon.png", "pricing": "Trial", "extensionPack": ["GitHub.copilot-chat", "GitHub.copilot"], "engines": {"vscode": "^1.98.0", "node": ">=20.0.0", "npm": ">=9.0.0"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Machine Learning"], "keywords": ["ai", "openai", "codex", "pilot", "snippets", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "c#", "java", "kotlin", "co-pilot"], "badges": [{"url": "https://img.shields.io/badge/GitHub%20Copilot-Subscription%20Required-orange", "href": "https://github.com/github-copilot/signup?editor=vscode", "description": "Sign up for GitHub Copilot"}, {"url": "https://img.shields.io/github/stars/github/copilot-docs?style=social", "href": "https://github.com/github/copilot-docs", "description": "Star Copilot on GitHub"}, {"url": "https://img.shields.io/youtube/channel/views/UC7c3Kb6jYCRj4JOHHZTxKsQ?style=social", "href": "https://www.youtube.com/@GitHub/search?query=copilot", "description": "Check out GitHub on Youtube"}, {"url": "https://img.shields.io/twitter/follow/github?style=social", "href": "https://twitter.com/github", "description": "Follow GitHub on Twitter"}], "activationEvents": ["onStartupFinished"], "browser": "./dist/web", "main": "./dist/extension", "enabledApiProposals": ["inlineCompletionsAdditions"], "contributes": {"commands": [{"command": "github.copilot.toggleStatusMenu", "title": "Open Status Menu", "category": "GitHub Copilot"}, {"command": "github.copilot.signIn", "title": "Sign In", "category": "GitHub Copilot", "enablement": "!github.copilot.activated"}, {"command": "github.copilot.acceptCursorPanelSolution", "title": "Accept Panel Suggestion at the Cursor", "enablement": "github.copilot.panelVisible", "category": "GitHub Copilot"}, {"command": "github.copilot.previousPanelSolution", "title": "Navigate to the Previous Panel Suggestion", "enablement": "github.copilot.panelVisible", "category": "GitHub Copilot"}, {"command": "github.copilot.nextPanelSolution", "title": "Navigate to the Next Panel Suggestion", "enablement": "github.copilot.panelVisible", "category": "GitHub Copilot"}, {"command": "github.copilot.generate", "title": "Open Completions Panel", "enablement": "github.copilot.activated && !isWeb", "category": "GitHub Copilot"}, {"command": "github.copilot.generateComparison", "title": "Open Comparison Panel", "enablement": "github.copilot.activated && !isWeb && github.copilot.comparisonPanelEnabled", "category": "GitHub Copilot"}, {"command": "github.copilot.acceptCursorComparisonPanelSolution", "title": "Accept Comparison Panel Suggestion at the Cursor", "enablement": "github.copilot.comparisonPanelVisible && github.copilot.comparisonPanelEnabled", "category": "GitHub Copilot"}, {"command": "github.copilot.previousComparisonPanelSolution", "title": "Navigate to the Previous Comparison Panel Suggestion", "enablement": "github.copilot.comparisonPanelVisible && github.copilot.comparisonPanelEnabled", "category": "GitHub Copilot"}, {"command": "github.copilot.nextComparisonPanelSolution", "title": "Navigate to the Next Comparison Panel Suggestion", "enablement": "github.copilot.comparisonPanelVisible && github.copilot.comparisonPanelEnabled", "category": "GitHub Copilot"}, {"command": "github.copilot.completions.disable", "title": "Disable Completions", "enablement": "github.copilot.activated && config.editor.inlineSuggest.enabled && github.copilot.completions.enabled", "category": "GitHub Copilot"}, {"command": "github.copilot.completions.enable", "title": "Enable Completions", "enablement": "github.copilot.activated && !(config.editor.inlineSuggest.enabled && github.copilot.completions.enabled)", "category": "GitHub Copilot"}, {"command": "github.copilot.completions.toggle", "title": "Toggle (Enable/Disable) Completions", "enablement": "github.copilot.activated", "category": "GitHub Copilot"}, {"command": "github.copilot.sendFeedback", "title": "Send Feedback", "category": "GitHub Copilot"}, {"command": "github.copilot.collectDiagnostics", "title": "Collect Diagnostics", "category": "GitHub Copilot", "enablement": "!isWeb"}, {"command": "github.copilot.openLogs", "title": "Open Logs", "category": "GitHub Copilot"}, {"command": "github.copilot.openModelPicker", "title": "Change Completions Model", "category": "GitHub Copilot", "enablement": "!isWeb"}, {"command": "github.copilot.sendCompletionFeedback", "title": "Send Copilot Completion Feedback", "category": "GitHub Copilot", "enablement": "!isWeb"}, {"command": "github-copilot-completions-debugger-view.refresh", "title": "Refresh", "icon": "$(refresh)"}], "keybindings": [{"command": "github.copilot.acceptCursorPanelSolution", "key": "ctrl+/", "mac": "ctrl+/", "when": "activeWebviewPanelId == 'GitHub Copilot Suggestions'"}, {"command": "github.copilot.previousPanelSolution", "key": "alt+[", "mac": "alt+[", "when": "activeWebviewPanelId == 'GitHub Copilot Suggestions'"}, {"command": "github.copilot.nextPanelSolution", "key": "alt+]", "mac": "alt+]", "when": "activeWebviewPanelId == 'GitHub Copilot Suggestions'"}, {"command": "github.copilot.acceptCursorComparisonPanelSolution", "key": "ctrl+shift+/", "mac": "ctrl+shift+/", "when": "activeWebviewPanelId == 'GitHub Copilot Comparison' && github.copilot.comparisonPanelEnabled"}, {"command": "github.copilot.previousComparisonPanelSolution", "key": "alt+shift+[", "mac": "alt+shift+[", "when": "activeWebviewPanelId == 'GitHub Copilot Comparison' && github.copilot.comparisonPanelEnabled"}, {"command": "github.copilot.nextComparisonPanelSolution", "key": "alt+shift+]", "mac": "alt+shift+]", "when": "activeWebviewPanelId == 'GitHub Copilot Comparison' && github.copilot.comparisonPanelEnabled"}, {"command": "editor.action.inlineSuggest.trigger", "key": "alt+\\", "when": "editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"}], "configuration": [{"title": "GitHub Copilot", "properties": {"github.copilot.selectedCompletionModel": {"type": "string", "default": "", "markdownDescription": "The currently selected completion model ID. To select from a list of available models, use the __\"Change Completions Model\"__ command or open the model picker (from the Copilot menu in the VS Code title bar, select __\"Configure Code Completions\"__ then __\"Change Completions Model\"__. The value must be a valid model ID. An empty value indicates that the default model will be used."}, "github.copilot.advanced": {"type": "object", "title": "Advanced Settings", "properties": {"authProvider": {"type": "string", "enum": ["github", "github-enterprise"], "enumDescriptions": ["GitHub.com", "GitHub Enterprise"], "default": "github", "description": "The GitHub identity to use for Copilot"}, "authPermissions": {"type": "string", "enum": ["default", "minimal"], "markdownEnumDescriptions": ["Default (recommended) - The default permissions enable the best that Copilot has to offer including, but not limited to, faster repo indexing and the power of the `@github` agent.", "Minimal - The minimal permissions required for Copilot functionality."], "default": "default", "markdownDescription": "Controls what kind of permissions are asked for when signing in to Copilot. The options are\n* `default` - (strongly recommended) The default permissions enable the best that Copilot has to offer including, but not limited to, faster repo indexing and the power of the `@github` agent.\n* `minimal` - The minimal permissions are the least that Copilot needs to function. Some features may behave slower or not at all."}, "useLanguageServer": {"type": "boolean", "default": false, "description": "Experimental: Use language server"}, "debug.overrideEngine": {"type": "string", "default": "", "description": "Override engine name"}, "debug.overrideProxyUrl": {"type": "string", "default": "", "description": "Override GitHub authentication proxy full URL"}, "debug.testOverrideProxyUrl": {"type": "string", "default": "", "description": "Override GitHub authentication proxy URL when running tests"}, "debug.overrideCapiUrl": {"type": "string", "default": "", "description": "Override GitHub Copilot API full URL"}, "debug.testOverrideCapiUrl": {"type": "string", "default": "", "description": "Override GitHub Copilot API URL when running tests"}, "debug.filterLogCategories": {"type": "array", "default": [], "deprecationMessage": "Set overrideLogLevels.* to ERROR to filter out unwanted categories.", "description": "Show only log categories listed in this setting. If an array is empty, show all loggers"}}}, "github.copilot.enable": {"type": "object", "scope": "window", "default": {"*": true, "plaintext": false, "markdown": false, "scminput": false}, "additionalProperties": {"type": "boolean"}, "markdownDescription": "Enable or disable auto triggering of Copilot completions for specified [languages](https://code.visualstudio.com/docs/languages/identifiers). You can still trigger suggestions manually using `Alt + \\`"}}}], "configurationDefaults": {"editor.tokenColorCustomizations": {"[*Light*]": {"textMateRules": [{"scope": "ref.matchtext", "settings": {"foreground": "#000"}}]}, "[*Dark*]": {"textMateRules": [{"scope": "ref.matchtext", "settings": {"foreground": "#fff"}}]}}}, "languages": [{"id": "code-referencing"}], "grammars": [{"language": "code-referencing", "scopeName": "text.ref", "path": "./syntaxes/ref.tmGrammar.json"}], "iconFonts": [{"id": "copilot-font", "src": [{"path": "assets/copilot.woff", "format": "woff"}]}], "viewsContainers": {"activitybar": [{"id": "github-copilot-completions-debugger-panel", "title": "Debugger", "icon": "$(bug)", "when": "config.github.copilot.internal.showWorkspaceContextDebugger && config.github.copilot.internal.useWorkspaceContextCoordinator"}]}, "views": {"github-copilot-completions-debugger-panel": [{"id": "github-copilot-completions-debugger-view", "name": "Debugger", "when": "config.github.copilot.internal.showWorkspaceContextDebugger && config.github.copilot.internal.useWorkspaceContextCoordinator", "icon": "$(bug)"}]}, "menus": {"view/title": [{"command": "github-copilot-completions-debugger-view.refresh", "when": "view == github-copilot-completions-debugger-view", "group": "navigation"}]}}, "scripts": {"build": "tsx esbuild.ts", "clean": "./script/build/clean.sh", "compress": "tsx ./script/compressTokenizer.ts", "forbid-sources-content:extension": "node script/forbid-sources-content.js --extension", "generate_languages": "tsx script/generateLanguages.ts && prettier --write lib/src/language/generatedLanguages.ts", "get_token": "tsx script/getToken.ts", "lint": "run-p --aggregate-output \"lint:*\"", "lint:deps": "depcruise -c .dependency-cruiser.js .", "lint:eslint": "eslint -f visualstudio --quiet --cache .", "lint:prettier": "prettier --check . 2>&1", "lint:types": "tsc --noEmit && tsc --noEmit -p extension/src/copilotPanel/webview", "prebuild": "npm install", "pretest": "npm run build", "pretest:headless": "npm run build", "pretest:extension": "npm run build", "pretest:lsp-client": "npm run build", "pretest:lib-e2e": "npm run build", "prewatch": "npm run build", "prewatch:esbuild": "npm run build", "start": "npm run watch", "test": "npm-run-all \"test:extension --ignore-scripts\" \"test:headless --ignore-scripts\" lint", "test:headless": "npm-run-all test:lib test:agent \"test:lib-e2e --ignore-scripts\" test:prompt \"test:lsp-client --ignore-scripts\" lint", "test:agent": "mocha \"agent/src/**/*.test.{ts,tsx}\"", "test:extension": "tsx extension/test/runTest.ts", "test:lib": "mocha \"lib/src/**/*.test.{ts,tsx}\"", "test:lib-e2e": "mocha \"lib/e2e/src/**/*.test.{ts,tsx}\" --exclude \"lib/e2e/src/prompt/**/*.test.ts\"", "test:lib-e2e-no-ci": "mocha \"lib/e2e/no-ci/**/*.test.{ts,tsx}\"", "test:lib-prompt-e2e": "mocha \"lib/e2e/src/prompt/prompt.test.ts\"", "test:lib-prompt-e2e-perf": "INCLUDE_PERFORMANCE=true npm run test:lib-prompt-e2e", "test:lsp-client": "mocha \"lsp-client/test/*.test.{ts,tsx}\"", "test:prompt": "mocha \"prompt/test/**/*.test.{ts,tsx}\"", "test:prepare-msbench": "tsx script/setupMsbench.ts", "test:run-msbench": "tsx lsp-client/test/msbench/agent/index.ts", "vscode-dts": "vscode-dts dev && mv vscode.proposed.*.ts extension/src", "vscode:prepublish": "run-s \"build --ignore-scripts\" forbid-sources-content:extension", "vscode:uninstall": "node dist/extensionUninstalled.js", "vsix": "vsce package --allow-missing-repository", "watch": "run-p \"watch:esbuild --ignore-scripts\" \"watch:types -- --preserveWatchOutput\"", "watch:esbuild": "tsx esbuild.ts --watch", "watch:types": "tsc --noEmit --watch"}, "devDependencies": {"@azure/identity": "^4.11.0", "@azure/keyvault-secrets": "^4.10.0", "@datadog/datadog-ci": "^3.17.0", "@github/prettier-config": "0.0.6", "@limegrass/eslint-plugin-import-alias": "^1.5.1", "@parcel/watcher": "^2.5.1", "@types/benchmark": "^2.1.5", "@types/crypto-js": "^4.2.2", "@types/diff": "^8.0.0", "@types/git-url-parse": "^16.0.0", "@types/js-yaml": "^4.0.6", "@types/kerberos": "^1.1.2", "@types/mocha": "^10.0.10", "@types/node": "~20.8.0", "@types/semver": "^7.7.0", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "@types/vscode": "1.98.0", "@types/yargs": "^17.0.24", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.3.2", "@yao-pkg/pkg": "^6.6.0", "benchmark": "^2.1.4", "boxen": "^8.0.1", "chalk": "^5.4.1", "dependency-cruiser": "^17.0.0", "electron": "^28.1.4", "esbuild": "^0.25.8", "esbuild-plugin-copy": "^2.1.1", "eslint": "^9.32.0", "eslint-formatter-visualstudio": "^8.40.0", "eslint-plugin-mocha": "^10.5.0", "glob": "^11.0.3", "globals": "^16.3.0", "js-yaml": "^4.1.0", "mocha": "^11.2.2", "mocha-junit-reporter": "^2.2.1", "mocha-multi-reporters": "^1.5.1", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "proxy": "^2.1.1", "simple-git": "^3.28.0", "sinon": "^20.0.0", "ts-dedent": "^2.2.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vscode-dts": "^0.3.3"}, "dependencies": {"@adobe/helix-fetch": "github:devm33/helix-fetch#2a08fa939591a0e14f34f611adcc3ed767579e9a", "@github/memoize": "1.1.5", "@microsoft/1ds-post-js": "^4.3.9", "@microsoft/applicationinsights-web-basic": "^3.3.6", "@microsoft/tiktokenizer": "^1.0.10", "@modelcontextprotocol/sdk": "^1.17.0", "@sinclair/typebox": "^0.34.33", "@types/vscode-webview": "^1.57.4", "@vscode/codicons": "^0.0.36", "@vscode/prompt-tsx": "0.4.0-alpha.1", "@vscode/webview-ui-toolkit": "^1.3.1", "await-lock": "^2.2.2", "crypto-js": "^4.2.0", "diff": "^8.0.2", "dldr": "^0.0.10", "events": "^3.3.0", "get-stream": "^6.0.1", "git-url-parse": "^16.0.3", "kerberos": "^2.2.0", "mac-ca": "^3.1.1", "microjob": "^0.7.0", "minimatch": "^9.0.3", "open": "^10.1.2", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "semver": "^7.7.1", "shiki": "~1.15.0", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "vscode-languageclient": "^9.0.0", "vscode-languageserver": "^9.0.0", "vscode-languageserver-protocol": "^3.17", "vscode-languageserver-textdocument": "~1.0.11", "vscode-uri": "^3.1.0", "web-tree-sitter": "^0.20.8", "windows-ca-certs": "^0.1.0", "yargs": "^17.7.2"}, "overrides": {"fsevents": "<0", "bindings": "npm:bundled-bindings@^1.5.0"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-1.353.1721", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "GitHub", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1754553145991, "pinned": false, "source": "gallery", "id": "23c4aeee-f844-43cd-b53e-1113e483f1a6", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": true, "hasPreReleaseVersion": true, "preRelease": true, "size": 66651513}, "isValid": true, "validations": [], "preRelease": true}, {"type": 1, "identifier": {"id": "augment.vscode-augment", "uuid": "fc0e137d-e132-47ed-9455-c4636fa5b897"}, "manifest": {"name": "vscode-augment", "displayName": "Augment", "publisher": "Augment", "repository": {}, "private": true, "preview": false, "license": "https://www.augmentcode.com/terms-of-service", "description": "Augment yourself with the best AI pair programmer", "version": "0.524.1", "engines": {"vscode": "^1.82.0", "node": ">= 18.15.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Snippets"], "activationEvents": ["onStartupFinished"], "icon": "icon.png", "galleryBanner": {"color": "#000000", "theme": "dark"}, "main": "./out/extension.js", "contributes": {"customEditors": [{"viewType": "rules.augment", "displayName": "Augment Rules Viewer", "selector": [{"filenamePattern": "**/.augment/rules/**/*.md"}], "priority": "default"}, {"viewType": "memories.augment", "displayName": "Augment Memories Viewer", "selector": [{"filenamePattern": "**/workspaceStorage/*/Augment.vscode-augment/Augment-Memories"}], "priority": "default"}], "configuration": [{"title": "Augment", "properties": {"augment.disableFocusOnAugmentPanel": {"type": "boolean", "order": 0, "default": false, "description": "Disable opening the Augment panel."}, "augment.completions.enableAutomaticCompletions": {"type": "boolean", "order": 0, "default": true, "description": "Provide automatic inline code completions (manual code completions are always available)."}, "augment.completions.enableQuickSuggestions": {"type": "boolean", "order": 1, "default": true, "description": "Add Augment to the IntelliSense pop-up suggestions."}, "augment.completions.disableCompletionsByLanguage": {"type": "array", "order": 2, "default": ["git-commit", "scminput"], "markdownDescription": "Disable completions by [language identifiers](https://code.visualstudio.com/docs/languages/identifiers).", "items": {"type": "string"}, "uniqueItems": true}, "augment.enableEmptyFileHint": {"type": "boolean", "order": 3, "default": true, "description": "Display a hint to use Augment Chat when an empty file is open."}, "augment.conflictingCodingAssistantCheck": {"type": "boolean", "order": 4, "default": true, "description": "Check for conflicting coding assistants when starting up and installing extensions."}, "augment.advanced": {"type": "object", "order": 99999, "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Augment access."}, "completionURL": {"type": "string", "default": "", "description": "URL of completion server."}, "completions": {"type": "object", "default": {}, "properties": {"timeoutMs": {"default": 800, "type": ["number", "null"], "description": "The default timeout for completions (in milliseconds)."}, "maxWaitMs": {"default": 1600, "type": ["number", "null"], "description": "The max timeout for completions items (in milliseconds). This allows Augment to retry completions that are cancelled due to changes in the editor."}, "addIntelliSenseSuggestions": {"default": true, "type": "boolean", "description": "Enable completions in the intellisense pop-up."}}}, "mcpServers": {"type": "array", "default": [{}], "items": {"type": "object", "properties": {"command": {"type": "string", "description": "The command to run the MCP server"}, "args": {"type": "array", "items": {"type": "string"}, "description": "Arguments to pass to the MCP server command"}, "timeoutMs": {"type": "number", "description": "Timeout in milliseconds for MCP server operations"}, "env": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Dictionary of Environment variables to set for the MCP server"}}}, "description": "List of MCP server configurations"}, "integrations": {"type": "object", "default": {}, "properties": {"atlassian": {"type": "object", "default": {}, "properties": {"serverUrl": {"type": "string", "default": "", "description": "Atlassian server URL"}, "personalApiToken": {"type": "string", "default": "", "description": "Personal API token for Atlassian"}, "username": {"type": "string", "default": "", "description": "Atlassian username"}}}, "notion": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Notion"}}}, "linear": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for Linear"}}}, "github": {"type": "object", "default": {}, "properties": {"apiToken": {"type": "string", "default": "", "description": "API token for GitHub"}}}}, "description": "Integration configurations for third-party services"}}}}}, {"title": "Next Edit", "properties": {"augment.nextEdit.enableBackgroundSuggestions": {"type": "boolean", "order": 0, "default": true, "description": "Enable Next Edit to run in the background and suggest changes in the editor."}, "augment.nextEdit.enableGlobalBackgroundSuggestions": {"type": "boolean", "order": 1, "default": false, "description": "Enable Next Edit to hint changes in files beyond the active editor tab."}, "augment.nextEdit.enableAutoApply": {"type": "boolean", "order": 2, "default": true, "description": "Automatically apply suggestions when you jump to them."}, "augment.nextEdit.showDiffInHover": {"type": "boolean", "order": 3, "default": false, "description": "Show a diff of the suggested change in the hover."}, "augment.nextEdit.highlightSuggestionsInTheEditor": {"type": "boolean", "order": 4, "default": false, "description": "Highlight all lines with a suggestion in addition to showing gutter icons and gray hint-text."}}}, {"title": "Experimental", "properties": {"augment.chat.userGuidelines": {"type": "string", "order": 5, "default": "", "description": "Edit this field on the Augment settings page."}}}], "commands": [{"category": "Augment", "command": "vscode-augment.internal-dv.o", "title": "View Diff"}, {"category": "Augment", "command": "vscode-augment.internal-dv.i", "title": "Code Instruction"}, {"category": "Augment", "command": "vscode-augment.internal-dv.aac", "title": "Accept All Chunks"}, {"category": "Augment", "command": "vscode-augment.internal-dv.afc", "title": "Accept Focused Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.rfc", "title": "Reject Focused Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.fpc", "title": "Focus Previous Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.fnc", "title": "Focus Next Chunk"}, {"category": "Augment", "command": "vscode-augment.internal-dv.c", "title": "Close Diff View"}, {"category": "Augment", "command": "vscode-augment.insertCompletion", "title": "Insert Completion"}, {"category": "Augment", "command": "vscode-augment.settings", "title": "$(gear) Edit Settings..."}, {"category": "Augment", "command": "vscode-augment.keyboard-shortcuts", "title": "$(keyboard) Edit Keyboard Shortcuts..."}, {"category": "Augment", "command": "vscode-augment.showDocs", "title": "Help", "icon": "$(question)"}, {"category": "Augment", "command": "vscode-augment.showAccountPage", "title": "Account & Billing", "icon": "$(note)"}, {"category": "Augment", "command": "vscode-augment.toggleAutomaticCompletionSetting", "title": "Toggle Automatic Completions"}, {"category": "Augment", "command": "vscode-augment.signIn", "title": "$(sign-in) Sign In"}, {"category": "Augment", "command": "vscode-augment.signOut", "title": "$(sign-out) Sign Out"}, {"category": "Augment", "command": "vscode-augment.chat.slash.fix", "title": "Fix using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.explain", "title": "Explain using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.test", "title": "Write test using Augment"}, {"category": "Augment", "command": "vscode-augment.chat.slash.document", "title": "Document using Augment"}, {"category": "Augment", "command": "vscode-augment.showHistoryPanel", "title": "$(history) Show History"}, {"category": "Augment", "command": "vscode-augment.copySessionId", "title": "Copy Session ID"}, {"category": "Augment", "command": "_vscode-augment.showSidebarWorkspaceContext", "title": "Manage Workspace Context", "icon": "$(folder-opened)"}, {"category": "Augment", "command": "_vscode-augment.showSidebarChat", "title": "Show Chat", "icon": "$(comment-discussion)"}, {"category": "Augment", "command": "vscode-augment.generateCommitMessage", "title": "Generate Commit Message with Augment", "icon": "$(sparkle)", "when": "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0"}, {"category": "Augment", "command": "vscode-augment.showSettingsPanel", "title": "Settings", "icon": "$(settings-gear)"}, {"category": "Augment", "command": "vscode-augment.showAugmentCommands", "title": "Show Augment Commands", "icon": "$(menu)"}, {"category": "Augment", "command": "vscode-augment.focusAugmentPanel", "title": "$(layout-sidebar-left) Open Augment"}, {"category": "Augment", "command": "vscode-augment.startNewChat", "title": "Start New Chat"}, {"category": "Augment", "command": "vscode-augment.clear-recent-editing-history", "title": "Clear Recent Editing History"}, {"category": "Augment", "command": "vscode-augment.showRemoteAgentsPanel", "title": "Show Remote Agents Panel", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"category": "Augment", "command": "vscode-augment.openSshConfig", "title": "Open Augment SSH Config", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"category": "Augment", "command": "vscode-augment.zipAugmentUserAssets", "title": "Zip Augment User Assets", "icon": "$(archive)"}, {"category": "Augment", "command": "vscode-augment.createSecret", "title": "Create Secret"}, {"category": "Augment", "command": "vscode-augment.listSecrets", "title": "List Secrets"}, {"category": "Augment", "command": "vscode-augment.deleteSecret", "title": "Delete Secret"}, {"category": "Augment", "command": "vscode-augment.next-edit.force", "title": "View Nearby Next Edit Suggestions (Forced)", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused)"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-panel-horizontal-split", "title": "Toggle Side Panel Split", "icon": "$(split-horizontal)", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"category": "Augment", "command": "vscode-augment.next-edit.update", "title": "Update Next Edit Suggestions", "icon": {"light": "media/next-edit/nextedit-update-light.svg", "dark": "media/next-edit/nextedit-update-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.loading", "title": "Updating Suggestions...", "icon": {"light": "media/next-edit/nextedit-update-loading-light.svg", "dark": "media/next-edit/nextedit-update-loading-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "title": "No Updates Available", "icon": {"light": "media/next-edit/nextedit-update-disabled-light.svg", "dark": "media/next-edit/nextedit-update-disabled-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "title": "Suggestions Up To Date", "icon": {"light": "media/next-edit/nextedit-update-complete-light.svg", "dark": "media/next-edit/nextedit-update-complete-dark.svg"}, "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.open-panel", "title": "Open Next Edit Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Accept Suggestion"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.accept-code-action", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptCodeAction", "title": "Accept Suggestion"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll", "title": "Accept All Suggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Reject Suggestion"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll", "title": "Reject All Suggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "title": "Dismiss Suggestion Highlights"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart", "title": "Go to Next Suggestion (Smart)"}, {"category": "Augment", "command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext", "title": "Go to Next Suggestion", "icon": {"light": "media/next-edit/right-light-enabled.svg", "dark": "media/next-edit/right-dark-enabled.svg"}}, {"category": "Augment", "command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious", "title": "Go to Previous Suggestion", "icon": {"light": "media/next-edit/left-light-enabled.svg", "dark": "media/next-edit/left-dark-enabled.svg"}}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.next-forward.disabled", "title": "Go to Next Suggestion", "icon": {"light": "media/next-edit/right-light-disabled.svg", "dark": "media/next-edit/right-dark-disabled.svg"}, "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.previous.disabled", "title": "Go to Previous Suggestion", "icon": {"light": "media/next-edit/left-light-disabled.svg", "dark": "media/next-edit/left-dark-disabled.svg"}, "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "_vscode-augment.next-edit.background.open", "title": "Augment Next Edit: View Suggestion", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-bg", "title": "Toggle Background Suggestions", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions"}, {"category": "Augment", "command": "vscode-augment.next-edit.toggle-all-highlights", "title": "Toggle Suggestion Highlights", "when": "vscode-augment.enableNextEdit"}, {"category": "Augment", "command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "when": "vscode-augment.enableNextEdit"}], "icons": {"augment-icon-simple": {"description": "Augment logo (simple)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E900"}}, "augment-icon-smile": {"description": "Augment logo (smile)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E901"}}, "augment-icon-zero": {"description": "Augment logo (zero)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E902"}}, "augment-icon-error": {"description": "Augment logo (error)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E903"}}, "augment-icon-closed-eyes": {"description": "Augment logo (closed eyes)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E904"}}, "augment-icon-dots": {"description": "Augment logo (dots)", "default": {"fontPath": "augment-icon-font.woff", "fontCharacter": "\\E905"}}, "augment-kb-z": {"description": "Keyboard icon Z", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f101"}}, "augment-kb-y": {"description": "Keyboard icon Y", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f102"}}, "augment-kb-x": {"description": "Keyboard icon X", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f103"}}, "augment-kb-win": {"description": "Keyboard icon Win", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f104"}}, "augment-kb-w": {"description": "Keyboard icon W", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f105"}}, "augment-kb-v": {"description": "Keyboard icon V", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f106"}}, "augment-kb-u": {"description": "Keyboard icon U", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f107"}}, "augment-kb-tab": {"description": "Keyboard icon Tab", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f108"}}, "augment-kb-t": {"description": "Keyboard icon T", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f109"}}, "augment-kb-shift": {"description": "Keyboard icon Shift", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10a"}}, "augment-kb-semicolon": {"description": "Keyboard icon Semicolon", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10b"}}, "augment-kb-s": {"description": "Keyboard icon S", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10c"}}, "augment-kb-return": {"description": "Keyboard icon Return", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10d"}}, "augment-kb-r": {"description": "Keyboard icon R", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10e"}}, "augment-kb-q": {"description": "Keyboard icon Q", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f10f"}}, "augment-kb-p": {"description": "Keyboard icon P", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f110"}}, "augment-kb-option": {"description": "Keyboard icon Option", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f111"}}, "augment-kb-o": {"description": "Keyboard icon O", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f112"}}, "augment-kb-n": {"description": "Keyboard icon N", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f113"}}, "augment-kb-meta": {"description": "Keyboard icon <PERSON><PERSON>", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f114"}}, "augment-kb-m": {"description": "Keyboard icon M", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f115"}}, "augment-kb-l": {"description": "Keyboard icon L", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f116"}}, "augment-kb-k": {"description": "Keyboard icon K", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f117"}}, "augment-kb-j": {"description": "Keyboard icon J", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f118"}}, "augment-kb-i": {"description": "Keyboard icon I", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f119"}}, "augment-kb-h": {"description": "Keyboard icon H", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11a"}}, "augment-kb-g": {"description": "Keyboard icon G", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11b"}}, "augment-kb-f": {"description": "Keyboard icon F", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11c"}}, "augment-kb-escape": {"description": "Keyboard icon Escape", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11d"}}, "augment-kb-e": {"description": "Keyboard icon E", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11e"}}, "augment-kb-delete": {"description": "Keyboard icon Delete", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f11f"}}, "augment-kb-d": {"description": "Keyboard icon D", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f120"}}, "augment-kb-ctrl": {"description": "Keyboard icon Ctrl", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f121"}}, "augment-kb-control": {"description": "Keyboard icon Control", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f122"}}, "augment-kb-command": {"description": "Keyboard icon Command", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f123"}}, "augment-kb-c": {"description": "Keyboard icon C", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f124"}}, "augment-kb-backspace": {"description": "Keyboard icon Backspace", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f125"}}, "augment-kb-b": {"description": "Keyboard icon B", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f126"}}, "augment-kb-alt": {"description": "Keyboard icon Alt", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f127"}}, "augment-kb-a": {"description": "Keyboard icon A", "default": {"fontPath": "augment-kb-icon-font.woff", "fontCharacter": "\\f128"}}}, "keybindings": [{"command": "vscode-augment.insertCompletion", "when": "editorHasCompletionItemProvider && editorTextFocus && !editorR<PERSON>only", "key": "ctrl-f11", "mac": "ctrl-f11"}, {"command": "vscode-augment.focusAugmentPanel", "key": "ctrl-alt-i", "mac": "cmd-ctrl-i"}, {"command": "vscode-augment.focusAugmentPanel", "key": "ctrl-l", "mac": "cmd-l"}, {"command": "vscode-augment.internal-dv.o", "when": "vscode-augment.enableDebugFeatures", "key": "ctrl-alt-o", "mac": "cmd-ctrl-o"}, {"command": "vscode-augment.internal-dv.i", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus", "key": "ctrl-i", "mac": "cmd-i"}, {"command": "vscode-augment.internal-dv.aac", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "ctrl-enter", "mac": "cmd-enter"}, {"command": "vscode-augment.internal-dv.afc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "enter", "mac": "enter"}, {"command": "vscode-augment.internal-dv.rfc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "backspace"}, {"command": "vscode-augment.internal-dv.fpc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "up"}, {"command": "vscode-augment.internal-dv.fnc", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "down"}, {"command": "vscode-augment.internal-dv.c", "when": "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused", "key": "escape"}, {"command": "vscode-augment.showAugmentCommands", "key": "ctrl-shift-a", "mac": "cmd-shift-a"}, {"command": "vscode-augment.toggleAutomaticCompletionSetting", "key": "ctrl-alt-a", "mac": "cmd-alt-a"}, {"command": "vscode-augment.showRemoteAgentsPanel", "when": "vscode-augment.featureFlags.enableRemoteAgents", "key": "ctrl-shift-r", "mac": "cmd-shift-r", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-y", "mac": "cmd-shift-z", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-enter", "mac": "cmd-enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "enter", "mac": "enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "tab", "mac": "tab", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll && augment-next-edit.active", "key": "ctrl-alt-enter", "mac": "cmd-alt-enter", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-backspace", "mac": "cmd-backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "backspace", "mac": "backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll && augment-next-edit.active", "key": "ctrl-alt-backspace", "mac": "cmd-alt-backspace", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel') && !inlineSuggestionVisible && !editorHasSelection && (!vim.active || vim.mode == 'Normal')", "key": "escape", "mac": "escape", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart", "key": "ctrl-;", "mac": "cmd-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext", "key": "ctrl-shift-'", "mac": "cmd-shift-'", "args": "keybinding"}, {"command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious", "key": "ctrl-shift-;", "mac": "cmd-shift-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.force", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-alt-;", "mac": "cmd-ctrl-;", "args": "keybinding"}, {"command": "vscode-augment.next-edit.open-panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "key": "ctrl-'", "mac": "cmd-'", "args": "keybinding"}, {"command": "_vscode-augment.next-edit.undo-accept-suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canUndoAcceptSuggestion && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')", "key": "ctrl-z", "mac": "cmd-z", "args": "keybinding"}], "submenus": [{"id": "vscode-augment.context-submenu", "label": "Send to Augment"}, {"id": "vscode-augment.viewTitleMenuEntryPoint", "label": "Augment Options", "icon": "$(gear)"}, {"id": "vscode-augment.next-edit.editor-action-submenu", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-available-light.svg", "dark": "media/next-edit/nextedit-available-dark.svg"}}, {"id": "vscode-augment.next-edit.editor-action-submenu.disabled", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-unavailable-light.svg", "dark": "media/next-edit/nextedit-unavailable-dark.svg"}}, {"id": "vscode-augment.next-edit.editor-action-submenu.loading", "label": "Next Edit", "icon": {"light": "media/next-edit/nextedit-loading-light.svg", "dark": "media/next-edit/nextedit-loading-dark.svg"}}, {"id": "vscode-augment.next-edit.panel-submenu", "label": "Next Edit Menu", "icon": "$(ellipsis)"}], "menus": {"view/title": [{"submenu": "vscode-augment.viewTitleMenuEntryPoint", "when": "view == augment-chat && vscode-augment.mainPanel.app == 'chat'", "group": "navigation@0"}, {"command": "vscode-augment.next-edit.update", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && !vscode-augment.nextEdit.global.updateCached", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.loading", "when": "view == augment-next-edit && vscode-augment.nextEdit.global.updating", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && !vscode-augment.nextEdit.global.canUpdate", "group": "navigation@1"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "when": "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && vscode-augment.nextEdit.global.updateCached", "group": "navigation@1"}, {"submenu": "vscode-augment.next-edit.panel-submenu", "when": "view == augment-next-edit", "group": "navigation@2"}], "vscode-augment.viewTitleMenuEntryPoint": [{"command": "vscode-augment.showSettingsPanel", "group": "menu@1"}, {"command": "vscode-augment.showDocs", "group": "menu@2"}, {"command": "vscode-augment.showAccountPage", "group": "menu@3"}, {"command": "vscode-augment.signOut", "group": "menu@4"}], "editor/context": [{"submenu": "vscode-augment.context-submenu", "group": "0_augment"}, {"command": "vscode-augment.next-edit.force", "group": "1_modification", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.context-submenu": [{"command": "vscode-augment.focusAugmentPanel", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.explain", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.test", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.fix", "when": "editorHasSelection"}, {"command": "vscode-augment.chat.slash.document", "when": "editorHasSelection"}], "editor/lineNumber/context": [{"command": "_vscode-augment.next-edit.background.open", "when": "vscode-augment.enableNextEdit && editorLineNumber in vscode-augment.nextEdit.linesWithGutterIconActions && resourcePath in vscode-augment.nextEdit.filesWithGutterIconActions", "group": "navigation@0"}], "commandPalette": [{"command": "vscode-augment.internal-dv.o", "when": "vscode-augment.enableDebugFeatures"}, {"command": "vscode-augment.insertCompletion", "when": "!editor<PERSON><PERSON><PERSON><PERSON>"}, {"command": "vscode-augment.toggleAutomaticCompletionSetting"}, {"command": "vscode-augment.signIn", "when": "vscode-augment.useOAuth && !vscode-augment.isLoggedIn"}, {"command": "vscode-augment.signOut", "when": "vscode-augment.useOAuth && vscode-augment.isLoggedIn"}, {"command": "vscode-augment.internal-dv.i", "when": "(vscode-augment.internal-new-instructions.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus"}, {"command": "vscode-augment.chat.slash.fix"}, {"command": "vscode-augment.startNewChat"}, {"command": "vscode-augment.focusAugmentPanel", "when": "vscode-augment.enableDebugFeatures || vscode-augment.isLoggedIn"}, {"command": "_vscode-augment.showSidebarChat", "when": "false"}, {"command": "_vscode-augment.showSidebarWorkspaceContext", "when": "false"}, {"command": "vscode-augment.clear-recent-editing-history", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.generateCommitMessage", "when": "false"}, {"command": "vscode-augment.showSettingsPanel"}, {"command": "vscode-augment.showRemoteAgentsPanel", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"command": "vscode-augment.openSshConfig", "when": "vscode-augment.featureFlags.enableRemoteAgents"}, {"command": "vscode-augment.createSecret", "when": "vscode-augment.enableDebugFeatures"}, {"command": "vscode-augment.listSecrets", "when": "vscode-augment.enableDebugFeatures"}, {"command": "vscode-augment.deleteSecret", "when": "vscode-augment.enableDebugFeatures"}, {"command": "vscode-augment.next-edit.open-panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}, {"command": "vscode-augment.next-edit.force", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.accept", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll"}, {"command": "vscode-augment.next-edit.background.reject", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll"}, {"command": "vscode-augment.next-edit.background.dismiss", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.next", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.next-forward", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.background.previous", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.toggle-bg", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.toggle-all-highlights", "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.background.accept-code-action", "when": "false"}, {"command": "vscode-augment.next-edit.toggle-panel-horizontal-split", "when": "view == augment-next-edit"}, {"command": "vscode-augment.next-edit.update", "when": "vscode-augment.enableNextEdit"}, {"command": "_vscode-augment.next-edit.update.loading", "when": "false"}, {"command": "_vscode-augment.next-edit.update.disabled-no-changes", "when": "false"}, {"command": "_vscode-augment.next-edit.update.disabled-cached", "when": "false"}, {"command": "vscode-augment.next-edit.learn-more", "when": "view == augment-next-edit"}, {"command": "_vscode-augment.next-edit.background.open", "when": "false"}, {"command": "_vscode-augment.next-edit.background.next-forward.disabled", "when": "false"}, {"command": "_vscode-augment.next-edit.background.previous.disabled", "when": "false"}], "scm/title": [{"command": "vscode-augment.generateCommitMessage", "args": ["${resourceUri}"], "group": "navigation", "when": "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0"}], "editor/title": [{"submenu": "vscode-augment.next-edit.editor-action-submenu", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading"}, {"submenu": "vscode-augment.next-edit.editor-action-submenu.disabled", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && !vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading"}, {"submenu": "vscode-augment.next-edit.editor-action-submenu.loading", "group": "navigation@43", "when": "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.loading"}], "vscode-augment.next-edit.panel-submenu": [{"command": "vscode-augment.next-edit.background.accept-all", "when": "vscode-augment.nextEdit.canAcceptAll", "title": "Accept All", "group": "2_more@1"}, {"command": "vscode-augment.next-edit.background.reject-all", "when": "vscode-augment.nextEdit.canRejectAll", "title": "Reject All", "group": "2_more@2"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_more@3"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_more@4"}], "vscode-augment.next-edit.editor-action-submenu": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.next-edit.editor-action-submenu.disabled": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}], "vscode-augment.next-edit.editor-action-submenu.loading": [{"command": "vscode-augment.next-edit.background.previous", "title": "Go to Previous Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@1"}, {"command": "vscode-augment.next-edit.background.next-forward", "title": "Go to Next Suggestion", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)", "group": "2_augment@2"}, {"command": "vscode-augment.next-edit.open-panel", "title": "Open Panel", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel", "group": "2_augment@3"}, {"command": "vscode-augment.next-edit.force", "group": "2_augment@4", "when": "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || augment-next-edit.active)"}, {"command": "vscode-augment.next-edit.enable-bg", "title": "Enable Background Suggestions", "when": "vscode-augment.enableNextEdit && !vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.disable-bg", "title": "Disable Background Suggestions", "when": "vscode-augment.enableNextEdit && vscode-augment.enableNextEditBackgroundSuggestions", "group": "2_augment@5"}, {"command": "vscode-augment.next-edit.settings", "title": "Next Edit Settings...", "group": "2_augment@6", "when": "vscode-augment.enableNextEdit"}, {"command": "vscode-augment.next-edit.learn-more", "title": "Learn about Next Edit...", "group": "2_augment@7", "when": "vscode-augment.enableNextEdit"}]}, "viewsContainers": {"activitybar": [{"icon": "media/activitybar.svg", "id": "augment-chat", "title": "Augment"}], "panel": [{"icon": "media/activitybar.svg", "id": "augment-panel", "title": "Augment Next Edit"}]}, "views": {"augment-chat": [{"id": "augment-chat", "name": "Augment", "type": "webview"}], "augment-panel": [{"id": "augment-next-edit", "name": "Augment Next Edit", "type": "webview", "when": "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel"}]}}, "scripts": {"build": "pnpm run vscode:build-dev && pnpm run common-webviews:build", "build:prod": "pnpm run vscode:esbuild-prod && pnpm run common-webviews:build", "lint": "pnpm run vscode:lint", "lint:fix": "pnpm run vscode:lint:fix", "test": "pnpm run vscode:test && pnpm run test:e2e", "test:fix": "pnpm run lint:fix && pnpm run test", "test:debug": "LOG_LEVEL=verbose pnpm run test", "test:e2e": "wdio run ./wdio.conf.ts", "watch": "concurrently 'npm:*:watch'", "package-extension": "STABLE_VSCODE_RELEASE_VERSION=$(head -n1 version)-$(git rev-parse --abbrev-ref HEAD).$(git log --format='%ct.%h' -n1) bazel build //clients/vscode:package-extension-prerelease --config=local_output --compilation_mode=opt --stamp && cp ../../bazel-bin/clients/vscode/vscode-augment-prerelease.vsix .", "package-extension-pnpm": "rm -rf out ; rm -rf webviews/dist ; pnpm run build:prod ; pnpm run embed-version-info && pnpm run package-for-release && cp package.json.bak package.json && rm package.json.bak", "marketplace-data": "vsce show --json Augment.vscode-augment", "embed-version-info": "./embed-version-info.py", "vsce-package": "vsce package --no-dependencies --allow-star-activation --skip-license --out=\"${EXTENSION_FILENAME:-out/vscode-augment.vsix}\"", "package-for-release": "[ \"$RELEASE_CHANNEL\" = \"prerelease\" ] && pnpm run vsce-package -- --pre-release || pnpm run vsce-package", "vscode:build-dev": "pnpm run vscode:esbuild-sourcemaps", "vscode:watch": "npm run vscode:esbuild-base -- --watch --sourcemap", "vscode:esbuild-prod": "npm run vscode:esbuild-base -- --minify", "vscode:esbuild-sourcemaps": "npm run vscode:esbuild-base -- --sourcemap", "vscode:esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=out/extension.js --external:vscode --format=cjs --platform=node", "vscode:extension:dev:hmr": "pnpm run -r dev:vite-hmr-vscode", "vscode:extension:dev:watch": ". ./.augment-hmr-env && pnpm exec ibazel run //clients/vscode:build_dev_to_workspace_hmr --config='local_output' --compilation_mode=dbg --action_env=AUGMENT_HMR=${AUGMENT_HMR} --action_env=AUGMENT_JS_ENV=${AUGMENT_JS_ENV}", "vscode:extension:dev:watch-no-hmr": "AUGMENT_JS_ENV=development pnpm exec ibazel run //clients/vscode:build_dev_to_workspace --config='local_output' --compilation_mode=dbg --action_env=AUGMENT_JS_ENV=development", "vscode:hmr:write:port": "node scripts/generate-augment-hmr-env.js", "vscode:lint": "pnpm run vscode:eslint && pnpm run vscode:prettier", "vscode:lint:fix": "pnpm run vscode:eslint:fix && pnpm run vscode:prettier:fix", "vscode:eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "vscode:eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "vscode:jest": "jest --config ./jest.config.js", "vscode:prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "vscode:prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --files", "vscode:test": "pnpm run build:prod && pnpm run vscode:lint && pnpm run vscode:jest", "common-webviews:build": "cd ../common/webviews && pnpm run build:vscode", "contributes-gen": "tsx scripts/contributes/cli.ts"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@jest/globals": "^29.7.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/diff": "^7.0.1", "@types/glob": "^7.2.0", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/lodash.memoize": "^4.1.9", "@types/lodash.throttle": "^4.1.9", "@types/node": "18.15.0", "@types/node-forge": "^1.3.11", "@types/semver": "^7.5.8", "@types/uuid": "^9.0.8", "@types/vscode": "^1.82.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vscode/test-electron": "^2.3.9", "@wdio/cli": "^9.3.0", "@wdio/globals": "^8.27.0", "@wdio/local-runner": "^8.27.0", "@wdio/mocha-framework": "^8.27.0", "@wdio/spec-reporter": "^8.27.0", "@wdio/types": "^8.27.0", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "esbuild": "^0.25.8", "eslint": "^8.57.0", "eslint-plugin-jest": "^27.6.3", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-unused-imports": "^3.2.0", "fetch-mock": "^9.11.0", "fetch-mock-jest": "^1.5.1", "glob": "^8.1.0", "isomorphic-fetch": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-mock-vscode": "^3.0.4", "node-fetch": "^2.7.0", "nodemon": "^3.0.3", "prebuildify-ci": "^1.0.5", "replace-in-file": "^6.3.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsx": "^4.17.0", "typescript": "^5.5.3", "wdio-vscode-service": "^6.1.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.27.2", "@anthropic-ai/vertex-sdk": "^0.4.1", "@bufbuild/protobuf": "^2.3.0", "@connectrpc/connect": "^2.0.2", "@types/archiver": "^6.0.3", "@vscode/vsce": "2.29.0", "archiver": "^7.0.1", "denque": "^2.1.0", "diff": "^7.0.0", "encoding": "^0.1.13", "exponential-backoff": "^3.1.2", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "ignore": "^5.3.0", "jest-junit": "^16.0.0", "json5": "^2.2.3", "level": "^10.0.0", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lodash.throttle": "^4.1.1", "lru-cache": "^11.0.0", "mac-ca": "^3.1.0", "monaco-editor": "^0.52.2", "node-diff3": "^3.1.2", "p-limit": "^3.1.0", "prettier-plugin-svelte": "^3.2.3", "prosemirror-model": "^1.23.0", "semver": "^7.6.3", "shlex": "^2.1.2", "simple-git": "^3.27.0", "typescript-eslint": "7.12.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-transport": "^4.6.0", "zod": "^3.23.8"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.524.1", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Augment Computing", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1754553144498, "pinned": false, "source": "gallery", "id": "fc0e137d-e132-47ed-9455-c4636fa5b897", "publisherId": "7814b14b-491a-4e83-83ac-9222fa835050", "publisherDisplayName": "Augment Computing", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false, "size": 32230949}, "isValid": true, "validations": [], "preRelease": false}, {"type": 1, "identifier": {"id": "rooveterinaryinc.roo-cline", "uuid": "4ce92b26-476a-4dd1-bf50-a8df00b87a74"}, "manifest": {"name": "roo-cline", "displayName": "Roo Code", "description": "A whole dev team of AI agents in your editor.", "publisher": "RooVeterinaryInc", "version": "3.25.8", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0", "node": "20.19.2"}, "author": {"name": "Roo Code"}, "repository": {"type": "git", "url": "https://github.com/RooCodeInc/Roo-Code"}, "homepage": "https://github.com/RooCodeInc/Roo-Code", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama", "roo code", "roocode"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "roo-cline-ActivityBar", "title": "Roo Code", "icon": "assets/icons/icon.svg"}]}, "views": {"roo-cline-ActivityBar": [{"type": "webview", "id": "roo-cline.SidebarProvider", "name": "Roo Code"}]}, "commands": [{"command": "roo-cline.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "title": "Modes", "icon": "$(organization)"}, {"command": "roo-cline.mcpButtonClicked", "title": "MCP Servers", "icon": "$(server)"}, {"command": "roo-cline.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "roo-cline.marketplaceButtonClicked", "title": "Marketplace", "icon": "$(extensions)"}, {"command": "roo-cline.popoutButtonClicked", "title": "Open in Editor", "icon": "$(link-external)"}, {"command": "roo-cline.accountButtonClicked", "title": "Account", "icon": "$(account)"}, {"command": "roo-cline.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "roo-cline.openInNewTab", "title": "Open In New Tab", "category": "Roo Code"}, {"command": "roo-cline.explainCode", "title": "Explain Code", "category": "Roo Code"}, {"command": "roo-cline.fixCode", "title": "Fix Code", "category": "Roo Code"}, {"command": "roo-cline.improveCode", "title": "Improve Code", "category": "Roo Code"}, {"command": "roo-cline.addToContext", "title": "Add To Context", "category": "Roo Code"}, {"command": "roo-cline.newTask", "title": "New Task", "category": "Roo Code"}, {"command": "roo-cline.terminalAddToContext", "title": "Add Terminal Content to Context", "category": "Terminal"}, {"command": "roo-cline.terminalFixCommand", "title": "Fix This Command", "category": "Terminal"}, {"command": "roo-cline.terminalExplainCommand", "title": "Explain This Command", "category": "Terminal"}, {"command": "roo-cline.setCustomStoragePath", "title": "Set Custom Storage Path", "category": "Roo Code"}, {"command": "roo-cline.importSettings", "title": "Import Settings", "category": "Roo Code"}, {"command": "roo-cline.focusInput", "title": "Focus Input Field", "category": "Roo Code"}, {"command": "roo-cline.acceptInput", "title": "Accept Input/Suggestion", "category": "Roo Code"}], "menus": {"editor/context": [{"submenu": "roo-cline.contextMenu", "group": "1"}], "roo-cline.contextMenu": [{"command": "roo-cline.addToContext", "group": "1_actions@1"}, {"command": "roo-cline.explainCode", "group": "1_actions@2"}, {"command": "roo-cline.improveCode", "group": "1_actions@3"}], "terminal/context": [{"submenu": "roo-cline.terminalMenu", "group": "2"}], "roo-cline.terminalMenu": [{"command": "roo-cline.terminalAddToContext", "group": "1_actions@1"}, {"command": "roo-cline.terminalFixCommand", "group": "1_actions@2"}, {"command": "roo-cline.terminalExplainCommand", "group": "1_actions@3"}], "view/title": [{"command": "roo-cline.plusButtonClicked", "group": "navigation@1", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.marketplaceButtonClicked", "group": "navigation@2", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.settingsButtonClicked", "group": "navigation@3", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.accountButtonClicked", "group": "navigation@4", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.historyButtonClicked", "group": "overflow@1", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "group": "overflow@2", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.mcpButtonClicked", "group": "overflow@3", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.popoutButtonClicked", "group": "overflow@4", "when": "view == roo-cline.SidebarProvider"}], "editor/title": [{"command": "roo-cline.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.marketplaceButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.settingsButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.accountButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.historyButtonClicked", "group": "overflow@1", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "group": "overflow@2", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.mcpButtonClicked", "group": "overflow@3", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.popoutButtonClicked", "group": "overflow@4", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}]}, "submenus": [{"id": "roo-cline.contextMenu", "label": "Roo Code"}, {"id": "roo-cline.terminalMenu", "label": "Roo Code"}], "configuration": {"title": "Roo Code", "properties": {"roo-cline.allowedCommands": {"type": "array", "items": {"type": "string"}, "default": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled"}, "roo-cline.deniedCommands": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Command prefixes that will be automatically denied without asking for approval. In case of conflicts with allowed commands, the longest prefix match takes precedence. Add * to deny all commands."}, "roo-cline.commandExecutionTimeout": {"type": "number", "default": 0, "minimum": 0, "maximum": 600, "description": "Maximum time in seconds to wait for command execution to complete before timing out (0 = no timeout, 1-600s, default: 0s)"}, "roo-cline.commandTimeoutAllowlist": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Command prefixes that are excluded from the command execution timeout. Commands matching these prefixes will run without timeout restrictions."}, "roo-cline.preventCompletionWithOpenTodos": {"type": "boolean", "default": false, "description": "Prevent task completion when there are incomplete todos in the todo list"}, "roo-cline.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "The vendor of the language model (e.g. copilot)"}, "family": {"type": "string", "description": "The family of the language model (e.g. gpt-4)"}}, "description": "Settings for VSCode Language Model API"}, "roo-cline.customStoragePath": {"type": "string", "default": "", "description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\RooCodeStorage')"}, "roo-cline.enableCodeActions": {"type": "boolean", "default": true, "description": "Enable Roo Code quick fixes"}, "roo-cline.autoImportSettingsPath": {"type": "string", "default": "", "description": "Path to a RooCode configuration file to automatically import on extension startup. Supports absolute paths and paths relative to the home directory (e.g. '~/Documents/roo-code-settings.json'). Leave empty to disable auto-import."}, "roo-cline.useAgentRules": {"type": "boolean", "default": true, "description": "Enable loading of AGENTS.md files for agent-specific rules (see https://agent-rules.org/)"}}}}, "scripts": {"lint": "eslint . --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "pretest": "turbo run bundle --cwd ..", "test": "vitest run", "format": "prettier --write .", "bundle": "node esbuild.mjs", "vscode:prepublish": "pnpm bundle --production", "vsix": "mkdirp ../bin && vsce package --no-dependencies --out ../bin", "publish:marketplace": "vsce publish --no-dependencies && ovsx publish --no-dependencies", "watch:bundle": "pnpm bundle --watch", "watch:tsc": "cd .. && tsc --noEmit --watch --project src/tsconfig.json", "clean": "rimraf README.md CHANGELOG.md LICENSE dist logs mock .turbo"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@aws-sdk/client-bedrock-runtime": "^3.848.0", "@aws-sdk/credential-providers": "^3.848.0", "@google/genai": "^1.0.0", "@lmstudio/sdk": "^1.1.1", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.9.0", "@qdrant/js-client-rest": "^1.14.0", "@roo-code/cloud": "workspace:^", "@roo-code/ipc": "workspace:^", "@roo-code/telemetry": "workspace:^", "@roo-code/types": "workspace:^", "@types/lodash.debounce": "^4.0.9", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "exceljs": "^4.4.0", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.0.0", "fastest-levenshtein": "^1.0.16", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "google-auth-library": "^9.15.1", "gray-matter": "^4.0.3", "i18next": "^25.0.0", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "lodash.debounce": "^4.0.8", "mammoth": "^1.9.1", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "node-ipc": "^12.0.0", "openai": "^5.0.0", "os-name": "^6.0.0", "p-limit": "^6.2.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^5.0.0", "pretty-bytes": "^7.0.0", "proper-lockfile": "^4.1.2", "ps-tree": "^1.2.0", "puppeteer-chromium-resolver": "^24.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "sanitize-filename": "^1.6.3", "say": "^0.16.0", "serialize-error": "^12.0.0", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "stream-json": "^1.8.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.12", "turndown": "^7.2.0", "uuid": "^11.1.0", "vscode-material-icons": "^0.1.1", "web-tree-sitter": "^0.25.6", "workerpool": "^9.2.0", "yaml": "^2.8.0", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/build": "workspace:^", "@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/clone-deep": "^4.0.4", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-cache": "^4.1.3", "@types/node-ipc": "^9.2.3", "@types/proper-lockfile": "^4.1.4", "@types/ps-tree": "^1.1.6", "@types/stream-json": "^1.7.8", "@types/string-similarity": "^4.0.2", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "3.3.2", "esbuild": "^0.25.0", "execa": "^9.5.2", "glob": "^11.0.1", "mkdirp": "^3.0.1", "nock": "^14.0.4", "npm-run-all2": "^8.0.1", "ovsx": "0.10.4", "rimraf": "^6.0.1", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "5.8.3", "vitest": "^3.2.3", "zod-to-ts": "^1.2.0"}}, "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8", "scheme": "file"}, "isBuiltin": false, "targetPlatform": "undefined", "publisherDisplayName": "Roo Code", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1754553162500, "pinned": false, "source": "vsix", "id": "4ce92b26-476a-4dd1-bf50-a8df00b87a74", "publisherDisplayName": "Roo Code", "publisherId": "4b55a936-06f2-4448-983f-3caa1b8e500a", "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "size": 139090834}, "isValid": true, "validations": [], "preRelease": false}]}