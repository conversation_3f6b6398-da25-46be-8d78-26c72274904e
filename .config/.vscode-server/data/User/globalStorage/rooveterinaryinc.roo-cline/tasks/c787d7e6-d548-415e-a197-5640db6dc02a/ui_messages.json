[{"ts": 1747325667555, "type": "say", "say": "text", "text": "help me fix this issue thats happening when trying to run my @/fullstack/backend/app.py  using the Run button in replit. Traceback (most recent call last):\n  File \"/home/<USER>/workspace/fullstack/backend/app.py\", line 9, in <module>\n    from modules.ppObjects import Pick, BoostPromo, ProtectedPromo\n  File \"/home/<USER>/workspace/fullstack/backend/modules/ppObjects.py\", line 1, in <module>\n    from ConfidenceDB_Port import generate_event_id\nModuleNotFoundError: No module named 'ConfidenceDB_Port'", "images": []}, {"ts": 1747325667659, "type": "say", "say": "api_req_started", "text": "{\"request\":\"<task>\\nhelp me fix this issue thats happening when trying to run my 'fullstack/backend/app.py' (see below for file content)  using the Run button in replit. Traceback (most recent call last):\\n  File \\\"/home/<USER>/workspace/fullstack/backend/app.py\\\", line 9, in <module>\\n    from modules.ppObjects import Pick, BoostPromo, ProtectedPromo\\n  File \\\"/home/<USER>/workspace/fullstack/backend/modules/ppObjects.py\\\", line 1, in <module>\\n    from ConfidenceDB_Port import generate_event_id\\nModuleNotFoundError: No module named 'ConfidenceDB_Port'\\n</task>\\n\\n<file_content path=\\\"fullstack/backend/app.py\\\">\\n  1 | import os\\n  2 | import sys\\n  3 | import json\\n  4 | import random\\n  5 | import sqlite3\\n  6 | from datetime import datetime\\n  7 | from itertools import combinations\\n  8 | from flask import Flask, request, jsonify, render_template, send_from_directory\\n  9 | from modules.ppObjects import Pick, BoostPromo, ProtectedPromo\\n 10 | from modules.RROptimizer import analyze_all_splits\\n 11 | from modules.ConfidenceDB_Port import submit_event, generate_event_id\\n 12 | \\n 13 | # Add the HandicapperAccuracy folder to the Python path\\n 14 | # sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'HandicapperAccuracy')))\\n 15 | \\n 16 | from Backend.HandiCapperAccuracyModel import main_model\\n 17 | \\n 18 | app = Flask(__name__, static_folder=\\\"../frontend/build\\\", static_url_path=\\\"/\\\")\\n 19 | \\n 20 | # Global object lists\\n 21 | pick_objects = []\\n 22 | boost_promo_objects = []\\n 23 | protected_promo_objects = []\\n 24 | next_id = 1\\n 25 | user_accuracy = 0.0\\n 26 | \\n 27 | DB_PATH = os.path.join(_WORKSPACE_ROOT, \\\"Data\\\", \\\"pick_confidence.db\\\")\\n 28 | _DB_DIR = os.path.dirname(DB_PATH)\\n 29 | if not os.path.exists(_DB_DIR):\\n 30 |     os.makedirs(_DB_DIR)\\n 31 |     print(f\\\"Created data directory: {_DB_DIR}\\\")\\n 32 | \\n 33 | \\n 34 | origin_profiles = {\\n 35 |     \\\"ChalkBoardPI\\\": 0.80,\\n 36 |     \\\"HarryLock\\\": 0.71,\\n 37 |     \\\"DanGamblePOD\\\": 0.78,\\n 38 |     \\\"DanGambleAIEdge\\\": 90.0,\\n 39 |     \\\"GameScript\\\": 0.80,\\n 40 |     \\\"Winible\\\": 0.83,\\n 41 |     \\\"DoberMan\\\": 0.76,\\n 42 |     \\\"JoshMiller\\\": 0.60,\\n 43 |     \\\"Me\\\": lambda: user_accuracy\\n 44 | }\\n 45 | \\n 46 | def confidence_score(decimal_odds, expert_confidence, expert_accuracy):\\n 47 |     implied_prob = 1 / decimal_odds\\n 48 |     c = expert_confidence / 100\\n 49 |     a = expert_accuracy / 100\\n 50 |     score = 100 * (a * c + (1 - a) * implied_prob)\\n 51 |     return score\\n 52 | \\n 53 | def generate_round_robin_subparlays(pick_list, subgroup_size):\\n 54 |     \\\"\\\"\\\"Returns a list of subparlays (combinations) of picks.\\\"\\\"\\\"\\n 55 |     return [list(combo) for combo in combinations(pick_list, subgroup_size)]\\n 56 | \\n 57 | def get_all_objects():\\n 58 |     return pick_objects, boost_promo_objects, protected_promo_objects\\n 59 | \\n 60 | @app.route(\\\"/get_picks\\\", methods=[\\\"GET\\\"])\\n 61 | def get_picks():\\n 62 |     return jsonify({\\n 63 |     \\\"objects\\\": [p.to_dict() for p in pick_objects]\\n 64 |     })\\n 65 | \\n 66 | @app.route(\\\"/\\\")\\n 67 | def index():\\n 68 |     return send_from_directory(app.static_folder, \\\"index.html\\\")\\n 69 | \\n 70 | @app.route(\\\"/optimize_split\\\", methods=[\\\"GET\\\"])\\n 71 | def optimize_split():\\n 72 |     sorted_picks = sorted(pick_objects, key=lambda x: getattr(x, \\\"confidence_score\\\", 0), reverse=True)\\n 73 |     print(\\\"Picks:\\\" + str(sorted_picks))\\n 74 |     best_score, best_label = analyze_all_splits(sorted_picks)\\n 75 |     #total_capital = request.json.get(\\\"total_capital\\\", 0)\\n 76 | \\n 77 | \\n 78 |     if \\\"Full List\\\" in best_label:\\n 79 |         picks_to_use = sorted_picks\\n 80 |     elif \\\"Left\\\" in best_label:\\n 81 |         split_index = int(best_label.split(\\\"index\\\")[1].strip())\\n 82 |         picks_to_use = sorted_picks[:split_index]\\n 83 |     elif \\\"Right\\\" in best_label:\\n 84 |         split_index = int(best_label.split(\\\"index\\\")[1].strip())\\n 85 |         picks_to_use = sorted_picks[split_index:]\\n 86 |     else:\\n 87 |         picks_to_use = []\\n 88 | \\n 89 |     # Extract optimal subgroup size from the label (e.g., \\\"Size 3\\\")\\n 90 |     import re\\n 91 |     match = re.search(r\\\"Size (\\\\d+)\\\", best_label)\\n 92 |     subgroup_size = int(match.group(1)) if match else 2\\n 93 | \\n 94 |     # Generate the subparlays (2D array of pick dicts)\\n 95 |     subparlays = generate_round_robin_subparlays(picks_to_use, subgroup_size)\\n 96 | \\n 97 |     return jsonify({\\n 98 |         \\\"best_score\\\": best_score,\\n 99 |         \\\"best_config\\\": best_label,\\n100 |         \\\"sorted_picks\\\": [p.to_dict() for p in sorted_picks],\\n101 |         \\\"subparlays\\\": [[p.to_dict() for p in sub] for sub in subparlays]\\n102 |     })\\n103 | \\n104 | \\n105 | \\n106 | @app.route(\\\"/process\\\", methods=[\\\"POST\\\"])\\n107 | def process():\\n108 |     global next_id\\n109 |     data = request.get_json()\\n110 | \\n111 |     try:\\n112 |         print(\\\"🟢 PAYLOAD RECEIVED:\\\", data)\\n113 | \\n114 |         name = data.get(\\\"name\\\", \\\"\\\").strip()\\n115 |         pick_origins = data.get(\\\"pick_origin\\\", [])  # [{ name, confidence }]\\n116 |         print(\\\"odds:\\\" + str(data.get(\\\"odds\\\", 0)))\\n117 |         odds = float(data.get(\\\"odds\\\", 0))\\n118 |         leagues = data.get(\\\"league\\\", [])\\n119 |         reusable = data.get(\\\"reusable\\\", True)\\n120 |         capital_limit = int(data.get(\\\"capital_limit\\\", 0))\\n121 |         mutual_exclusion = int(data.get(\\\"mutual_exclusion\\\", -1))\\n122 |         pick_type = data.get(\\\"pick_type\\\", \\\"MoneyLine\\\")\\n123 |         player_team = data.get(\\\"player_team\\\", \\\"None\\\")\\n124 |         stat_type = data.get(\\\"stat_type\\\", \\\"MoneyLine\\\")\\n125 | \\n126 |         if not name or not odds or not pick_origins or not leagues:\\n127 |             return jsonify({\\\"response\\\": \\\"Missing required fields\\\", \\\"success\\\": False}), 400\\n128 | \\n129 |         implied_prob = round(1 / odds, 4)  # crowd probability from odds\\n130 |         today = datetime.today().strftime(\\\"%Y-%m-%d\\\")\\n131 | \\n132 |         expert_predictions = []\\n133 |         total_score = 0\\n134 | \\n135 |         for origin_obj in pick_origins:\\n136 |             origin = origin_obj.get(\\\"name\\\")\\n137 |             print(origin_obj.get(\\\"confidence\\\"))\\n138 |             origin_conf = origin_obj.get(\\\"confidence\\\")\\n139 | \\n140 |             if not origin:\\n141 |                 continue  # Skip invalid entries\\n142 | \\n143 |             # 💡 Ensure origin_conf is a usable float\\n144 |             try:\\n145 |                 used_conf = float(origin_conf)\\n146 |             except Exception as e:\\n147 |                 used_conf = 75.0  # fallback if None or not a number\\n148 | \\n149 |             # 🔧 Normalize the origin key by removing spaces\\n150 |             origin_key = origin.replace(\\\" \\\", \\\"\\\")\\n151 | \\n152 |             if origin_key not in origin_profiles:\\n153 |                 raise KeyError(f\\\"Origin key '{origin_key}' not found in origin_profiles\\\")\\n154 | \\n155 |             origin_accuracy = origin_profiles[origin_key]() if callable(origin_profiles[origin_key]) else origin_profiles[origin_key]\\n156 | \\n157 | \\n158 |             norm_conf = used_conf / 100.0\\n159 |             # Extract shared prediction direction\\n160 |             prediction = int(data.get(\\\"prediction\\\", 1))  # 1 = Higher, 0 = Lower\\n161 | \\n162 |             for origin_obj in pick_origins:\\n163 |                 origin = origin_obj.get(\\\"name\\\")\\n164 |                 origin_conf = origin_obj.get(\\\"confidence\\\")\\n165 | \\n166 |                 if not origin:\\n167 |                     continue  # Skip invalid entries\\n168 | \\n169 |                 # 💡 Ensure origin_conf is a usable float\\n170 |                 try:\\n171 |                     used_conf = float(origin_conf)\\n172 |                 except Exception:\\n173 |                     used_conf = 75.0  # fallback if None or not a number\\n174 | \\n175 |                 # 🧠 Historical accuracy\\n176 |                 origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]\\n177 | \\n178 |                 norm_conf = used_conf / 100.0\\n179 | \\n180 |                 # ⬅️ Use the shared prediction value for all experts\\n181 |                 expert_predictions.append((origin, prediction, norm_conf))\\n182 | \\n183 |                 # 🧮 Confidence score calculation\\n184 |                 score = confidence_score(odds, used_conf, origin_accuracy)\\n185 |                 total_score += score\\n186 | \\n187 |         # final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0\\n188 |         # print(final_score)\\n189 | \\n190 |         if pick_type == \\\"MoneyLine\\\":\\n191 |             team_a = name\\n192 |             team_b = \\\"Other\\\"\\n193 |             player_team = \\\"None\\\"\\n194 |         else:\\n195 |             team_a = \\\"Over\\\"\\n196 |             team_b = \\\"Under\\\"\\n197 | \\n198 |         print(\\\"🧠 Parsed expert predictions:\\\", expert_predictions)\\n199 | \\n200 |         status_messages = []\\n201 |         success_count = 0\\n202 | \\n203 |         for league in leagues:\\n204 |             event_id = generate_event_id(name, league)\\n205 |             # Check if the event already exists\\n206 |             conn = sqlite3.connect(DB_PATH)\\n207 |             cursor = conn.cursor()\\n208 |             cursor.execute(\\\"SELECT 1 FROM events WHERE event_id = ?\\\", (event_id,))\\n209 |             exists = cursor.fetchone() is not None\\n210 |             conn.close()\\n211 | \\n212 |             if not exists:\\n213 |                 # Insert event & predictions as usual\\n214 |                 success, message = submit_event(\\n215 |                     event_id=event_id,\\n216 |                     event_date=today,\\n217 |                     league=league,\\n218 |                     team_a=team_a,\\n219 |                     team_b=team_b,\\n220 |                     crowd_probability=implied_prob,\\n221 |                     expert_predictions=expert_predictions,\\n222 |                     actual_result=None,\\n223 |                     pick_type=pick_type,\\n224 |                     player_team=player_team,\\n225 |                     stat_type=stat_type\\n226 |                 )\\n227 |             else:\\n228 |                 # Just insert additional expert predictions and update crowd prob\\n229 |                 try:\\n230 |                     conn = sqlite3.connect(DB_PATH)\\n231 |                     cursor = conn.cursor()\\n232 | \\n233 |                     # ✅ Update crowd_probability (optional)\\n234 |                     cursor.execute(\\\"\\\"\\\"\\n235 |                         INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)\\n236 |                         VALUES (?, ?)\\n237 |                     \\\"\\\"\\\", (event_id, implied_prob))\\n238 | \\n239 |                     # ✅ Append new expert predictions\\n240 |                     for expert_name, prediction, confidence in expert_predictions:\\n241 |                         cursor.execute(\\\"\\\"\\\"\\n242 |                             INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)\\n243 |                             VALUES (?, ?, ?, ?)\\n244 |                         \\\"\\\"\\\", (event_id, expert_name, prediction, confidence))\\n245 | \\n246 |                         # Ensure expert exists in reliability table\\n247 |                         cursor.execute(\\\"SELECT 1 FROM expert_reliability WHERE expert_name = ?\\\", (expert_name,))\\n248 |                         if not cursor.fetchone():\\n249 |                             cursor.execute(\\\"\\\"\\\"\\n250 |                                 INSERT INTO expert_reliability (expert_name)\\n251 |                                 VALUES (?)\\n252 |                             \\\"\\\"\\\", (expert_name,))\\n253 | \\n254 |                     conn.commit()\\n255 |                     conn.close()\\n256 |                     success = True\\n257 |                     message = \\\"Existing event updated with new expert predictions.\\\"\\n258 | \\n259 |                 except Exception as e:\\n260 |                     success = False\\n261 |                     message = f\\\"Error updating existing event: {e}\\\"\\n262 | \\n263 |             status_messages.append(f\\\"[{league}] {message}\\\")\\n264 | \\n265 |             #----USE ML MODEL TO ANALYZE PICK-------\\n266 |             prediction_result = main_model(event_id)\\n267 |             ml_prob = prediction_result[\\\"combined_prob\\\"]\\n268 |             logistic_prob = prediction_result[\\\"logistic_prob\\\"]\\n269 |             bayesian_prob = prediction_result[\\\"bayesian_prob\\\"]\\n270 |             bayesian_conf = prediction_result[\\\"quality_score\\\"]\\n271 | \\n272 | \\n273 |             # Calculate implied probability\\n274 |             implied_prob = round(1 / odds, 4)\\n275 |             print(\\\"IP:\\\" + str(implied_prob))\\n276 | \\n277 |             # Step 1: Compute raw difference\\n278 |             raw_score = ml_prob - implied_prob  # can range from -1 to +1\\n279 | \\n280 |             print(\\\"RS:\\\"+ str(raw_score))\\n281 | \\n282 |             # Step 2: Clamp to ±0.2 range\\n283 |             clamped_score = max(min(raw_score, 0.2), -0.2)\\n284 | \\n285 |             print(\\\"CS:\\\"+ str(clamped_score))\\n286 | \\n287 |             # Step 3: Scale to 0–100\\n288 |             scaled_score = round((clamped_score + 0.2) / 0.4 * 100)\\n289 | \\n290 |             print(\\\"FS:\\\"+ str(scaled_score))\\n291 | \\n292 |             final_score = scaled_score  # this is now an int from 0 to 100\\n293 | \\n294 |             print(\\\"odds:\\\" + str(odds))\\n295 | \\n296 |             if success:\\n297 |                 new_pick = Pick(\\n298 |                     name=name,\\n299 |                     odds=odds,\\n300 |                     confidence=final_score,\\n301 |                     mutual_exclusion_group=mutual_exclusion,\\n302 |                     league=league,\\n303 |                     event_id=event_id,\\n304 |                     bayesian_prob =bayesian_prob, \\n305 |                     logistic_prob=logistic_prob, \\n306 |                     bayesian_conf=bayesian_conf,\\n307 |                     stat_type=stat_type,\\n308 |                     reusable=reusable,\\n309 |                     capital_limit=capital_limit\\n310 |                 )\\n311 |                 pick_objects.append(new_pick)\\n312 |                 next_id += 1\\n313 |                 success_count += 1\\n314 | \\n315 |         return jsonify({\\n316 |             \\\"response\\\": \\\" | \\\".join(status_messages),\\n317 |             \\\"objects\\\": [p.__dict__ for p in pick_objects],\\n318 |             \\\"success\\\": success_count == len(leagues)\\n319 |         })\\n320 | \\n321 |     except Exception as e:\\n322 |         print(\\\"❌ SERVER ERROR:\\\", e)\\n323 |         return jsonify({\\\"response\\\": f\\\"Server error: {str(e)}\\\", \\\"success\\\": False}), 500\\n324 | \\n325 | \\n326 | \\n327 | @app.route(\\\"/create_boost_promo\\\", methods=[\\\"POST\\\"])\\n328 | def create_boost_promo():\\n329 |     data = request.get_json()\\n330 |     boost_percentage = int(data.get(\\\"boost_percentage\\\", 0))\\n331 |     required_picks = int(data.get(\\\"required_picks\\\", 0))\\n332 |     same_sport = data.get(\\\"same_sport\\\", False)\\n333 | \\n334 |     boost = BoostPromo(boost_percentage, required_picks, same_sport)\\n335 |     boost_promo_objects.append(boost.__dict__)\\n336 | \\n337 |     return jsonify({\\\"response\\\": f\\\"Created Boost Promo: {boost.name}\\\", \\\"boost_promos\\\": boost_promo_objects})\\n338 | \\n339 | @app.route(\\\"/create_protected_promo\\\", methods=[\\\"POST\\\"])\\n340 | def create_protected_promo():\\n341 |     data = request.get_json()\\n342 |     protected_amount = int(data.get(\\\"protected_amount\\\", 0))\\n343 |     eligible_leagues = data.get(\\\"eligible_leagues\\\", [])\\n344 | \\n345 |     protected = ProtectedPromo(protected_amount, eligible_leagues)\\n346 |     protected_promo_objects.append(protected.__dict__)\\n347 | \\n348 |     return jsonify({\\\"response\\\": f\\\"Created Protected Play Promo: {protected.name}\\\", \\\"protected_promos\\\": protected_promo_objects})\\n349 | \\n350 | @app.route(\\\"/edit\\\", methods=[\\\"POST\\\"])\\n351 | def edit():\\n352 |     global pick_objects\\n353 |     data = request.get_json()\\n354 |     obj_id = data.get(\\\"id\\\")\\n355 | \\n356 |     for obj in pick_objects:\\n357 |         if obj.pID == obj_id:\\n358 |             # Update fields on the Pick object\\n359 |             obj.name = data.get(\\\"name\\\", obj.name)\\n360 |             obj.decimalOdds = float(data.get(\\\"odds\\\", obj.decimalOdds))\\n361 |             obj.pick_origin = data.get(\\\"pick_origin\\\", obj.pick_origin)\\n362 |             obj.league = data.get(\\\"league\\\", obj.league)\\n363 |             obj.reusable = data.get(\\\"reusable\\\", obj.reusable)\\n364 |             obj.capital_limit = int(data.get(\\\"capital_limit\\\", obj.capital_limit))\\n365 |             obj.gameID = int(data.get(\\\"mutual_exclusion\\\", obj.gameID))\\n366 |             obj.pick_type = data.get(\\\"pick_type\\\", obj.pick_type)\\n367 |             obj.player_team = data.get(\\\"player_team\\\", obj.player_team)\\n368 |             obj.stat_type = data.get(\\\"stat_type\\\", obj.stat_type)\\n369 | \\n370 |             name = obj.name\\n371 |             odds = obj.decimalOdds\\n372 |             leagues = obj.league\\n373 |             pick_origins = obj.pick_origin\\n374 |             pick_type = obj.pick_type\\n375 |             player_team = obj.player_team\\n376 |             stat_type = obj.stat_type\\n377 | \\n378 |             # Determine team_a and team_b based on pick_type\\n379 |             if pick_type == \\\"MoneyLine\\\":\\n380 |                 team_a = name\\n381 |                 team_b = \\\"Other\\\"\\n382 |                 player_team = \\\"None\\\"\\n383 |             else:\\n384 |                 team_a = \\\"Over\\\"\\n385 |                 team_b = \\\"Under\\\"\\n386 | \\n387 |             implied_prob = round(1 / odds, 4)\\n388 |             today = datetime.today().strftime(\\\"%Y-%m-%d\\\")\\n389 | \\n390 |             # Recalculate expert prediction score\\n391 |             expert_predictions = []\\n392 |             total_score = 0\\n393 | \\n394 |             for origin_obj in pick_origins:\\n395 |                 origin = origin_obj.get(\\\"name\\\")\\n396 |                 origin_conf = origin_obj.get(\\\"confidence\\\", None)\\n397 |                 prediction = origin_obj.get(\\\"prediction\\\", 1)  # default to Higher\\n398 | \\n399 |                 origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]\\n400 |                 norm_conf = origin_conf / 100 if origin_conf is not None else None\\n401 | \\n402 |                 expert_predictions.append((origin, prediction, norm_conf))\\n403 | \\n404 |                 used_conf = origin_conf if origin_conf is not None else 75.0\\n405 |                 score = confidence_score(odds, used_conf, origin_accuracy)\\n406 |                 total_score += score\\n407 | \\n408 |             final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0\\n409 |             obj.confidence = None\\n410 |             obj.confidence_score = final_score\\n411 | \\n412 |             # Update the database\\n413 |             for league in leagues:\\n414 |                 event_id = generate_event_id(name, league)\\n415 |                 obj.event_id = event_id\\n416 | \\n417 |                 try:\\n418 |                     conn = sqlite3.connect(DB_PATH)\\n419 |                     cursor = conn.cursor()\\n420 | \\n421 |                     cursor.execute(\\\"\\\"\\\"\\n422 |                         INSERT OR REPLACE INTO events (\\n423 |                             event_id, event_date, league,\\n424 |                             team_a, team_b, actual_result,\\n425 |                             pick_type, player_team, stat_type\\n426 |                         ) VALUES (?, ?, ?, ?, ?, COALESCE(\\n427 |                             (SELECT actual_result FROM events WHERE event_id = ?), NULL\\n428 |                         ), ?, ?, ?)\\n429 |                     \\\"\\\"\\\", (\\n430 |                         event_id, today, league,\\n431 |                         team_a, team_b, event_id,\\n432 |                         pick_type, player_team, stat_type\\n433 |                     ))\\n434 | \\n435 |                     cursor.execute(\\\"\\\"\\\"\\n436 |                         INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)\\n437 |                         VALUES (?, ?)\\n438 |                     \\\"\\\"\\\", (event_id, implied_prob))\\n439 | \\n440 |                     cursor.execute(\\\"DELETE FROM expert_predictions WHERE event_id = ?\\\", (event_id,))\\n441 |                     for origin, prediction, confidence in expert_predictions:\\n442 |                         cursor.execute(\\\"\\\"\\\"\\n443 |                             INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)\\n444 |                             VALUES (?, ?, ?, ?)\\n445 |                         \\\"\\\"\\\", (event_id, origin, prediction, confidence))\\n446 | \\n447 |                         cursor.execute(\\\"SELECT 1 FROM expert_reliability WHERE expert_name = ?\\\", (origin,))\\n448 |                         if not cursor.fetchone():\\n449 |                             cursor.execute(\\\"\\\"\\\"\\n450 |                                 INSERT INTO expert_reliability (expert_name)\\n451 |                                 VALUES (?)\\n452 |                             \\\"\\\"\\\", (origin,))\\n453 | \\n454 |                     conn.commit()\\n455 |                     conn.close()\\n456 | \\n457 |                 except Exception as e:\\n458 |                     print(f\\\"❌ DB Error while editing {event_id}: {e}\\\")\\n459 | \\n460 |             break\\n461 | \\n462 |     return jsonify({\\\"objects\\\": [p.__dict__ for p in pick_objects]})\\n463 | \\n464 | ##### CURRENT SPOT IS CREATING RELIABLE EVENT ID FROM PICK ID ####\\n465 | def get_event_id_from_pick_id(pick_id):\\n466 |     for pick in pick_objects:\\n467 |         print(pick.pID)\\n468 |         if pick.pID == pick_id:\\n469 |             return pick.event_id\\n470 |     return None\\n471 | \\n472 | \\n473 | \\n474 | @app.route(\\\"/submit_verified\\\", methods=[\\\"POST\\\"])\\n475 | def submit_verified():\\n476 |     data = request.get_json()\\n477 |     verified = data.get(\\\"verified\\\", [])\\n478 | \\n479 |     print(pick_objects)\\n480 | \\n481 |     conn = sqlite3.connect(DB_PATH)\\n482 |     cursor = conn.cursor()\\n483 |     updated_ids = []\\n484 |     localID = 0\\n485 | \\n486 |     for item in verified:\\n487 |         localID+=1\\n488 |         pick_id = localID\\n489 |         \\n490 | \\n491 |         # Assume item[\\\"actual_result\\\"] is 1 if user marked it \\\"Verified\\\"\\n492 |         user_verification = item[\\\"actual_result\\\"]\\n493 | \\n494 |         # Find pick object by ID\\n495 |         pick = next((p for p in pick_objects if hasattr(p, \\\"pID\\\") and p.pID == pick_id), None)\\n496 |         if not pick:\\n497 |             print(f\\\"❌ Pick ID {pick_id} not found in memory.\\\")\\n498 |             continue\\n499 | \\n500 |         event_id = getattr(pick, \\\"event_id\\\", None)\\n501 |         if not event_id:\\n502 |             print(f\\\"❌ Pick ID {pick_id} has no event_id.\\\")\\n503 |             continue\\n504 | \\n505 |         # 🔍 Interpret prediction direction:\\n506 |         #  - If expert said \\\"Higher\\\" and user marked as 1 → event happened ✅\\n507 |         #  - If expert said \\\"Lower\\\" and user marked as 1 → event did NOT happen ❌\\n508 |         # We'll default to the first expert's prediction signal (they're all assumed to match)\\n509 |         cursor.execute(\\\"SELECT prediction FROM expert_predictions WHERE event_id = ? LIMIT 1\\\", (event_id,))\\n510 |         row = cursor.fetchone()\\n511 | \\n512 |         if row:\\n513 |             expert_prediction = row[0]  # 1 = Higher, 0 = Lower\\n514 | \\n515 |             if expert_prediction == 1:  # Higher = event is expected to occur\\n516 |                 actual_result = 1 if user_verification == 1 else 0\\n517 |             else:  # Lower = expert expects event NOT to occur\\n518 |                 actual_result = 0 if user_verification == 1 else 1\\n519 |         else:\\n520 |             print(f\\\"⚠️ No expert prediction found for {event_id}, assuming default.\\\")\\n521 |             actual_result = user_verification\\n522 |         event_id = get_event_id_from_pick_id(pick_id)\\n523 |         print(event_id)\\n524 | \\n525 |         # ✅ Find pick object by ID (from actual class instances)\\n526 |         pick = next((p for p in pick_objects if hasattr(p, \\\"pID\\\") and p.pID == pick_id), None)\\n527 |         if not pick:\\n528 |             print(f\\\"❌ Pick ID {pick_id} not found in memory.\\\")\\n529 |             continue\\n530 | \\n531 |         event_id = getattr(pick, \\\"event_id\\\", None)\\n532 |         if not event_id:\\n533 |             print(f\\\"❌ Pick ID {pick_id} has no event_id.\\\")\\n534 |             continue\\n535 | \\n536 |         print(f\\\"✅ Updating event_id: {event_id} → actual_result: {actual_result}\\\")\\n537 |         try:\\n538 |             cursor.execute(\\\"\\\"\\\"\\n539 |                 UPDATE events\\n540 |                 SET actual_result = ?\\n541 |                 WHERE event_id = ?\\n542 |             \\\"\\\"\\\", (actual_result, event_id))\\n543 | \\n544 |             if cursor.rowcount > 0:\\n545 |                 updated_ids.append(event_id)\\n546 |             else:\\n547 |                 print(f\\\"⚠️ No rows updated for {event_id} (may not exist in DB).\\\")\\n548 | \\n549 |         except Exception as e:\\n550 |             print(f\\\"❌ DB error updating {event_id}: {e}\\\")\\n551 | \\n552 |     conn.commit()\\n553 |     conn.close()\\n554 | \\n555 |     return jsonify({\\\"message\\\": f\\\"Updated {len(updated_ids)} events with actual results.\\\"})\\n556 | \\n557 | \\n558 | \\n559 | \\n560 | \\n561 | \\n562 | @app.route(\\\"/load_sample_picks\\\", methods=[\\\"POST\\\"])\\n563 | def load_sample_picks():\\n564 |     global pick_objects\\n565 |     pick_objects = []\\n566 | \\n567 |     num_picks = 8\\n568 |     example_names = [\\\"Lakers ML\\\", \\\"Yankees -1.5\\\", \\\"Chiefs +3\\\", \\\"Over 8.5\\\", \\\"Under 220\\\", \\\"Dodgers ML\\\", \\\"Ravens -2.5\\\", \\\"Heat +6\\\", \\\"Bills ML\\\", \\\"Nets Over 230\\\"]\\n569 |     leagues = [\\\"NBA\\\", \\\"NFL\\\", \\\"MLB\\\", \\\"NHL\\\"]\\n570 | \\n571 |     for i in range(num_picks):\\n572 |         name = random.choice(example_names) + f\\\" #{i+1}\\\"\\n573 |         odds = round(random.uniform(1.05, 2.5), 2)\\n574 |         mutual_exclusion_group = random.randint(0, 5)\\n575 |         league = random.choice(leagues)\\n576 |         reusable = random.choice([True, False])\\n577 |         capital_limit = random.randint(10, 100)\\n578 |         stat_type = \\\"MoneyLine\\\"\\n579 |         event_id = f\\\"SAMPLE-{i+1}\\\"\\n580 | \\n581 |         # Generate synthetic model probabilities\\n582 |         bayesian_prob = round(random.uniform(0.4, 0.9), 2)\\n583 |         logistic_prob = round(random.uniform(0.4, 0.9), 2)\\n584 |         bayesian_conf = round(random.uniform(0.5, 0.9), 2)\\n585 | \\n586 |         # Calculate final confidence score using model-weighted blend\\n587 |         combined_prob = round(\\n588 |             bayesian_conf * bayesian_prob + (1 - bayesian_conf) * logistic_prob, 4\\n589 |         )\\n590 |         implied_prob = 1 / odds\\n591 |         raw_score = combined_prob - implied_prob\\n592 |         clamped = max(min(raw_score, 0.2), -0.2)\\n593 |         scaled_score = round((clamped + 0.2) / 0.4 * 100, 2)\\n594 | \\n595 |         # Create Pick object\\n596 |         new_pick = Pick(\\n597 |             name=name,\\n598 |             odds=odds,\\n599 |             confidence=scaled_score,\\n600 |             mutual_exclusion_group=mutual_exclusion_group,\\n601 |             league=league,\\n602 |             event_id=event_id,\\n603 |             bayesian_prob=bayesian_prob,\\n604 |             logistic_prob=logistic_prob,\\n605 |             bayesian_conf=bayesian_conf,\\n606 |             stat_type=stat_type,\\n607 |             reusable=reusable,\\n608 |             capital_limit=capital_limit\\n609 |         )\\n610 | \\n611 |         pick_objects.append(new_pick)\\n612 | \\n613 |     return jsonify({\\n614 |         \\\"message\\\": f\\\"{num_picks} sample picks loaded.\\\",\\n615 |         \\\"objects\\\": [p.to_dict() for p in pick_objects]\\n616 |     })\\n617 | \\n618 | \\n619 | @app.route(\\\"/clear_picks\\\", methods=[\\\"POST\\\"])\\n620 | def clear_picks():\\n621 |     global pick_objects\\n622 |     pick_objects = []\\n623 |     Pick.pID_counter = 0  # reset ID counter\\n624 | \\n625 |     # Optional: clear optimizer results too if you're storing those separately\\n626 |     # e.g., if you eventually save best_score or best_label in global vars\\n627 | \\n628 |     return jsonify({\\n629 |         \\\"message\\\": \\\"All picks cleared.\\\",\\n630 |         \\\"objects\\\": pick_objects\\n631 |     })\\n632 | \\n633 | \\n634 | @app.route(\\\"/delete\\\", methods=[\\\"POST\\\"])\\n635 | def delete():\\n636 |     data = request.get_json()\\n637 |     obj_id = data.get(\\\"id\\\")\\n638 | \\n639 |     global pick_objects\\n640 |     deleted_pick = None\\n641 | \\n642 |     # Find the pick\\n643 |     for obj in pick_objects:\\n644 |         if obj[\\\"id\\\"] == obj_id:\\n645 |             deleted_pick = obj\\n646 |             break\\n647 | \\n648 |     if deleted_pick:\\n649 |         name = deleted_pick[\\\"name\\\"]\\n650 |         leagues = deleted_pick[\\\"league\\\"]\\n651 |         for league in leagues:\\n652 |             event_id = generate_event_id(name, league)\\n653 |             try:\\n654 |                 conn = sqlite3.connect(DB_PATH)\\n655 |                 cursor = conn.cursor()\\n656 |                 cursor.execute(\\\"DELETE FROM expert_predictions WHERE event_id = ?\\\", (event_id,))\\n657 |                 cursor.execute(\\\"DELETE FROM crowd_predictions WHERE event_id = ?\\\", (event_id,))\\n658 |                 cursor.execute(\\\"DELETE FROM events WHERE event_id = ?\\\", (event_id,))\\n659 |                 conn.commit()\\n660 |                 conn.close()\\n661 |             except Exception as e:\\n662 |                 print(f\\\"DB error while deleting event {event_id}: {e}\\\")\\n663 | \\n664 |         # Remove from in-memory list\\n665 |         pick_objects = [obj for obj in pick_objects if obj[\\\"id\\\"] != obj_id]\\n666 | \\n667 |     return jsonify({\\n668 |    \\\"objects\\\": [p.to_dict() for p in pick_objects]\\n669 |     })\\n670 | \\n671 | \\n672 | @app.route(\\\"/update_accuracy\\\", methods=[\\\"POST\\\"])\\n673 | def update_accuracy():\\n674 |     global user_accuracy\\n675 |     data = request.get_json()\\n676 |     try:\\n677 |         user_accuracy = float(data.get(\\\"accuracy\\\", 0.0))\\n678 |     except ValueError:\\n679 |         user_accuracy = 0.0\\n680 |     return jsonify({\\\"message\\\": f\\\"Accuracy updated to {user_accuracy}\\\", \\\"user_accuracy\\\": user_accuracy})\\n681 | \\n682 | if __name__ == \\\"__main__\\\":\\n683 |     app.run(host='0.0.0.0', port=5000, debug=True)\\n\\n</file_content>\\n\\n<environment_details>\\n# VSCode Visible Files\\nfullstack/backend/app.py\\n\\n# VSCode Open Tabs\\nfullstack/backend/app.py,fullstack/backend/modules/ConfidenceDB_Port.py\\n\\n# Current Time\\n5/15/2025, 4:14:27 PM (UTC, UTC+0:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>orchestrator</slug>\\n<name>🪃 Orchestrator</name>\\n<model>default-lm</model>\\n\\n\\nNOTE: You are currently in '🪃 Orchestrator' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n\\n# Current Workspace Directory (/home/<USER>/workspace) Files\\n_config.yml\\n.DS_Store\\n.gitignore\\n.replit\\nLICENSE\\nREADME.md\\nrun.sh\\nBackend/\\nBackend/HandiCapperAccuracyModel.py\\nBackend/last_trained.txt\\nBackend/logistic_model.pkl\\nBackend/RROptimizer.py\\nBackend/test.ipynb\\nData/\\nDataScience/\\nDataScience/AiEdgeImporter.py\\nDataScience/Capper_StatFill.py\\nDataScience/ConfidenceDB_Port.py\\nDataScience/DanGambleAiEdge.csv\\nDataScience/Events.csv\\nDataScience/goalserve_test.py\\nDataScience/Image_Extracter.py\\nDataScience/NBA_player_log_scraper.py\\nDataScience/ocr_output.txt\\nDataScience/planet_scale_port_temp.py\\nDataScience/planet_scale_port.py\\nDataScience/test_NBA_stats_importer.py\\nFrontend/\\nFrontend/main.css\\nFrontend/static/how_to_bet.css\\nFrontend/templates/projectparlay.html\\nfullstack/\\nfullstack/backend/app.py\\nfullstack/backend/requirements.txt\\nfullstack/backend/modules/__init__.py\\nfullstack/backend/modules/ConfidenceDB_Port.py\\nfullstack/backend/modules/planet_scale_port.py\\nfullstack/backend/modules/ppObjects.py\\nfullstack/frontend/.gitignore\\nfullstack/frontend/package-lock.json\\nfullstack/frontend/package.json\\nfullstack/frontend/README.md\\nfullstack/frontend/public/favicon.ico\\nfullstack/frontend/public/index.html\\nfullstack/frontend/public/logo192.png\\nfullstack/frontend/public/logo512.png\\nfullstack/frontend/public/manifest.json\\nfullstack/frontend/public/robots.txt\\nfullstack/frontend/src/App.css\\nfullstack/frontend/src/App.js\\nfullstack/frontend/src/App.test.js\\nfullstack/frontend/src/index.css\\nfullstack/frontend/src/index.js\\nfullstack/frontend/src/logo.svg\\nfullstack/frontend/src/reportWebVitals.js\\nfullstack/frontend/src/setupTests.js\\nFullStackMesh/\\nFullStackMesh/ConfidenceDB_Port.py\\nFullStackMesh/HowToBet.py\\nFullStackMesh/planet_scale_port.py\\nFullStackMesh/ppObjects.py\\nFullStackMesh/requirements.txt\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1747325667855, "type": "say", "say": "text", "text": "Language model functionality is limited. Please check VS Code configuration.", "partial": false}, {"ts": 1747325668064, "type": "say", "say": "checkpoint_saved", "text": "dffe6aed7ed08fbc8183c1ad6dc737596e24c414", "checkpoint": {"isFirst": true, "from": "dffe6aed7ed08fbc8183c1ad6dc737596e24c414", "to": "dffe6aed7ed08fbc8183c1ad6dc737596e24c414"}}, {"ts": 1747325668117, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\nfullstack/backend/app.py\\n\\n# VSCode Open Tabs\\nfullstack/backend/app.py,fullstack/backend/modules/ConfidenceDB_Port.py\\n\\n# Current Time\\n5/15/2025, 4:14:28 PM (UTC, UTC+0:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>orchestrator</slug>\\n<name>🪃 Orchestrator</name>\\n<model>default-lm</model>\\n\\n\\nNOTE: You are currently in '🪃 Orchestrator' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1747325668296, "type": "say", "say": "text", "text": "Language model functionality is limited. Please check VS Code configuration.", "partial": false}, {"ts": 1747325668460, "type": "say", "say": "api_req_started", "text": "{\"request\":\"[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\\n\\n# Reminder: Instructions for Tool Use\\n\\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\\n\\n<actual_tool_name>\\n<parameter1_name>value1</parameter1_name>\\n<parameter2_name>value2</parameter2_name>\\n...\\n</actual_tool_name>\\n\\nFor example, to use the attempt_completion tool:\\n\\n<attempt_completion>\\n<result>\\nI have completed the task...\\n</result>\\n</attempt_completion>\\n\\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\\n\\n# Next Steps\\n\\nIf you have completed the user's task, use the attempt_completion tool. \\nIf you require additional information from the user, use the ask_followup_question tool. \\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task. \\n(This is an automated message, so do not respond to it conversationally.)\\n\\n<environment_details>\\n# VSCode Visible Files\\nfullstack/backend/app.py\\n\\n# VSCode Open Tabs\\nfullstack/backend/app.py,fullstack/backend/modules/ConfidenceDB_Port.py\\n\\n# Current Time\\n5/15/2025, 4:14:28 PM (UTC, UTC+0:00)\\n\\n# Current Context Size (Tokens)\\n(Not available)\\n\\n# Current Cost\\n$0.00\\n\\n# Current Mode\\n<slug>orchestrator</slug>\\n<name>🪃 Orchestrator</name>\\n<model>default-lm</model>\\n\\n\\nNOTE: You are currently in '🪃 Orchestrator' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '💻 Code' mode.\\n</environment_details>\",\"tokensIn\":0,\"tokensOut\":0,\"cacheWrites\":0,\"cacheReads\":0,\"cost\":0}"}, {"ts": 1747325668658, "type": "say", "say": "text", "text": "Language model functionality is limited. Please check VS Code configuration.", "partial": false}, {"ts": 1747325668819, "type": "ask", "ask": "mistake_limit_reached", "text": "Roo Code uses complex prompts and iterative task execution that may be challenging for less capable models. For best results, it's recommended to use Claude 3.7 Sonnet for its advanced agentic coding capabilities."}]