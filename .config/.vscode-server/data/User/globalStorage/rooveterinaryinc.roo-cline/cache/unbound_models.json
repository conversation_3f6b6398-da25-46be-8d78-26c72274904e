{"anthropic/claude-3-5-haiku-20241022": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.8, "outputPrice": 4, "cacheWritesPrice": 1, "cacheReadsPrice": 0.08}, "anthropic/claude-3-5-sonnet-20241022": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3}, "anthropic/claude-3-7-sonnet-20250219": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3}, "anthropic/claude-3-haiku-20240307": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.25, "outputPrice": 1.25, "cacheWritesPrice": 0.3, "cacheReadsPrice": 0.03}, "anthropic/claude-3-opus-20240229": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 15, "outputPrice": 75, "cacheWritesPrice": 18.75, "cacheReadsPrice": 1.5}, "anthropic/claude-3-sonnet-20240229": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 3, "outputPrice": 15}, "anthropic/claude-opus-4-20250514": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": true, "inputPrice": 15, "outputPrice": 75, "cacheWritesPrice": 18.75, "cacheReadsPrice": 1.5}, "anthropic/claude-sonnet-4-20250514": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3}, "bedrock/us.anthropic.claude-3-5-sonnet-20240620-v1:0": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3}, "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"maxTokens": 64000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3}, "dashscope/qwen2.5-7b-instruct": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0, "outputPrice": 0, "cacheWritesPrice": 0, "cacheReadsPrice": 0}, "dashscope/qwen2.5-vl-72b-instruct": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0, "outputPrice": 0, "cacheWritesPrice": 0, "cacheReadsPrice": 0}, "fireworks-ai/kimi-k2-instruct": {"maxTokens": 131072, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.6, "outputPrice": 2.5, "cacheWritesPrice": 0, "cacheReadsPrice": 0.15}, "fireworks-ai/qwen3-235b-a22b-instruct-2507": {"maxTokens": 32768, "contextWindow": 256000, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.22, "outputPrice": 0.88, "cacheWritesPrice": 0, "cacheReadsPrice": 0}, "google/gemini-1.5-flash": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.1, "outputPrice": 0.4, "cacheWritesPrice": 0.06, "cacheReadsPrice": 0.01}, "google/gemini-2.0-flash": {"maxTokens": 8192, "contextWindow": 0, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.1, "outputPrice": 0.4, "cacheWritesPrice": 0.3, "cacheReadsPrice": 0.03}, "google/gemini-2.0-flash-lite": {"maxTokens": 8192, "contextWindow": 0, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.08, "outputPrice": 0.3, "cacheWritesPrice": 0.3, "cacheReadsPrice": 0.03}, "google/gemini-2.0-pro-exp-02-05": {"maxTokens": 8192, "contextWindow": 0, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0, "outputPrice": 0, "cacheWritesPrice": 0, "cacheReadsPrice": 0}, "google/gemini-2.5-flash": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.3, "outputPrice": 2.5, "cacheWritesPrice": 0.075, "cacheReadsPrice": 0.075}, "google/gemini-2.5-flash-lite-preview-06-17": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.1, "outputPrice": 0.4, "cacheWritesPrice": 0.025, "cacheReadsPrice": 0.025}, "google/gemini-2.5-flash-preview-04-17": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.15, "outputPrice": 0.6, "cacheWritesPrice": 0.23, "cacheReadsPrice": 0.04}, "google/gemini-2.5-pro": {"maxTokens": 65536, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 1.25, "outputPrice": 10, "cacheWritesPrice": 0.31, "cacheReadsPrice": 0.31}, "google/gemini-2.5-pro-exp-03-25": {"maxTokens": 65536, "contextWindow": 0, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0, "outputPrice": 0, "cacheWritesPrice": 0, "cacheReadsPrice": 0}, "google/gemini-2.5-pro-preview-05-06": {"maxTokens": 65535, "contextWindow": 0, "supportsImages": true, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 1.25, "outputPrice": 10, "cacheWritesPrice": 1.63, "cacheReadsPrice": 0.31}, "meta/meta-llama-3.3-70b-instruct": {"maxTokens": 9500, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.5, "outputPrice": 1.5, "cacheWritesPrice": 1.5, "cacheReadsPrice": 0.5}, "mistral-ai/codestral-latest": {"maxTokens": 32768, "contextWindow": 256000, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.3, "outputPrice": 0.9}, "openai/gpt-3.5-turbo": {"maxTokens": 4096, "contextWindow": 16384, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.5, "outputPrice": 1.5}, "openai/gpt-4.1": {"maxTokens": 32768, "contextWindow": 1047576, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 2, "outputPrice": 8, "cacheWritesPrice": 0, "cacheReadsPrice": 0.5}, "openai/gpt-4.1-mini": {"maxTokens": 32768, "contextWindow": 1047576, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.4, "outputPrice": 1.6, "cacheWritesPrice": 0, "cacheReadsPrice": 0.1}, "openai/gpt-4.1-nano": {"maxTokens": 32768, "contextWindow": 1047576, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 0.1, "outputPrice": 0.4, "cacheWritesPrice": 0, "cacheReadsPrice": 0.025}, "openai/gpt-4o": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 2.5, "outputPrice": 10, "cacheWritesPrice": 0, "cacheReadsPrice": 1.25}, "openai/gpt-4o-mini": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.15, "outputPrice": 0.6, "cacheWritesPrice": 0, "cacheReadsPrice": 0.075}, "openai/o1-mini": {"maxTokens": 65536, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 3, "outputPrice": 12, "cacheWritesPrice": 0, "cacheReadsPrice": 1.5}, "openai/o1-preview": {"maxTokens": 32768, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 15, "outputPrice": 60, "cacheWritesPrice": 0, "cacheReadsPrice": 7.5}, "openai/o3": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 10, "outputPrice": 40, "cacheWritesPrice": 0, "cacheReadsPrice": 2.5}, "openai/o3-mini": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheWritesPrice": 0, "cacheReadsPrice": 0.55}, "openai/o4-mini": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheWritesPrice": 0, "cacheReadsPrice": 0.275}, "tinfoil/deepseek-r1-70b": {"maxTokens": 32768, "contextWindow": 64000, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 2, "outputPrice": 2}, "tinfoil/llama3-3-70b": {"maxTokens": 16384, "contextWindow": 64000, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 2, "outputPrice": 2}, "tinfoil/mistral-small-3-1-24b": {"maxTokens": 128000, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 2, "outputPrice": 2}, "x-ai/grok-2-latest": {"contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 2, "outputPrice": 10}, "x-ai/grok-3-beta": {"contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 3, "outputPrice": 15}, "x-ai/grok-3-fast-beta": {"contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 5, "outputPrice": 25}, "x-ai/grok-3-mini-beta": {"contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.3, "outputPrice": 0.5}, "x-ai/grok-3-mini-fast-beta": {"contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 0.6, "outputPrice": 4}, "x-ai/grok-4": {"contextWindow": 256000, "supportsImages": false, "supportsPromptCache": true, "supportsComputerUse": false, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 0, "cacheReadsPrice": 0.75}, "x-ai/grok-beta": {"contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "supportsComputerUse": false, "inputPrice": 5, "outputPrice": 15}}