{"openrouter/horizon-beta": {"maxTokens": 32768, "contextWindow": 256000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "This is a cloaked model provided to the community to gather feedback. This is an improved version of [Horizon Alpha](/openrouter/horizon-alpha)\n\nNote: It’s free to use during this testing period, and prompts and completions are logged by the model creator for feedback and training.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "mistralai/codestral-2508": {"maxTokens": 51200, "contextWindow": 256000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 0.8999999999999999, "description": "Mistral's cutting-edge language model for coding released end of July 2025. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation.\n\n[Blog Post](https://mistral.ai/news/codestral-25-08)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen3-30b-a3b-instruct-2507": {"maxTokens": 32768, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.7999999999999999, "description": "Qwen3-30B-A3B-Instruct-2507 is a 30.5B-parameter mixture-of-experts language model from Qwen, with 3.3B active parameters per inference. It operates in non-thinking mode and is designed for high-quality instruction following, multilingual understanding, and agentic tool use. Post-trained on instruction data, it demonstrates competitive performance across reasoning (AIME, ZebraLogic), coding (MultiPL-E, LiveCodeBench), and alignment (IFEval, WritingBench) benchmarks. It outperforms its non-instruct variant on subjective and open-ended tasks while retaining strong factual and coding performance.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "z-ai/glm-4.5": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.19999999999999998, "description": "GLM-4.5 is our latest flagship foundation model, purpose-built for agent-based applications. It leverages a Mixture-of-Experts (MoE) architecture and supports a context length of up to 128k tokens. GLM-4.5 delivers significantly enhanced capabilities in reasoning, code generation, and agent alignment. It supports a hybrid inference mode with two options, a \"thinking mode\" designed for complex reasoning and tool use, and a \"non-thinking mode\" optimized for instant responses. Users can control the reasoning behaviour with the `reasoning` `enabled` boolean. [Learn more in our docs](https://openrouter.ai/docs/use-cases/reasoning-tokens#enable-reasoning-with-default-config)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "z-ai/glm-4.5-air:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "GLM-4.5-Air is the lightweight variant of our latest flagship model family, also purpose-built for agent-centric applications. Like GLM-4.5, it adopts the Mixture-of-Experts (MoE) architecture but with a more compact parameter size. GLM-4.5-Air also supports hybrid inference modes, offering a \"thinking mode\" for advanced reasoning and tool use, and a \"non-thinking mode\" for real-time interaction. Users can control the reasoning behaviour with the `reasoning` `enabled` boolean. [Learn more in our docs](https://openrouter.ai/docs/use-cases/reasoning-tokens#enable-reasoning-with-default-config)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "z-ai/glm-4.5-air": {"maxTokens": 96000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 1.1, "cacheReadsPrice": 0.03, "description": "GLM-4.5-Air is the lightweight variant of our latest flagship model family, also purpose-built for agent-centric applications. Like GLM-4.5, it adopts the Mixture-of-Experts (MoE) architecture but with a more compact parameter size. GLM-4.5-Air also supports hybrid inference modes, offering a \"thinking mode\" for advanced reasoning and tool use, and a \"non-thinking mode\" for real-time interaction. Users can control the reasoning behaviour with the `reasoning` `enabled` boolean. [Learn more in our docs](https://openrouter.ai/docs/use-cases/reasoning-tokens#enable-reasoning-with-default-config)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-235b-a22b-thinking-2507": {"maxTokens": 52429, "contextWindow": 262144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.1179, "outputPrice": 0.1179, "description": "Qwen3-235B-A22B-Thinking-2507 is a high-performance, open-weight Mixture-of-Experts (MoE) language model optimized for complex reasoning tasks. It activates 22B of its 235B parameters per forward pass and natively supports up to 262,144 tokens of context. This \"thinking-only\" variant enhances structured logical reasoning, mathematics, science, and long-form generation, showing strong benchmark performance across AIME, SuperGPQA, LiveCodeBench, and MMLU-Redux. It enforces a special reasoning mode (</think>) and is designed for high-token outputs (up to 81,920 tokens) in challenging domains.\n\nThe model is instruction-tuned and excels at step-by-step reasoning, tool use, agentic workflows, and multilingual tasks. This release represents the most capable open-source variant in the Qwen3-235B series, surpassing many closed models in structured reasoning use cases.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "z-ai/glm-4-32b": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.09999999999999999, "description": "GLM 4 32B is a cost-effective foundation language model.\n\nIt can efficiently perform complex tasks and has significantly enhanced capabilities in tool use, online search, and code-related intelligent tasks.\n\nIt is made by the same lab behind the thudm models.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen3-coder:free": {"maxTokens": 52429, "contextWindow": 262144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen3-Coder-480B-A35B-Instruct is a Mixture-of-Experts (MoE) code generation model developed by the Qwen team. It is optimized for agentic coding tasks such as function calling, tool use, and long-context reasoning over repositories. The model features 480 billion total parameters, with 35 billion active per forward pass (8 out of 160 experts).\n\nPricing for the Alibaba endpoints varies by context length. Once a request is greater than 128k input tokens, the higher pricing is used.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen3-coder": {"maxTokens": 52429, "contextWindow": 262144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 1.2, "description": "Qwen3-Coder-480B-A35B-Instruct is a Mixture-of-Experts (MoE) code generation model developed by the Qwen team. It is optimized for agentic coding tasks such as function calling, tool use, and long-context reasoning over repositories. The model features 480 billion total parameters, with 35 billion active per forward pass (8 out of 160 experts).\n\nPricing for the Alibaba endpoints varies by context length. Once a request is greater than 128k input tokens, the higher pricing is used.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "bytedance/ui-tars-1.5-7b": {"maxTokens": 2048, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.19999999999999998, "description": "UI-TARS-1.5 is a multimodal vision-language agent optimized for GUI-based environments, including desktop interfaces, web browsers, mobile systems, and games. Built by ByteDance, it builds upon the UI-TARS framework with reinforcement learning-based reasoning, enabling robust action planning and execution across virtual interfaces.\n\nThis model achieves state-of-the-art results on a range of interactive and grounding benchmarks, including OSworld, WebVoyager, AndroidWorld, and ScreenSpot. It also demonstrates perfect task completion across diverse Poki games and outperforms prior models in Minecraft agent tasks. UI-TARS-1.5 supports thought decomposition during inference and shows strong scaling across variants, with the 1.5 version notably exceeding the performance of earlier 72B and 7B checkpoints.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-2.5-flash-lite": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.09999999999999999, "outputPrice": 0.*****************, "cacheWritesPrice": 0.18330000000000002, "cacheReadsPrice": 0.024999999999999998, "description": "Gemini 2.5 Flash-Lite is a lightweight reasoning model in the Gemini 2.5 family, optimized for ultra-low latency and cost efficiency. It offers improved throughput, faster token generation, and better performance across common benchmarks compared to earlier Flash models. By default, \"thinking\" (i.e. multi-pass reasoning) is disabled to prioritize speed, but developers can enable it via the [Reasoning API parameter](https://openrouter.ai/docs/use-cases/reasoning-tokens) to selectively trade off cost for intelligence. ", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-235b-a22b-2507": {"maxTokens": 52429, "contextWindow": 262144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.1179, "outputPrice": 0.1179, "description": "Qwen3-235B-A22B-Instruct-2507 is a multilingual, instruction-tuned mixture-of-experts language model based on the Qwen3-235B architecture, with 22B active parameters per forward pass. It is optimized for general-purpose text generation, including instruction following, logical reasoning, math, code, and tool usage. The model supports a native 262K context length and does not implement \"thinking mode\" (<think> blocks).\n\nCompared to its base variant, this version delivers significant gains in knowledge coverage, long-context reasoning, coding benchmarks, and alignment with open-ended tasks. It is particularly strong on multilingual understanding, math reasoning (e.g., AIME, HMMT), and alignment evaluations like Arena-Hard and WritingBench.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "switchpoint/router": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.85, "outputPrice": 3.4, "description": "Switchpoint AI's router instantly analyzes your request and directs it to the optimal AI from an ever-evolving library. \n\nAs the world of LLMs advances, our router gets smarter, ensuring you always benefit from the industry's newest models without changing your workflow.\n\nThis model is configured for a simple, flat rate per response here on OpenRouter. It's powered by the full routing engine from [Switchpoint AI](https://www.switchpoint.dev).", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "moonshotai/kimi-k2:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Kimi K2 Instruct is a large-scale Mixture-of-Experts (MoE) language model developed by Moonshot AI, featuring 1 trillion total parameters with 32 billion active per forward pass. It is optimized for agentic capabilities, including advanced tool use, reasoning, and code synthesis. Kimi K2 excels across a broad range of benchmarks, particularly in coding (LiveCodeBench, SWE-bench), reasoning (ZebraLogic, GPQA), and tool-use (Tau2, AceBench) tasks. It supports long-context inference up to 128K tokens and is designed with a novel training stack that includes the MuonClip optimizer for stable large-scale MoE training.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "moonshotai/kimi-k2": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.0878, "outputPrice": 0.0878, "description": "Kimi K2 Instruct is a large-scale Mixture-of-Experts (MoE) language model developed by Moonshot AI, featuring 1 trillion total parameters with 32 billion active per forward pass. It is optimized for agentic capabilities, including advanced tool use, reasoning, and code synthesis. Kimi K2 excels across a broad range of benchmarks, particularly in coding (LiveCodeBench, SWE-bench), reasoning (ZebraLogic, GPQA), and tool-use (Tau2, AceBench) tasks. It supports long-context inference up to 128K tokens and is designed with a novel training stack that includes the MuonClip optimizer for stable large-scale MoE training.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thudm/glm-4.1v-9b-thinking": {"maxTokens": 8000, "contextWindow": 65536, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.035, "outputPrice": 0.13799999999999998, "description": "GLM-4.1V-9B-Thinking is a 9B parameter vision-language model developed by THUDM, based on the GLM-4-9B foundation. It introduces a reasoning-centric \"thinking paradigm\" enhanced with reinforcement learning to improve multimodal reasoning, long-context understanding (up to 64K tokens), and complex problem solving. It achieves state-of-the-art performance among models in its class, outperforming even larger models like Qwen-2.5-VL-72B on a majority of benchmark tasks. ", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "mistralai/devstral-medium": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 2, "description": "Devstral Medium is a high-performance code generation and agentic reasoning model developed jointly by Mistral AI and All Hands AI. Positioned as a step up from Devstral Small, it achieves 61.6% on SWE-Bench Verified, placing it ahead of Gemini 2.5 Pro and GPT-4.1 in code-related tasks, at a fraction of the cost. It is designed for generalization across prompt styles and tool use in code agents and frameworks.\n\nDevstral Medium is available via API only (not open-weight), and supports enterprise deployment on private infrastructure, with optional fine-tuning capabilities.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/devstral-small": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.07, "outputPrice": 0.28, "description": "Devstral Small 1.1 is a 24B parameter open-weight language model for software engineering agents, developed by Mistral AI in collaboration with All Hands AI. Finetuned from Mistral Small 3.1 and released under the Apache 2.0 license, it features a 128k token context window and supports both Mistral-style function calling and XML output formats.\n\nDesigned for agentic coding workflows, Devstral Small 1.1 is optimized for tasks such as codebase exploration, multi-file edits, and integration into autonomous development agents like OpenHands and Cline. It achieves 53.6% on SWE-Bench Verified, surpassing all other open models on this benchmark, while remaining lightweight enough to run on a single 4090 GPU or Apple silicon machine. The model uses a Tekken tokenizer with a 131k vocabulary and is deployable via vLLM, Transformers, Ollama, LM Studio, and other OpenAI-compatible runtimes.\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cognitivecomputations/dolphin-mistral-24b-venice-edition:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Venice Uncensored Dolphin Mistral 24B Venice Edition is a fine-tuned variant of Mistral-Small-24B-Instruct-2501, developed by dphn.ai in collaboration with Venice.ai. This model is designed as an “uncensored” instruct-tuned LLM, preserving user control over alignment, system prompts, and behavior. Intended for advanced and unrestricted use cases, Venice Uncensored emphasizes steerability and transparent behavior, removing default safety and alignment layers typically found in mainstream assistant models.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "x-ai/grok-4": {"maxTokens": 51200, "contextWindow": 256000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 15, "cacheReadsPrice": 0.75, "description": "Grok 4 is xAI's latest reasoning model with a 256k context window. It supports parallel tool calling, structured outputs, and both image and text inputs. Note that reasoning is not exposed, reasoning cannot be disabled, and the reasoning effort cannot be specified. Pricing increases once the total tokens in a given request is greater than 128k tokens. See more details on the [xAI docs](https://docs.x.ai/docs/models/grok-4-0709)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "google/gemma-3n-e2b-it:free": {"maxTokens": 2048, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemma 3n E2B IT is a multimodal, instruction-tuned model developed by Google DeepMind, designed to operate efficiently at an effective parameter size of 2B while leveraging a 6B architecture. Based on the MatFormer architecture, it supports nested submodels and modular composition via the Mix-and-Match framework. Gemma 3n models are optimized for low-resource deployment, offering 32K context length and strong multilingual and reasoning performance across common benchmarks. This variant is trained on a diverse corpus including code, math, web, and multimodal data.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "tencent/hunyuan-a13b-instruct:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Hunyuan-A13B is a 13B active parameter Mixture-of-Experts (MoE) language model developed by Tencent, with a total parameter count of 80B and support for reasoning via Chain-of-Thought. It offers competitive benchmark performance across mathematics, science, coding, and multi-turn reasoning tasks, while maintaining high inference efficiency via Grouped Query Attention (GQA) and quantization support (FP8, GPTQ, etc.).", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "tencent/hunyuan-a13b-instruct": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.03, "description": "Hunyuan-A13B is a 13B active parameter Mixture-of-Experts (MoE) language model developed by Tencent, with a total parameter count of 80B and support for reasoning via Chain-of-Thought. It offers competitive benchmark performance across mathematics, science, coding, and multi-turn reasoning tasks, while maintaining high inference efficiency via Grouped Query Attention (GQA) and quantization support (FP8, GPTQ, etc.).", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "tngtech/deepseek-r1t2-chimera:free": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek-TNG-R1T2-Chimera is the second-generation Chimera model from TNG Tech. It is a 671 B-parameter mixture-of-experts text-generation model assembled from DeepSeek-AI’s R1-0528, R1, and V3-0324 checkpoints with an Assembly-of-Experts merge. The tri-parent design yields strong reasoning performance while running roughly 20 % faster than the original R1 and more than 2× faster than R1-0528 under vLLM, giving a favorable cost-to-intelligence trade-off. The checkpoint supports contexts up to 60 k tokens in standard use (tested to ~130 k) and maintains consistent <think> token behaviour, making it suitable for long-context analysis, dialogue and other open-ended generation tasks.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "tngtech/deepseek-r1t2-chimera": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.302, "outputPrice": 0.302, "description": "DeepSeek-TNG-R1T2-Chimera is the second-generation Chimera model from TNG Tech. It is a 671 B-parameter mixture-of-experts text-generation model assembled from DeepSeek-AI’s R1-0528, R1, and V3-0324 checkpoints with an Assembly-of-Experts merge. The tri-parent design yields strong reasoning performance while running roughly 20 % faster than the original R1 and more than 2× faster than R1-0528 under vLLM, giving a favorable cost-to-intelligence trade-off. The checkpoint supports contexts up to 60 k tokens in standard use (tested to ~130 k) and maintains consistent <think> token behaviour, making it suitable for long-context analysis, dialogue and other open-ended generation tasks.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "morph/morph-v3-large": {"maxTokens": 38000, "contextWindow": 81920, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.8999999999999999, "outputPrice": 1.9, "description": "Morph's high-accuracy apply model for complex code edits. 2000+ tokens/sec with 98% accuracy for precise code transformations.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "morph/morph-v3-fast": {"maxTokens": 38000, "contextWindow": 81920, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.8999999999999999, "outputPrice": 1.9, "description": "Morph's fastest apply model for code edits. 4500+ tokens/sec with 96% accuracy for rapid code transformations.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "baidu/ernie-4.5-300b-a47b": {"maxTokens": 12000, "contextWindow": 123000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.28, "outputPrice": 1.1, "description": "ERNIE-4.5-300B-A47B is a 300B parameter Mixture-of-Experts (MoE) language model developed by <PERSON><PERSON> as part of the ERNIE 4.5 series. It activates 47B parameters per token and supports text generation in both English and Chinese. Optimized for high-throughput inference and efficient scaling, it uses a heterogeneous MoE structure with advanced routing and quantization strategies, including FP8 and 2-bit formats. This version is fine-tuned for language-only tasks and supports reasoning, tool parameters, and extended context lengths up to 131k tokens. Suitable for general-purpose LLM applications with high reasoning and throughput demands.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thedrummer/anubis-70b-v1.1": {"maxTokens": 3277, "contextWindow": 16384, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 0.7, "description": "TheDrummer's Anubis v1.1 is an unaligned, creative Llama 3.3 70B model focused on providing character-driven roleplay & stories. It excels at gritty, visceral prose, unique character adherence, and coherent narratives, while maintaining the instruction following Llama 3.3 70B is known for.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "inception/mercury": {"maxTokens": 16000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.25, "outputPrice": 1, "description": "Mercury is the first diffusion large language model (dLLM). Applying a breakthrough discrete diffusion approach, the model runs 5-10x faster than even speed optimized models like GPT-4.1 Nano and Claude 3.5 Haiku while matching their performance. Mercury's speed enables developers to provide responsive user experiences, including with voice agents, search interfaces, and chatbots. Read more in the blog post here. ", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "mistralai/mistral-small-3.2-24b-instruct:free": {"maxTokens": 19200, "contextWindow": 96000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Mistral-Small-3.2-24B-Instruct-2506 is an updated 24B parameter model from Mistral optimized for instruction following, repetition reduction, and improved function calling. Compared to the 3.1 release, version 3.2 significantly improves accuracy on WildBench and Arena Hard, reduces infinite generations, and delivers gains in tool use and structured output tasks.\n\nIt supports image and text inputs with structured outputs, function/tool calling, and strong performance across coding (HumanEval+, MBPP), STEM (MMLU, MATH, GPQA), and vision benchmarks (ChartQA, DocVQA).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-small-3.2-24b-instruct": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.049999999999999996, "outputPrice": 0.09999999999999999, "description": "Mistral-Small-3.2-24B-Instruct-2506 is an updated 24B parameter model from Mistral optimized for instruction following, repetition reduction, and improved function calling. Compared to the 3.1 release, version 3.2 significantly improves accuracy on WildBench and Arena Hard, reduces infinite generations, and delivers gains in tool use and structured output tasks.\n\nIt supports image and text inputs with structured outputs, function/tool calling, and strong performance across coding (HumanEval+, MBPP), STEM (MMLU, MATH, GPQA), and vision benchmarks (ChartQA, DocVQA).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "minimax/minimax-m1": {"maxTokens": 40000, "contextWindow": 1000000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 1.6500000000000001, "description": "MiniMax-M1 is a large-scale, open-weight reasoning model designed for extended context and high-efficiency inference. It leverages a hybrid Mixture-of-Experts (MoE) architecture paired with a custom \"lightning attention\" mechanism, allowing it to process long sequences—up to 1 million tokens—while maintaining competitive FLOP efficiency. With 456 billion total parameters and 45.9B active per token, this variant is optimized for complex, multi-step reasoning tasks.\n\nTrained via a custom reinforcement learning pipeline (CISPO), M1 excels in long-context understanding, software engineering, agentic tool use, and mathematical reasoning. Benchmarks show strong performance across FullStackBench, SWE-bench, MATH, GPQA, and TAU-Bench, often outperforming other open models like DeepSeek R1 and Qwen3-235B.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "google/gemini-2.5-flash-lite-preview-06-17": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.09999999999999999, "outputPrice": 0.*****************, "cacheWritesPrice": 0.18330000000000002, "cacheReadsPrice": 0.024999999999999998, "description": "Gemini 2.5 Flash-Lite is a lightweight reasoning model in the Gemini 2.5 family, optimized for ultra-low latency and cost efficiency. It offers improved throughput, faster token generation, and better performance across common benchmarks compared to earlier Flash models. By default, \"thinking\" (i.e. multi-pass reasoning) is disabled to prioritize speed, but developers can enable it via the [Reasoning API parameter](https://openrouter.ai/docs/use-cases/reasoning-tokens) to selectively trade off cost for intelligence. ", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsReasoningBudget": true}, "google/gemini-2.5-flash": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.3, "outputPrice": 2.5, "cacheWritesPrice": 0.3833, "cacheReadsPrice": 0.075, "description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsReasoningBudget": true}, "google/gemini-2.5-pro": {"maxTokens": 65536, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 1.25, "outputPrice": 10, "cacheWritesPrice": 1.625, "cacheReadsPrice": 0.31, "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsReasoningBudget": true, "requiredReasoningBudget": true}, "moonshotai/kimi-dev-72b:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Kimi-Dev-72B is an open-source large language model fine-tuned for software engineering and issue resolution tasks. Based on Qwen2.5-72B, it is optimized using large-scale reinforcement learning that applies code patches in real repositories and validates them via full test suite execution—rewarding only correct, robust completions. The model achieves 60.4% on SWE-bench Verified, setting a new benchmark among open-source models for software bug fixing and code reasoning.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "openai/o3-pro": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 20, "outputPrice": 80, "description": "The o-series of models are trained with reinforcement learning to think before they answer and perform complex reasoning. The o3-pro model uses more compute to think harder and provide consistently better answers.\n\nNote that BYOK is required for this model. Set up here: https://openrouter.ai/settings/integrations", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "x-ai/grok-3-mini": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 0.5, "cacheReadsPrice": 0.075, "description": "A lightweight model that thinks before responding. Fast, smart, and great for logic-based tasks that do not require deep domain knowledge. The raw thinking traces are accessible.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "x-ai/grok-3": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 15, "cacheReadsPrice": 0.75, "description": "Grok 3 is the latest model from xAI. It's their flagship model that excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.\n\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/magistral-small-2506": {"maxTokens": 40000, "contextWindow": 40000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 1.5, "description": "Magistral Small is a 24B parameter instruction-tuned model based on Mistral-Small-3.1 (2503), enhanced through supervised fine-tuning on traces from Magistral Medium and further refined via reinforcement learning. It is optimized for reasoning and supports a wide multilingual range, including over 20 languages.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "mistralai/magistral-medium-2506": {"maxTokens": 40000, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 5, "description": "Magistral is Mistral's first reasoning model. It is ideal for general purpose use requiring longer thought processing and better accuracy than with non-reasoning LLMs. From legal research and financial forecasting to software development and creative storytelling — this model solves multi-step challenges where transparency and precision are critical.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "mistralai/magistral-medium-2506:thinking": {"maxTokens": 40000, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 5, "description": "Magistral is Mistral's first reasoning model. It is ideal for general purpose use requiring longer thought processing and better accuracy than with non-reasoning LLMs. From legal research and financial forecasting to software development and creative storytelling — this model solves multi-step challenges where transparency and precision are critical.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "google/gemini-2.5-pro-preview": {"maxTokens": 65536, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 1.25, "outputPrice": 10, "cacheWritesPrice": 1.625, "cacheReadsPrice": 0.31, "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.\n", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsReasoningBudget": true}, "deepseek/deepseek-r1-distill-qwen-7b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.19999999999999998, "description": "DeepSeek-R1-Distill-Qwen-7B is a 7 billion parameter dense language model distilled from DeepSeek-R1, leveraging reinforcement learning-enhanced reasoning data generated by DeepSeek's larger models. The distillation process transfers advanced reasoning, math, and code capabilities into a smaller, more efficient model architecture based on Qwen2.5-Math-7B. This model demonstrates strong performance across mathematical benchmarks (92.8% pass@1 on MATH-500), coding tasks (Codeforces rating 1189), and general reasoning (49.1% pass@1 on GPQA Diamond), achieving competitive accuracy relative to larger models while maintaining smaller inference costs.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-0528-qwen3-8b:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek-R1-0528 is a lightly upgraded release of DeepSeek R1 that taps more compute and smarter post-training tricks, pushing its reasoning and inference to the brink of flagship models like O3 and Gemini 2.5 Pro.\nIt now tops math, programming, and logic leaderboards, showcasing a step-change in depth-of-thought.\nThe distilled variant, DeepSeek-R1-0528-Qwen3-8B, transfers this chain-of-thought into an 8 B-parameter form, beating standard Qwen3 8B by +10 pp and tying the 235 B “thinking” giant on AIME 2024.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-0528-qwen3-8b": {"maxTokens": 6400, "contextWindow": 32000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.01, "outputPrice": 0.02, "description": "DeepSeek-R1-0528 is a lightly upgraded release of DeepSeek R1 that taps more compute and smarter post-training tricks, pushing its reasoning and inference to the brink of flagship models like O3 and Gemini 2.5 Pro.\nIt now tops math, programming, and logic leaderboards, showcasing a step-change in depth-of-thought.\nThe distilled variant, DeepSeek-R1-0528-Qwen3-8B, transfers this chain-of-thought into an 8 B-parameter form, beating standard Qwen3 8B by +10 pp and tying the 235 B “thinking” giant on AIME 2024.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-0528:free": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "May 28th update to the [original DeepSeek R1](/deepseek/deepseek-r1) Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-0528": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.272, "outputPrice": 0.272, "description": "May 28th update to the [original DeepSeek R1](/deepseek/deepseek-r1) Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "sarvamai/sarvam-m:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Sarvam-M is a 24 B-parameter, instruction-tuned derivative of Mistral-Small-3.1-24B-Base-2503, post-trained on English plus eleven major Indic languages (bn, hi, kn, gu, mr, ml, or, pa, ta, te). The model introduces a dual-mode interface: “non-think” for low-latency chat and a optional “think” phase that exposes chain-of-thought tokens for more demanding reasoning, math, and coding tasks. \n\nBenchmark reports show solid gains versus similarly sized open models on Indic-language QA, GSM-8K math, and SWE-Bench coding, making Sarvam-M a practical general-purpose choice for multilingual conversational agents as well as analytical workloads that mix English, native Indic scripts, or romanized text.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thedrummer/valkyrie-49b-v1": {"maxTokens": 131072, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.65, "outputPrice": 1, "description": "Built on top of NVIDIA's Llama 3.3 Nemotron Super 49B, <PERSON><PERSON><PERSON> is TheDrummer's newest model drop for creative writing.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "anthropic/claude-opus-4": {"maxTokens": 32000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 15, "outputPrice": 75, "cacheWritesPrice": 18.75, "cacheReadsPrice": 1.5, "description": "Claude Opus 4 is benchmarked as the world’s best coding model, at time of release, bringing sustained performance on complex, long-running tasks and agent workflows. It sets new benchmarks in software engineering, achieving leading results on SWE-bench (72.5%) and Terminal-bench (43.2%). Opus 4 supports extended, agentic workflows, handling thousands of task steps continuously for hours without degradation. \n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsComputerUse": true, "supportsReasoningBudget": true}, "anthropic/claude-sonnet-4": {"maxTokens": 64000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "Claude Sonnet 4 significantly enhances the capabilities of its predecessor, Sonnet 3.7, excelling in both coding and reasoning tasks with improved precision and controllability. Achieving state-of-the-art performance on SWE-bench (72.7%), Sonnet 4 balances capability and computational efficiency, making it suitable for a broad range of applications from routine coding tasks to complex software development projects. Key enhancements include improved autonomous codebase navigation, reduced error rates in agent-driven workflows, and increased reliability in following intricate instructions. Sonnet 4 is optimized for practical everyday use, providing advanced reasoning capabilities while maintaining efficiency and responsiveness in diverse internal and external scenarios.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsComputerUse": true, "supportsReasoningBudget": true}, "mistralai/devstral-small-2505:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/devstral-small-2505": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.03, "description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3n-e4b-it:free": {"maxTokens": 2048, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemma 3n E4B-it is optimized for efficient execution on mobile and low-resource devices, such as phones, laptops, and tablets. It supports multimodal inputs—including text, visual data, and audio—enabling diverse tasks such as text generation, speech recognition, translation, and image analysis. Leveraging innovations like Per-Layer Embedding (PLE) caching and the MatFormer architecture, Gemma 3n dynamically manages memory usage and computational load by selectively activating model parameters, significantly reducing runtime resource requirements.\n\nThis model supports a wide linguistic range (trained in over 140 languages) and features a flexible 32K token context window. Gemma 3n can selectively load parameters, optimizing memory and computational efficiency based on the task or device capabilities, making it well-suited for privacy-focused, offline-capable applications and on-device AI solutions. [Read more in the blog post](https://developers.googleblog.com/en/introducing-gemma-3n/)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3n-e4b-it": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.02, "outputPrice": 0.04, "description": "Gemma 3n E4B-it is optimized for efficient execution on mobile and low-resource devices, such as phones, laptops, and tablets. It supports multimodal inputs—including text, visual data, and audio—enabling diverse tasks such as text generation, speech recognition, translation, and image analysis. Leveraging innovations like Per-Layer Embedding (PLE) caching and the MatFormer architecture, Gemma 3n dynamically manages memory usage and computational load by selectively activating model parameters, significantly reducing runtime resource requirements.\n\nThis model supports a wide linguistic range (trained in over 140 languages) and features a flexible 32K token context window. Gemma 3n can selectively load parameters, optimizing memory and computational efficiency based on the task or device capabilities, making it well-suited for privacy-focused, offline-capable applications and on-device AI solutions. [Read more in the blog post](https://developers.googleblog.com/en/introducing-gemma-3n/)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/codex-mini": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 1.5, "outputPrice": 6, "cacheReadsPrice": 0.375, "description": "codex-mini-latest is a fine-tuned version of o4-mini specifically for use in Codex CLI. For direct use in the API, we recommend starting with gpt-4.1.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "nousresearch/deephermes-3-mistral-24b-preview": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.14100000000000001, "outputPrice": 0.14100000000000001, "description": "DeepHermes 3 (Mistral 24B Preview) is an instruction-tuned language model by Nous Research based on Mistral-Small-24B, designed for chat, function calling, and advanced multi-turn reasoning. It introduces a dual-mode system that toggles between intuitive chat responses and structured “deep reasoning” mode using special system prompts. Fine-tuned via distillation from R1, it supports structured output (JSON mode) and function call syntax for agent-based applications.\n\nDeepHermes 3 supports a **reasoning toggle via system prompt**, allowing users to switch between fast, intuitive responses and deliberate, multi-step reasoning. When activated with the following specific system instruction, the model enters a *\"deep thinking\"* mode—generating extended chains of thought wrapped in `<think></think>` tags before delivering a final answer. \n\nSystem Prompt: You are a deep thinking AI, you may use extremely long chains of thought to deeply consider the problem and deliberate with yourself via systematic reasoning processes to help come to a correct solution prior to answering. You should enclose your thoughts and internal monologue inside <think> </think> tags, and then provide your solution or response to the problem.\n", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "mistralai/mistral-medium-3": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 2, "description": "Mistral Medium 3 is a high-performance enterprise-grade language model designed to deliver frontier-level capabilities at significantly reduced operational cost. It balances state-of-the-art reasoning and multimodal performance with 8× lower cost compared to traditional large models, making it suitable for scalable deployments across professional and industrial use cases.\n\nThe model excels in domains such as coding, STEM reasoning, and enterprise adaptation. It supports hybrid, on-prem, and in-VPC deployments and is optimized for integration into custom workflows. Mistral Medium 3 offers competitive accuracy relative to larger models like Claude Sonnet 3.5/3.7, Llama 4 Maverick, and Command R+, while maintaining broad compatibility across cloud environments.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-2.5-pro-preview-05-06": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 1.25, "outputPrice": 10, "cacheWritesPrice": 1.625, "cacheReadsPrice": 0.31, "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "arcee-ai/spotlight": {"maxTokens": 65537, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.18, "outputPrice": 0.18, "description": "Spotlight is a 7‑billion‑parameter vision‑language model derived from Qwen 2.5‑VL and fine‑tuned by Arcee AI for tight image‑text grounding tasks. It offers a 32 k‑token context window, enabling rich multimodal conversations that combine lengthy documents with one or more images. Training emphasized fast inference on consumer GPUs while retaining strong captioning, visual‐question‑answering, and diagram‑analysis accuracy. As a result, Spotlight slots neatly into agent workflows where screenshots, charts or UI mock‑ups need to be interpreted on the fly. Early benchmarks show it matching or out‑scoring larger VLMs such as LLaVA‑1.6 13 B on popular VQA and POPE alignment tests. ", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "arcee-ai/maestro-reasoning": {"maxTokens": 32000, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.8999999999999999, "outputPrice": 3.3000000000000003, "description": "Maestro Reasoning is Arcee's flagship analysis model: a 32 B‑parameter derivative of Qwen 2.5‑32 B tuned with DPO and chain‑of‑thought RL for step‑by‑step logic. Compared to the earlier 7 B preview, the production 32 B release widens the context window to 128 k tokens and doubles pass‑rate on MATH and GSM‑8K, while also lifting code completion accuracy. Its instruction style encourages structured \"thought → answer\" traces that can be parsed or hidden according to user preference. That transparency pairs well with audit‑focused industries like finance or healthcare where seeing the reasoning path matters. In Arcee Conductor, <PERSON><PERSON> is automatically selected for complex, multi‑constraint queries that smaller SLMs bounce. ", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "arcee-ai/virtuoso-large": {"maxTokens": 64000, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.75, "outputPrice": 1.2, "description": "Virtuoso‑Large is Arcee's top‑tier general‑purpose LLM at 72 B parameters, tuned to tackle cross‑domain reasoning, creative writing and enterprise QA. Unlike many 70 B peers, it retains the 128 k context inherited from Qwen 2.5, letting it ingest books, codebases or financial filings wholesale. Training blended DeepSeek R1 distillation, multi‑epoch supervised fine‑tuning and a final DPO/RLHF alignment stage, yielding strong performance on BIG‑Bench‑Hard, GSM‑8K and long‑context Needle‑In‑Haystack tests. Enterprises use Virtuoso‑Large as the \"fallback\" brain in Conductor pipelines when other SLMs flag low confidence. Despite its size, aggressive KV‑cache optimizations keep first‑token latency in the low‑second range on 8× H100 nodes, making it a practical production‑grade powerhouse.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "arcee-ai/coder-large": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 0.7999999999999999, "description": "Coder‑Large is a 32 B‑parameter offspring of Qwen 2.5‑Instruct that has been further trained on permissively‑licensed GitHub, CodeSearchNet and synthetic bug‑fix corpora. It supports a 32k context window, enabling multi‑file refactoring or long diff review in a single call, and understands 30‑plus programming languages with special attention to TypeScript, Go and Terraform. Internal benchmarks show 5–8 pt gains over CodeLlama‑34 B‑Python on HumanEval and competitive BugFix scores thanks to a reinforcement pass that rewards compilable output. The model emits structured explanations alongside code blocks by default, making it suitable for educational tooling as well as production copilot scenarios. Cost‑wise, Together AI prices it well below proprietary incumbents, so teams can scale interactive coding without runaway spend. ", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/phi-4-reasoning-plus": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.07, "outputPrice": 0.35, "description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It uses the same dense decoder-only transformer architecture as Phi-4, but generates longer, more comprehensive outputs structured into a step-by-step reasoning trace and final answer.\n\nWhile it offers improved benchmark scores over Phi-4-reasoning across tasks like AIME, OmniMath, and HumanEvalPlus, its responses are typically ~50% longer, resulting in higher latency. Designed for English-only applications, it is well-suited for structured reasoning workflows where output quality takes priority over response speed.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "inception/mercury-coder": {"maxTokens": 32000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.25, "outputPrice": 1, "description": "Mercury Coder is the first diffusion large language model (dLLM). Applying a breakthrough discrete diffusion approach, the model runs 5-10x faster than even speed optimized models like Claude 3.5 Haiku and GPT-4o Mini while matching their performance. Mercury Coder's speed means that developers can stay in the flow while coding, enjoying rapid chat-based iteration and responsive code completion suggestions. On Copilot Arena, Mercury Coder ranks 1st in speed and ties for 2nd in quality. Read more in the [blog post here](https://www.inceptionlabs.ai/introducing-mercury).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "qwen/qwen3-4b:free": {"maxTokens": 8192, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen3-4B is a 4 billion parameter dense language model from the Qwen3 series, designed to support both general-purpose and reasoning-intensive tasks. It introduces a dual-mode architecture—thinking and non-thinking—allowing dynamic switching between high-precision logical reasoning and efficient dialogue generation. This makes it well-suited for multi-turn chat, instruction following, and complex agent workflows.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "opengvlab/internvl3-14b": {"maxTokens": 2458, "contextWindow": 12288, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.*****************, "description": "The 14b version of the InternVL3 series. An advanced multimodal large language model (MLLM) series that demonstrates superior overall performance. Compared to InternVL 2.5, InternVL3 exhibits superior multimodal perception and reasoning capabilities, while further extending its multimodal capabilities to encompass tool usage, GUI agents, industrial image analysis, 3D vision perception, and more.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-prover-v2": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 2.1799999999999997, "description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-guard-4-12b": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.049999999999999996, "outputPrice": 0.049999999999999996, "description": "Llama Guard 4 is a Llama 4 Scout-derived multimodal pretrained model, fine-tuned for content safety classification. Similar to previous versions, it can be used to classify content in both LLM inputs (prompt classification) and in LLM responses (response classification). It acts as an LLM—generating text in its output that indicates whether a given prompt or response is safe or unsafe, and if unsafe, it also lists the content categories violated.\n\nLlama Guard 4 was aligned to safeguard against the standardized MLCommons hazards taxonomy and designed to support multimodal Llama 4 capabilities. Specifically, it combines features from previous Llama Guard models, providing content moderation for English and multiple supported languages, along with enhanced capabilities to handle mixed text-and-image prompts, including multiple images. Additionally, Llama Guard 4 is integrated into the Llama Moderations API, extending robust safety classification to text and images.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen3-30b-a3b:free": {"maxTokens": 8192, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-30b-a3b": {"maxTokens": 40960, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.08, "outputPrice": 0.29, "description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-8b:free": {"maxTokens": 40960, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-8b": {"maxTokens": 20000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.035, "outputPrice": 0.13799999999999998, "description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-14b:free": {"maxTokens": 8192, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-14b": {"maxTokens": 8192, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.06, "outputPrice": 0.24, "description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-32b": {"maxTokens": 8192, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.027, "outputPrice": 0.027, "description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-235b-a22b:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwen3-235b-a22b": {"maxTokens": 40960, "contextWindow": 40960, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.13, "outputPrice": 0.6, "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "tngtech/deepseek-r1t-chimera:free": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek-R1T-Chimera is created by merging DeepSeek-R1 and DeepSeek-V3 (0324), combining the reasoning capabilities of R1 with the token efficiency improvements of V3. It is based on a DeepSeek-MoE Transformer architecture and is optimized for general text generation tasks.\n\nThe model merges pretrained weights from both source models to balance performance across reasoning, efficiency, and instruction-following tasks. It is released under the MIT license and intended for research and commercial use.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "microsoft/mai-ds-r1:free": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "MAI-DS-R1 is a post-trained variant of DeepSeek-R1 developed by the Microsoft AI team to improve the model’s responsiveness on previously blocked topics while enhancing its safety profile. Built on top of DeepSeek-R1’s reasoning foundation, it integrates 110k examples from the Tulu-3 SFT dataset and 350k internally curated multilingual safety-alignment samples. The model retains strong reasoning, coding, and problem-solving capabilities, while unblocking a wide range of prompts previously restricted in R1.\n\nMAI-DS-R1 demonstrates improved performance on harm mitigation benchmarks and maintains competitive results across general reasoning tasks. It surpasses R1-1776 in satisfaction metrics for blocked queries and reduces leakage in harmful content categories. The model is based on a transformer MoE architecture and is suitable for general-purpose use cases, excluding high-stakes domains such as legal, medical, or autonomous systems.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "microsoft/mai-ds-r1": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.302, "outputPrice": 0.302, "description": "MAI-DS-R1 is a post-trained variant of DeepSeek-R1 developed by the Microsoft AI team to improve the model’s responsiveness on previously blocked topics while enhancing its safety profile. Built on top of DeepSeek-R1’s reasoning foundation, it integrates 110k examples from the Tulu-3 SFT dataset and 350k internally curated multilingual safety-alignment samples. The model retains strong reasoning, coding, and problem-solving capabilities, while unblocking a wide range of prompts previously restricted in R1.\n\nMAI-DS-R1 demonstrates improved performance on harm mitigation benchmarks and maintains competitive results across general reasoning tasks. It surpasses R1-1776 in satisfaction metrics for blocked queries and reduces leakage in harmful content categories. The model is based on a transformer MoE architecture and is suitable for general-purpose use cases, excluding high-stakes domains such as legal, medical, or autonomous systems.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "thudm/glm-z1-32b:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific and general pairwise preference-based—to improve performance on complex multi-step tasks. Compared to the base GLM-4-32B model, Z1 significantly boosts capabilities in structured reasoning and formal domains.\n\nThe model supports enforced “thinking” steps via prompt engineering and offers improved coherence for long-form outputs. It’s optimized for use in agentic workflows, and includes support for long context (via YaRN), JSON tool calling, and fine-grained sampling configuration for stable inference. Ideal for use cases requiring deliberate, multi-step reasoning or formal derivations.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "thudm/glm-z1-32b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.03, "description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific and general pairwise preference-based—to improve performance on complex multi-step tasks. Compared to the base GLM-4-32B model, Z1 significantly boosts capabilities in structured reasoning and formal domains.\n\nThe model supports enforced “thinking” steps via prompt engineering and offers improved coherence for long-form outputs. It’s optimized for use in agentic workflows, and includes support for long context (via YaRN), JSON tool calling, and fine-grained sampling configuration for stable inference. Ideal for use cases requiring deliberate, multi-step reasoning or formal derivations.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "thudm/glm-4-32b:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-heavy data, it was further refined using human preference alignment, rejection sampling, and reinforcement learning. The model excels in complex reasoning, artifact generation, and structured output tasks, achieving performance comparable to GPT-4o and DeepSeek-V3-0324 across several benchmarks.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thudm/glm-4-32b": {"maxTokens": 32000, "contextWindow": 32000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.24, "outputPrice": 0.24, "description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-heavy data, it was further refined using human preference alignment, rejection sampling, and reinforcement learning. The model excels in complex reasoning, artifact generation, and structured output tasks, achieving performance comparable to GPT-4o and DeepSeek-V3-0324 across several benchmarks.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/o4-mini-high": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheReadsPrice": 0.275, "description": "OpenAI o4-mini-high is the same model as [o4-mini](/openai/o4-mini) with reasoning_effort set to high. \n\nOpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "openai/o3": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "cacheReadsPrice": 0.5, "description": "o3 is a well-rounded and powerful model across domains. It sets a new standard for math, science, coding, and visual reasoning tasks. It also excels at technical writing and instruction-following. Use it to think through multi-step problems that involve analysis across text, code, and images. Note that BYOK is required for this model. Set up here: https://openrouter.ai/settings/integrations", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "openai/o4-mini": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheReadsPrice": 0.275, "description": "OpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "shisa-ai/shisa-v2-llama3.3-70b:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Shisa V2 Llama 3.3 70B is a bilingual Japanese-English chat model fine-tuned by Shisa.AI on Meta’s Llama-3.3-70B-Instruct base. It prioritizes Japanese language performance while retaining strong English capabilities. The model was optimized entirely through post-training, using a refined mix of supervised fine-tuning (SFT) and DPO datasets including regenerated ShareGPT-style data, translation tasks, roleplaying conversations, and instruction-following prompts. Unlike earlier Shisa releases, this version avoids tokenizer modifications or extended pretraining.\n\nShisa V2 70B achieves leading Japanese task performance across a wide range of custom and public benchmarks, including JA MT Bench, ELYZA 100, and Rakuda. It supports a 128K token context length and integrates smoothly with inference frameworks like vLLM and SGLang. While it inherits safety characteristics from its base model, no additional alignment was applied. The model is intended for high-performance bilingual chat, instruction following, and translation tasks across JA/EN.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "shisa-ai/shisa-v2-llama3.3-70b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.03, "description": "Shisa V2 Llama 3.3 70B is a bilingual Japanese-English chat model fine-tuned by Shisa.AI on Meta’s Llama-3.3-70B-Instruct base. It prioritizes Japanese language performance while retaining strong English capabilities. The model was optimized entirely through post-training, using a refined mix of supervised fine-tuning (SFT) and DPO datasets including regenerated ShareGPT-style data, translation tasks, roleplaying conversations, and instruction-following prompts. Unlike earlier Shisa releases, this version avoids tokenizer modifications or extended pretraining.\n\nShisa V2 70B achieves leading Japanese task performance across a wide range of custom and public benchmarks, including JA MT Bench, ELYZA 100, and Rakuda. It supports a 128K token context length and integrates smoothly with inference frameworks like vLLM and SGLang. While it inherits safety characteristics from its base model, no additional alignment was applied. The model is intended for high-performance bilingual chat, instruction following, and translation tasks across JA/EN.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4.1": {"maxTokens": 32768, "contextWindow": 1047576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "cacheReadsPrice": 0.5, "description": "GPT-4.1 is a flagship large language model optimized for advanced instruction following, real-world software engineering, and long-context reasoning. It supports a 1 million token context window and outperforms GPT-4o and GPT-4.5 across coding (54.6% SWE-bench Verified), instruction compliance (87.4% IFEval), and multimodal understanding benchmarks. It is tuned for precise code diffs, agent reliability, and high recall in large document contexts, making it ideal for agents, IDE tooling, and enterprise knowledge retrieval.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4.1-mini": {"maxTokens": 32768, "contextWindow": 1047576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 1.5999999999999999, "cacheReadsPrice": 0.09999999999999999, "description": "GPT-4.1 Mini is a mid-sized model delivering performance competitive with GPT-4o at substantially lower latency and cost. It retains a 1 million token context window and scores 45.1% on hard instruction evals, 35.8% on MultiChallenge, and 84.1% on IFEval. Mini also shows strong coding ability (e.g., 31.6% on Aid<PERSON>’s polyglot diff benchmark) and vision understanding, making it suitable for interactive applications with tight performance constraints.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4.1-nano": {"maxTokens": 32768, "contextWindow": 1047576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.*****************, "cacheReadsPrice": 0.024999999999999998, "description": "For tasks that demand low latency, GPT‑4.1 nano is the fastest and cheapest model in the GPT-4.1 series. It delivers exceptional performance at a small size with its 1 million token context window, and scores 80.1% on MMLU, 50.3% on GPQA, and 9.8% on Aider polyglot coding – even higher than GPT‑4o mini. It’s ideal for tasks like classification or autocompletion.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "eleutherai/llemma_7b": {"maxTokens": 4096, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 1.2, "description": "Llemma 7B is a language model for mathematics. It was initialized with Code Llama 7B weights, and trained on the Proof-Pile-2 for 200B tokens. Llemma models are particularly strong at chain-of-thought mathematical reasoning and using computational tools for mathematics, such as Python and formal theorem provers.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "alfredpros/codellama-7b-instruct-solidity": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.6, "outputPrice": 1, "description": "A finetuned 7 billion parameters Code LLaMA - Instruct model to generate Solidity smart contract using 4-bit QLoRA finetuning provided by PEFT library.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "arliai/qwq-32b-arliai-rpr-v1:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "QwQ-32B-ArliAI-RpR-v1 is a 32B parameter model fine-tuned from Qwen/QwQ-32B using a curated creative writing and roleplay dataset originally developed for the RPMax series. It is designed to maintain coherence and reasoning across long multi-turn conversations by introducing explicit reasoning steps per dialogue turn, generated and refined using the base model itself.\n\nThe model was trained using RS-QLORA+ on 8K sequence lengths and supports up to 128K context windows (with practical performance around 32K). It is optimized for creative roleplay and dialogue generation, with an emphasis on minimizing cross-context repetition while preserving stylistic diversity.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "arliai/qwq-32b-arliai-rpr-v1": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.015, "outputPrice": 0.015, "description": "QwQ-32B-ArliAI-RpR-v1 is a 32B parameter model fine-tuned from Qwen/QwQ-32B using a curated creative writing and roleplay dataset originally developed for the RPMax series. It is designed to maintain coherence and reasoning across long multi-turn conversations by introducing explicit reasoning steps per dialogue turn, generated and refined using the base model itself.\n\nThe model was trained using RS-QLORA+ on 8K sequence lengths and supports up to 128K context windows (with practical performance around 32K). It is optimized for creative roleplay and dialogue generation, with an emphasis on minimizing cross-context repetition while preserving stylistic diversity.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "agentica-org/deepcoder-14b-preview:free": {"maxTokens": 19200, "contextWindow": 96000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepCoder-14B-Preview is a 14B parameter code generation model fine-tuned from DeepSeek-R1-Distill-Qwen-14B using reinforcement learning with GRPO+ and iterative context lengthening. It is optimized for long-context program synthesis and achieves strong performance across coding benchmarks, including 60.6% on LiveCodeBench v5, competitive with models like o3-Mini", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "agentica-org/deepcoder-14b-preview": {"maxTokens": 19200, "contextWindow": 96000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.015, "outputPrice": 0.015, "description": "DeepCoder-14B-Preview is a 14B parameter code generation model fine-tuned from DeepSeek-R1-Distill-Qwen-14B using reinforcement learning with GRPO+ and iterative context lengthening. It is optimized for long-context program synthesis and achieves strong performance across coding benchmarks, including 60.6% on LiveCodeBench v5, competitive with models like o3-Mini", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "moonshotai/kimi-vl-a3b-thinking:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Kimi-VL is a lightweight Mixture-of-Experts vision-language model that activates only 2.8B parameters per step while delivering strong performance on multimodal reasoning and long-context tasks. The Kimi-VL-A3B-Thinking variant, fine-tuned with chain-of-thought and reinforcement learning, excels in math and visual reasoning benchmarks like MathVision, MMMU, and MathVista, rivaling much larger models such as Qwen2.5-VL-7B and Gemma-3-12B. It supports 128K context and high-resolution input via its MoonViT encoder.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "moonshotai/kimi-vl-a3b-thinking": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.038000000000000006, "outputPrice": 0.038000000000000006, "description": "Kimi-VL is a lightweight Mixture-of-Experts vision-language model that activates only 2.8B parameters per step while delivering strong performance on multimodal reasoning and long-context tasks. The Kimi-VL-A3B-Thinking variant, fine-tuned with chain-of-thought and reinforcement learning, excels in math and visual reasoning benchmarks like MathVision, MMMU, and MathVista, rivaling much larger models such as Qwen2.5-VL-7B and Gemma-3-12B. It supports 128K context and high-resolution input via its MoonViT encoder.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "x-ai/grok-3-mini-beta": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 0.5, "cacheReadsPrice": 0.075, "description": "Grok 3 Mini is a lightweight, smaller thinking model. Unlike traditional models that generate answers immediately, Grok 3 Mini thinks before responding. It’s ideal for reasoning-heavy tasks that don’t demand extensive domain knowledge, and shines in math-specific and quantitative use cases, such as solving challenging puzzles or math problems.\n\nTransparent \"thinking\" traces accessible. Defaults to low reasoning, can boost with setting `reasoning: { effort: \"high\" }`\n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "x-ai/grok-3-beta": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 15, "cacheReadsPrice": 0.75, "description": "Grok 3 is the latest model from xAI. It's their flagship model that excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.\n\nExcels in structured tasks and benchmarks like GPQA, LCB, and MMLU-Pro where it outperforms Grok 3 Mini even on high thinking. \n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nvidia/llama-3.3-nemotron-super-49b-v1": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.13, "outputPrice": 0.*****************, "description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta's Llama-3.3-70B-Instruct, it employs a Neural Architecture Search (NAS) approach, significantly enhancing efficiency and reducing memory requirements. This allows the model to support a context length of up to 128K tokens and fit efficiently on single high-performance GPUs, such as NVIDIA H200.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nvidia/llama-3.1-nemotron-ultra-253b-v1:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Llama-3.1-Nemotron-Ultra-253B-v1 is a large language model (LLM) optimized for advanced reasoning, human-interactive chat, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta’s Llama-3.1-405B-Instruct, it has been significantly customized using Neural Architecture Search (NAS), resulting in enhanced efficiency, reduced memory usage, and improved inference latency. The model supports a context length of up to 128K tokens and can operate efficiently on an 8x NVIDIA H100 node.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nvidia/llama-3.1-nemotron-ultra-253b-v1": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.6, "outputPrice": 1.7999999999999998, "description": "Llama-3.1-Nemotron-Ultra-253B-v1 is a large language model (LLM) optimized for advanced reasoning, human-interactive chat, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta’s Llama-3.1-405B-Instruct, it has been significantly customized using Neural Architecture Search (NAS), resulting in enhanced efficiency, reduced memory usage, and improved inference latency. The model supports a context length of up to 128K tokens and can operate efficiently on an 8x NVIDIA H100 node.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "meta-llama/llama-4-maverick": {"maxTokens": 16384, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.6, "description": "Llama 4 Maverick 17B Instruct (128E) is a high-capacity multimodal language model from Meta, built on a mixture-of-experts (MoE) architecture with 128 experts and 17 billion active parameters per forward pass (400B total). It supports multilingual text and image input, and produces multilingual text and code output across 12 supported languages. Optimized for vision-language tasks, Maverick is instruction-tuned for assistant-like behavior, image reasoning, and general-purpose multimodal interaction.\n\nMaverick features early fusion for native multimodality and a 1 million token context window. It was trained on a curated mixture of public, licensed, and Meta-platform data, covering ~22 trillion tokens, with a knowledge cutoff in August 2024. Released on April 5, 2025 under the Llama 4 Community License, Maverick is suited for research and commercial applications requiring advanced multimodal understanding and high model throughput.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-4-scout": {"maxTokens": 1048576, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.08, "outputPrice": 0.3, "description": "Llama 4 Scout 17B Instruct (16E) is a mixture-of-experts (MoE) language model developed by Meta, activating 17 billion parameters out of a total of 109B. It supports native multimodal input (text and image) and multilingual output (text and code) across 12 supported languages. Designed for assistant-style interaction and visual reasoning, <PERSON> uses 16 experts per forward pass and features a context length of 10 million tokens, with a training corpus of ~40 trillion tokens.\n\nBuilt for high efficiency and local or commercial deployment, Llama 4 Scout incorporates early fusion for seamless modality integration. It is instruction-tuned for use in multilingual chat, captioning, and image understanding tasks. Released under the Llama 4 Community License, it was last trained on data up to August 2024 and launched publicly on April 5, 2025.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-v3-base": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.302, "outputPrice": 0.302, "description": "Note that this is a base model mostly meant for testing, you need to provide detailed prompts for the model to return useful responses. \n\nDeepSeek-V3 Base is a 671B parameter open Mixture-of-Experts (MoE) language model with 37B active parameters per forward pass and a context length of 128K tokens. Trained on 14.8T tokens using FP8 mixed precision, it achieves high training efficiency and stability, with strong performance across language, reasoning, math, and coding tasks. \n\nDeepSeek-V3 Base is the pre-trained model behind [DeepSeek V3](/deepseek/deepseek-chat-v3)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "scb10x/llama3.1-typhoon2-70b-instruct": {"maxTokens": 1639, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.88, "outputPrice": 0.88, "description": "Llama3.1-Typhoon2-70B-Instruct is a Thai-English instruction-tuned language model with 70 billion parameters, built on Llama 3.1. It demonstrates strong performance across general instruction-following, math, coding, and tool-use tasks, with state-of-the-art results in Thai-specific benchmarks such as IFEval, MT-Bench, and Thai-English code-switching.\n\nThe model excels in bilingual reasoning and function-calling scenarios, offering high accuracy across diverse domains. Comparative evaluations show consistent improvements over prior Thai LLMs and other Llama-based baselines. Full results and methodology are available in the [technical report.](https://arxiv.org/abs/2412.13702)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-2.5-pro-exp-03-25": {"maxTokens": 65535, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "This model has been deprecated by Google in favor of the (paid Preview model)[google/gemini-2.5-pro-preview]\n \nGemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen2.5-vl-32b-instruct:free": {"maxTokens": 1639, "contextWindow": 8192, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen2.5-vl-32b-instruct": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.6, "description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-chat-v3-0324:free": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-chat-v3-0324": {"maxTokens": 163840, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.25, "outputPrice": 0.85, "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "featherless/qwerky-72b:free": {"maxTokens": 4096, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qrwkv-72B is a linear-attention RWKV variant of the Qwen 2.5 72B model, optimized to significantly reduce computational cost at scale. Leveraging linear attention, it achieves substantial inference speedups (>1000x) while retaining competitive accuracy on common benchmarks like ARC, HellaSwag, Lambada, and MMLU. It inherits knowledge and language support from Qwen 2.5, supporting approximately 30 languages, making it suitable for efficient inference in large-context applications.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/o1-pro": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 150, "outputPrice": 600, "description": "The o1 series of models are trained with reinforcement learning to think before they answer and perform complex reasoning. The o1-pro model uses more compute to think harder and provide consistently better answers.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "mistralai/mistral-small-3.1-24b-instruct:free": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Mistral Small 3.1 24B Instruct is an upgraded variant of Mistral Small 3 (2501), featuring 24 billion parameters with advanced multimodal capabilities. It provides state-of-the-art performance in text-based reasoning and vision tasks, including image analysis, programming, mathematical reasoning, and multilingual support across dozens of languages. Equipped with an extensive 128k token context window and optimized for efficient local inference, it supports use cases such as conversational agents, function calling, long-document comprehension, and privacy-sensitive deployments. The updated version is [Mistral Small 3.2](mistralai/mistral-small-3.2-24b-instruct)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-small-3.1-24b-instruct": {"maxTokens": 96000, "contextWindow": 96000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.027, "outputPrice": 0.027, "description": "Mistral Small 3.1 24B Instruct is an upgraded variant of Mistral Small 3 (2501), featuring 24 billion parameters with advanced multimodal capabilities. It provides state-of-the-art performance in text-based reasoning and vision tasks, including image analysis, programming, mathematical reasoning, and multilingual support across dozens of languages. Equipped with an extensive 128k token context window and optimized for efficient local inference, it supports use cases such as conversational agents, function calling, long-document comprehension, and privacy-sensitive deployments. The updated version is [Mistral Small 3.2](mistralai/mistral-small-3.2-24b-instruct)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3-4b-it:free": {"maxTokens": 8192, "contextWindow": 32768, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3-4b-it": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.02, "outputPrice": 0.04, "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "ai21/jamba-1.6-large": {"maxTokens": 4096, "contextWindow": 256000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "description": "AI21 Jamba Large 1.6 is a high-performance hybrid foundation model combining State Space Models (Mamba) with Transformer attention mechanisms. Developed by AI21, it excels in extremely long-context handling (256K tokens), demonstrates superior inference efficiency (up to 2.5x faster than comparable models), and supports structured JSON output and tool-use capabilities. It has 94 billion active parameters (398 billion total), optimized quantization support (ExpertsInt8), and multilingual proficiency in languages such as English, Spanish, French, Portuguese, Italian, Dutch, German, Arabic, and Hebrew.\n\nUsage of this model is subject to the [Jamba Open Model License](https://www.ai21.com/licenses/jamba-open-model-license).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "ai21/jamba-1.6-mini": {"maxTokens": 4096, "contextWindow": 256000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.*****************, "description": "AI21 Jamba Mini 1.6 is a hybrid foundation model combining State Space Models (Mamba) with Transformer attention mechanisms. With 12 billion active parameters (52 billion total), this model excels in extremely long-context tasks (up to 256K tokens) and achieves superior inference efficiency, outperforming comparable open models on tasks such as retrieval-augmented generation (RAG) and grounded question answering. Jamba Mini 1.6 supports multilingual tasks across English, Spanish, French, Portuguese, Italian, Dutch, German, Arabic, and Hebrew, along with structured JSON output and tool-use capabilities.\n\nUsage of this model is subject to the [Jamba Open Model License](https://www.ai21.com/licenses/jamba-open-model-license).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3-12b-it:free": {"maxTokens": 8192, "contextWindow": 96000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 12B is the second largest in the family of Gemma 3 models after [Gemma 3 27B](google/gemma-3-27b-it)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3-12b-it": {"maxTokens": 8192, "contextWindow": 96000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.03, "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 12B is the second largest in the family of Gemma 3 models after [Gemma 3 27B](google/gemma-3-27b-it)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-a": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "description": "Command A is an open-weights 111B parameter model with a 256k context window focused on delivering great performance across agentic, multilingual, and coding use cases.\nCompared to other leading proprietary and open-weights models Command A delivers maximum performance with minimum hardware costs, excelling on business-critical agentic and multilingual tasks.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o-mini-search-preview": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.6, "description": "GPT-4o mini Search Preview is a specialized model for web search in Chat Completions. It is trained to understand and execute web search queries.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "openai/gpt-4o-search-preview": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "description": "GPT-4o Search Previewis a specialized model for web search in Chat Completions. It is trained to understand and execute web search queries.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "rekaai/reka-flash-3:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Reka Flash 3 is a general-purpose, instruction-tuned large language model with 21 billion parameters, developed by Reka. It excels at general chat, coding tasks, instruction-following, and function calling. Featuring a 32K context length and optimized through reinforcement learning (RLOO), it provides competitive performance comparable to proprietary models within a smaller parameter footprint. Ideal for low-latency, local, or on-device deployments, Reka Flash 3 is compact, supports efficient quantization (down to 11GB at 4-bit precision), and employs explicit reasoning tags (\"<reasoning>\") to indicate its internal thought process.\n\nReka Flash 3 is primarily an English model with limited multilingual understanding capabilities. The model weights are released under the Apache 2.0 license.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "google/gemma-3-27b-it:free": {"maxTokens": 8192, "contextWindow": 96000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 27B is Google's latest open source model, successor to [Gemma 2](google/gemma-2-27b-it)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-3-27b-it": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.09, "outputPrice": 0.16999999999999998, "description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 27B is Google's latest open source model, successor to [Gemma 2](google/gemma-2-27b-it)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thedrummer/anubis-pro-105b-v1": {"maxTokens": 131072, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 1, "description": "Anubis Pro 105B v1 is an expanded and refined variant of Meta’s Llama 3.3 70B, featuring 50% additional layers and further fine-tuning to leverage its increased capacity. Designed for advanced narrative, roleplay, and instructional tasks, it demonstrates enhanced emotional intelligence, creativity, nuanced character portrayal, and superior prompt adherence compared to smaller models. Its larger parameter count allows for deeper contextual understanding and extended reasoning capabilities, optimized for engaging, intelligent, and coherent interactions.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thedrummer/skyfall-36b-v2": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.0728, "outputPrice": 0.0728, "description": "Skyfall 36B v2 is an enhanced iteration of Mistral Small 2501, specifically fine-tuned for improved creativity, nuanced writing, role-playing, and coherent storytelling.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/phi-4-multimodal-instruct": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.049999999999999996, "outputPrice": 0.09999999999999999, "description": "Phi-4 Multimodal Instruct is a versatile 5.6B parameter foundation model that combines advanced reasoning and instruction-following capabilities across both text and visual inputs, providing accurate text outputs. The unified architecture enables efficient, low-latency inference, suitable for edge and mobile deployments. Phi-4 Multimodal Instruct supports text inputs in multiple languages including Arabic, Chinese, English, French, German, Japanese, Spanish, and more, with visual input optimized primarily for English. It delivers impressive performance on multimodal tasks involving mathematical, scientific, and document reasoning, providing developers and enterprises a powerful yet compact model for sophisticated interactive applications. For more information, see the [Phi-4 Multimodal blog post](https://azure.microsoft.com/en-us/blog/empowering-innovation-the-next-generation-of-the-phi-family/).\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "perplexity/sonar-reasoning-pro": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nSonar Reasoning Pro is a premier reasoning model powered by DeepSeek R1 with Chain of Thought (CoT). Designed for advanced use cases, it supports in-depth, multi-step queries with a larger context window and can surface more citations per search, enabling more comprehensive and extensible responses.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "perplexity/sonar-pro": {"maxTokens": 8000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 15, "description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nFor enterprises seeking more advanced capabilities, the Sonar Pro API can handle in-depth, multi-step queries with added extensibility, like double the number of citations per search as Sonar on average. Plus, with a larger context window, it can handle longer and more nuanced searches and follow-up questions. ", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "perplexity/sonar-deep-research": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "description": "Sonar Deep Research is a research-focused model designed for multi-step retrieval, synthesis, and reasoning across complex topics. It autonomously searches, reads, and evaluates sources, refining its approach as it gathers information. This enables comprehensive report generation across domains like finance, technology, health, and current events.\n\nNotes on Pricing ([Source](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-deep-research)) \n- Input tokens comprise of Prompt tokens (user prompt) + Citation tokens (these are processed tokens from running searches)\n- Deep Research runs multiple searches to conduct exhaustive research. Searches are priced at $5/1000 searches. A request that does 30 searches will cost $0.15 in this step.\n- Reasoning is a distinct step in Deep Research since it does extensive automated reasoning through all the material it gathers during its research phase. Reasoning tokens here are a bit different than the CoTs in the answer - these are tokens that we use to reason through the research material prior to generating the outputs via the CoTs. Reasoning tokens are priced at $3/1M tokens", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "qwen/qwq-32b:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in downstream tasks, especially hard problems. QwQ-32B is the medium-sized reasoning model, which is capable of achieving competitive performance against state-of-the-art reasoning models, e.g., DeepSeek-R1, o1-mini.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwq-32b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.075, "outputPrice": 0.15, "description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in downstream tasks, especially hard problems. QwQ-32B is the medium-sized reasoning model, which is capable of achieving competitive performance against state-of-the-art reasoning models, e.g., DeepSeek-R1, o1-mini.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "nousresearch/deephermes-3-llama-3-8b-preview:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepHermes 3 Preview is the latest version of our flagship Hermes series of LLMs by Nous Research, and one of the first models in the world to unify Reasoning (long chains of thought that improve answer accuracy) and normal LLM response modes into one model. We have also improved LLM annotation, judgement, and function calling.\n\nDeepHermes 3 Preview is one of the first LLM models to unify both \"intuitive\", traditional mode responses and long chain of thought reasoning responses into a single model, toggled by a system prompt.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-2.0-flash-lite-001": {"maxTokens": 8192, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.075, "outputPrice": 0.3, "description": "Gemini 2.0 Flash Lite offers a significantly faster time to first token (TTFT) compared to [Gemini Flash 1.5](/google/gemini-flash-1.5), while maintaining quality on par with larger models like [Gemini Pro 1.5](/google/gemini-pro-1.5), all at extremely economical token prices.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.7-sonnet": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsComputerUse": true, "supportsReasoningBudget": false}, "anthropic/claude-3.7-sonnet:thinking": {"maxTokens": 128000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsComputerUse": true, "supportsReasoningBudget": true, "requiredReasoningBudget": true}, "anthropic/claude-3.7-sonnet:beta": {"maxTokens": 128000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"], "supportsComputerUse": true, "supportsReasoningBudget": true}, "perplexity/r1-1776": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 8, "description": "R1 1776 is a version of DeepSeek-R1 that has been post-trained to remove censorship constraints related to topics restricted by the Chinese government. The model retains its original reasoning capabilities while providing direct responses to a wider range of queries. R1 1776 is an offline chat model that does not use the perplexity search subsystem.\n\nThe model was tested on a multilingual dataset of over 1,000 examples covering sensitive topics to measure its likelihood of refusal or overly filtered responses. [Evaluation Results](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/GiN2VqC5hawUgAGJ6oHla.png) Its performance on math and reasoning benchmarks remains similar to the base R1 model. [Reasoning Performance](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/n4Z9Byqp2S7sKUvCvI40R.png)\n\nRead more on the [Blog Post](https://perplexity.ai/hub/blog/open-sourcing-r1-1776)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "mistralai/mistral-saba": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.6, "description": "Mistral Saba is a 24B-parameter language model specifically designed for the Middle East and South Asia, delivering accurate and contextually relevant responses while maintaining efficient performance. Trained on curated regional datasets, it supports multiple Indian-origin languages—including Tamil and Malayalam—alongside Arabic. This makes it a versatile option for a range of regional and multilingual applications. Read more at the blog post [here](https://mistral.ai/en/news/mistral-saba)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cognitivecomputations/dolphin3.0-r1-mistral-24b:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Dolphin 3.0 R1 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases.\n\nThe R1 version has been trained for 3 epochs to reason using 800k reasoning traces from the Dolphin-R1 dataset.\n\nDolphin aims to be a general purpose reasoning instruct model, similar to the models behind <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>.\n\nPart of the [Dolphin 3.0 Collection](https://huggingface.co/collections/cognitivecomputations/dolphin-30-677ab47f73d7ff66743979a3) Curated and trained by [<PERSON>](https://huggingface.co/ehartford), [<PERSON>](https://huggingface.co/bigstorm), [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://huggingface.co/BlouseJury) and [Cognitive Computations](https://huggingface.co/cognitivecomputations)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "cognitivecomputations/dolphin3.0-r1-mistral-24b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.013000000000000001, "outputPrice": 0.013000000000000001, "description": "Dolphin 3.0 R1 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases.\n\nThe R1 version has been trained for 3 epochs to reason using 800k reasoning traces from the Dolphin-R1 dataset.\n\nDolphin aims to be a general purpose reasoning instruct model, similar to the models behind <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>.\n\nPart of the [Dolphin 3.0 Collection](https://huggingface.co/collections/cognitivecomputations/dolphin-30-677ab47f73d7ff66743979a3) Curated and trained by [<PERSON>](https://huggingface.co/ehartford), [<PERSON>](https://huggingface.co/bigstorm), [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://huggingface.co/BlouseJury) and [Cognitive Computations](https://huggingface.co/cognitivecomputations)", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "cognitivecomputations/dolphin3.0-mistral-24b:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Dolphin 3.0 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases.\n\nDolphin aims to be a general purpose instruct model, similar to the models behind <PERSON><PERSON>GP<PERSON>, <PERSON>, <PERSON>. \n\nPart of the [Dolphin 3.0 Collection](https://huggingface.co/collections/cognitivecomputations/dolphin-30-677ab47f73d7ff66743979a3) Curated and trained by [<PERSON>](https://huggingface.co/ehartford), [<PERSON>](https://huggingface.co/bigstorm), [BlouseJury](https://huggingface.co/BlouseJury) and [Cognitive Computations](https://huggingface.co/cognitivecomputations)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-guard-3-8b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.02, "outputPrice": 0.06, "description": "Llama Guard 3 is a Llama-3.1-8B pretrained model, fine-tuned for content safety classification. Similar to previous versions, it can be used to classify content in both LLM inputs (prompt classification) and in LLM responses (response classification). It acts as an LLM – it generates text in its output that indicates whether a given prompt or response is safe or unsafe, and if unsafe, it also lists the content categories violated.\n\nLlama Guard 3 was aligned to safeguard against the MLCommons standardized hazards taxonomy and designed to support Llama 3.1 capabilities. Specifically, it provides content moderation in 8 languages, and was optimized to support safety and security for search and code interpreter tool calls.\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/o3-mini-high": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheReadsPrice": 0.55, "description": "OpenAI o3-mini-high is the same model as [o3-mini](/openai/o3-mini) with reasoning_effort set to high. \n\no3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding. The model features three adjustable reasoning effort levels and supports key developer capabilities including function calling, structured outputs, and streaming, though it does not include vision processing capabilities.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "deepseek/deepseek-r1-distill-llama-8b": {"maxTokens": 32000, "contextWindow": 32000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.04, "outputPrice": 0.04, "description": "DeepSeek R1 Distill Llama 8B is a distilled large language model based on [Llama-3.1-8B-Instruct](/meta-llama/llama-3.1-8b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 50.4\n- MATH-500 pass@1: 89.1\n- CodeForces Rating: 1205\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.\n\nHugging Face: \n- [Llama-3.1-8B](https://huggingface.co/meta-llama/Llama-3.1-8B) \n- [DeepSeek-R1-Distill-Llama-8B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-8B)   |", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "google/gemini-2.0-flash-001": {"maxTokens": 8192, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.09999999999999999, "outputPrice": 0.*****************, "cacheWritesPrice": 0.18330000000000002, "cacheReadsPrice": 0.024999999999999998, "description": "Gemini Flash 2.0 offers a significantly faster time to first token (TTFT) compared to [Gemini Flash 1.5](/google/gemini-flash-1.5), while maintaining quality on par with larger models like [Gemini Pro 1.5](/google/gemini-pro-1.5). It introduces notable enhancements in multimodal understanding, coding capabilities, complex instruction following, and function calling. These advancements come together to deliver more seamless and robust agentic experiences.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-vl-plus": {"maxTokens": 1500, "contextWindow": 7500, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.21, "outputPrice": 0.63, "description": "Qwen's Enhanced Large Visual Language Model. Significantly upgraded for detailed recognition capabilities and text recognition abilities, supporting ultra-high pixel resolutions up to millions of pixels and extreme aspect ratios for image input. It delivers significant performance across a broad range of visual tasks.\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "aion-labs/aion-1.0": {"maxTokens": 32768, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 4, "outputPrice": 8, "description": "Aion-1.0 is a multi-model system designed for high performance across various tasks, including reasoning and coding. It is built on DeepSeek-R1, augmented with additional models and techniques such as Tree of Thoughts (ToT) and Mixture of Experts (MoE). It is Aion Lab's most powerful reasoning model.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "aion-labs/aion-1.0-mini": {"maxTokens": 32768, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7, "outputPrice": 1.4, "description": "Aion-1.0-Mini 32B parameter model is a distilled version of the DeepSeek-R1 model, designed for strong performance in reasoning domains such as mathematics, coding, and logic. It is a modified variant of a FuseAI model that outperforms R1-Distill-Qwen-32B and R1-Distill-Llama-70B, with benchmark results available on its [Hugging Face page](https://huggingface.co/FuseAI/FuseO1-DeepSeekR1-QwQ-SkyT1-32B-Preview), independently replicated for verification.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "aion-labs/aion-rp-llama-3.1-8b": {"maxTokens": 32768, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.19999999999999998, "description": "Aion-RP-Llama-3.1-8B ranks the highest in the character evaluation portion of the RPBench-Auto benchmark, a roleplaying-specific variant of Arena-Hard-Auto, where LLMs evaluate each other’s responses. It is a fine-tuned base model rather than an instruct model, designed to produce more natural and varied writing.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-vl-max": {"maxTokens": 1500, "contextWindow": 7500, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 3.1999999999999997, "description": "Qwen VL Max is a visual understanding model with 7500 tokens context length. It excels in delivering optimal performance for a broader spectrum of complex tasks.\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-turbo": {"maxTokens": 8192, "contextWindow": 1000000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.049999999999999996, "outputPrice": 0.19999999999999998, "cacheReadsPrice": 0.02, "description": "Qwen-Turbo, based on Qwen2.5, is a 1M context model that provides fast speed and low cost, suitable for simple tasks.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen2.5-vl-72b-instruct:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen2.5-vl-72b-instruct": {"maxTokens": 6400, "contextWindow": 32000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.25, "outputPrice": 0.75, "description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-plus": {"maxTokens": 8192, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 1.2, "cacheReadsPrice": 0.16, "description": "Qwen-Plus, based on the Qwen2.5 foundation model, is a 131K context model with a balanced performance, speed, and cost combination.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-max": {"maxTokens": 8192, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.5999999999999999, "outputPrice": 6.3999999999999995, "cacheReadsPrice": 0.64, "description": "Qwen-Max, based on Qwen2.5, provides the best inference performance among [Qwen models](/qwen), especially for complex multi-step tasks. It's a large-scale MoE model that has been pretrained on over 20 trillion tokens and further post-trained with curated Supervised Fine-Tuning (SFT) and Reinforcement Learning from Human Feedback (RLHF) methodologies. The parameter count is unknown.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/o3-mini": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheReadsPrice": 0.55, "description": "OpenAI o3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding.\n\nThis model supports the `reasoning_effort` parameter, which can be set to \"high\", \"medium\", or \"low\" to control the thinking time of the model. The default is \"medium\". OpenRouter also offers the model slug `openai/o3-mini-high` to default the parameter to \"high\".\n\nThe model features three adjustable reasoning effort levels and supports key developer capabilities including function calling, structured outputs, and streaming, though it does not include vision processing capabilities.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "deepseek/deepseek-r1-distill-qwen-1.5b": {"maxTokens": 32768, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.18, "outputPrice": 0.18, "description": "DeepSeek R1 Distill Qwen 1.5B is a distilled large language model based on  [Qwen 2.5 Math 1.5B](https://huggingface.co/Qwen/Qwen2.5-Math-1.5B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It's a very small and efficient model which outperforms [GPT 4o 0513](/openai/gpt-4o-2024-05-13) on Math Benchmarks.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 28.9\n- AIME 2024 cons@64: 52.7\n- MATH-500 pass@1: 83.9\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "mistralai/mistral-small-24b-instruct-2501:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Mistral Small 3 is a 24B-parameter language model optimized for low-latency performance across common AI tasks. Released under the Apache 2.0 license, it features both pre-trained and instruction-tuned versions designed for efficient local deployment.\n\nThe model achieves 81% accuracy on the MMLU benchmark and performs competitively with larger models like Llama 3.3 70B and Qwen 32B, while operating at three times the speed on equivalent hardware. [Read the blog post about the model here.](https://mistral.ai/news/mistral-small-3/)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-small-24b-instruct-2501": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.03, "description": "Mistral Small 3 is a 24B-parameter language model optimized for low-latency performance across common AI tasks. Released under the Apache 2.0 license, it features both pre-trained and instruction-tuned versions designed for efficient local deployment.\n\nThe model achieves 81% accuracy on the MMLU benchmark and performs competitively with larger models like Llama 3.3 70B and Qwen 32B, while operating at three times the speed on equivalent hardware. [Read the blog post about the model here.](https://mistral.ai/news/mistral-small-3/)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-r1-distill-qwen-32b": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.075, "outputPrice": 0.15, "description": "DeepSeek R1 Distill Qwen 32B is a distilled large language model based on [Qwen 2.5 32B](https://huggingface.co/Qwen/Qwen2.5-32B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\\n\\nOther benchmark results include:\\n\\n- AIME 2024 pass@1: 72.6\\n- MATH-500 pass@1: 94.3\\n- CodeForces Rating: 1691\\n\\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-distill-qwen-14b:free": {"maxTokens": 12800, "contextWindow": 64000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on [Qwen 2.5 14B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 69.7\n- MATH-500 pass@1: 93.9\n- CodeForces Rating: 1481\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-distill-qwen-14b": {"maxTokens": 32000, "contextWindow": 64000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.15, "description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on [Qwen 2.5 14B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 69.7\n- MATH-500 pass@1: 93.9\n- CodeForces Rating: 1481\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "perplexity/sonar-reasoning": {"maxTokens": 25400, "contextWindow": 127000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1, "outputPrice": 5, "description": "Sonar Reasoning is a reasoning model provided by Perplexity based on [DeepSeek R1](/deepseek/deepseek-r1).\n\nIt allows developers to utilize long chain of thought with built-in web search. Sonar Reasoning is uncensored and hosted in US datacenters. ", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "perplexity/sonar": {"maxTokens": 25415, "contextWindow": 127072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 1, "outputPrice": 1, "description": "Sonar is lightweight, affordable, fast, and simple to use — now featuring citations and the ability to customize sources. It is designed for companies seeking to integrate lightweight question-and-answer features optimized for speed.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "liquid/lfm-7b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.01, "outputPrice": 0.01, "description": "LFM-7B, a new best-in-class language model. LFM-7B is designed for exceptional chat capabilities, including languages like Arabic and Japanese. Powered by the Liquid Foundation Model (LFM) architecture, it exhibits unique features like low memory footprint and fast inference speed. \n\nLFM-7B is the world’s best-in-class multilingual language model in English, Arabic, and Japanese.\n\nSee the [launch announcement](https://www.liquid.ai/lfm-7b) for benchmarks and more info.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "liquid/lfm-3b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.02, "outputPrice": 0.02, "description": "Liquid's LFM 3B delivers incredible performance for its size. It positions itself as first place among 3B parameter transformers, hybrids, and RNN models It is also on par with Phi-3.5-mini on multiple benchmarks, while being 18.4% smaller.\n\nLFM-3B is the ideal choice for mobile and other edge text-based applications.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-r1-distill-llama-70b:free": {"maxTokens": 4096, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek R1 Distill Llama 70B is a distilled large language model based on [Llama-3.3-70B-Instruct](/meta-llama/llama-3.3-70b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 70.0\n- MATH-500 pass@1: 94.5\n- CodeForces Rating: 1633\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1-distill-llama-70b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.049999999999999996, "outputPrice": 0.049999999999999996, "description": "DeepSeek R1 Distill Llama 70B is a distilled large language model based on [Llama-3.3-70B-Instruct](/meta-llama/llama-3.3-70b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 70.0\n- MATH-500 pass@1: 94.5\n- CodeForces Rating: 1633\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "deepseek/deepseek-r1:free": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "reasoning", "include_reasoning", "temperature"]}, "deepseek/deepseek-r1": {"maxTokens": 163840, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 2, "description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "supportsReasoningEffort": true, "supportedParameters": ["max_tokens", "temperature", "reasoning", "include_reasoning"]}, "minimax/minimax-01": {"maxTokens": 1000192, "contextWindow": 1000192, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 1.1, "description": "MiniMax-01 is a combines MiniMax-Text-01 for text generation and MiniMax-VL-01 for image understanding. It has 456 billion parameters, with 45.9 billion parameters activated per inference, and can handle a context of up to 4 million tokens.\n\nThe text model adopts a hybrid architecture that combines Lightning Attention, Softmax Attention, and Mixture-of-Experts (MoE). The image model adopts the “ViT-MLP-LLM” framework and is trained on top of the text model.\n\nTo read more about the release, see: https://www.minimaxi.com/en/news/minimax-01-series-2", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/codestral-2501": {"maxTokens": 52429, "contextWindow": 262144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 0.8999999999999999, "description": "[Mistral](/mistralai)'s cutting-edge language model for coding. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation. \n\nLearn more on their blog post: https://mistral.ai/news/codestral-2501/", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/phi-4": {"maxTokens": 3277, "contextWindow": 16384, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.06, "outputPrice": 0.14, "description": "[Microsoft Research](/microsoft) Phi-4 is designed to perform well in complex reasoning tasks and can operate efficiently in situations with limited memory or where quick responses are needed. \n\nAt 14 billion parameters, it was trained on a mix of high-quality synthetic datasets, data from curated websites, and academic materials. It has undergone careful improvement to follow instructions accurately and maintain strong safety standards. It works best with English language inputs.\n\nFor more information, please see [Phi-4 Technical Report](https://arxiv.org/pdf/2412.08905)\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "deepseek/deepseek-chat": {"maxTokens": 32768, "contextWindow": 163840, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.272, "outputPrice": 0.272, "description": "DeepSeek-V3 is the latest model from the DeepSeek team, building upon the instruction following and coding abilities of the previous versions. Pre-trained on nearly 15 trillion tokens, the reported evaluations reveal that the model outperforms other open-source models and rivals leading closed-source models.\n\nFor model details, please visit [the DeepSeek-V3 repo](https://github.com/deepseek-ai/DeepSeek-V3) for more information, or see the [launch announcement](https://api-docs.deepseek.com/news/news1226).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "sao10k/l3.3-euryale-70b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.65, "outputPrice": 0.75, "description": "Euryale L3.3 70B is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k). It is the successor of [Euryale L3 70B v2.2](/models/sao10k/l3-euryale-70b).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/o1": {"maxTokens": 100000, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 15, "outputPrice": 60, "cacheReadsPrice": 7.5, "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding. The o1 model series is trained with large-scale reinforcement learning to reason using chain of thought. \n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "x-ai/grok-2-vision-1212": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 10, "description": "Grok 2 Vision 1212 advances image-based AI with stronger visual comprehension, refined instruction-following, and multilingual support. From object recognition to style analysis, it empowers developers to build more intuitive, visually aware applications. Its enhanced steerability and reasoning establish a robust foundation for next-generation image solutions.\n\nTo read more about this model, check out [xAI's announcement](https://x.ai/blog/grok-1212).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "x-ai/grok-2-1212": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 10, "description": "Grok 2 1212 introduces significant enhancements to accuracy, instruction adherence, and multilingual support, making it a powerful and flexible choice for developers seeking a highly steerable, intelligent model.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r7b-12-2024": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.0375, "outputPrice": 0.15, "description": "Command R7B (12-2024) is a small, fast update of the Command R+ model, delivered in December 2024. It excels at RAG, tool use, agents, and similar tasks requiring complex reasoning and multiple steps.\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-2.0-flash-exp:free": {"maxTokens": 8192, "contextWindow": 1048576, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemini Flash 2.0 offers a significantly faster time to first token (TTFT) compared to [Gemini Flash 1.5](/google/gemini-flash-1.5), while maintaining quality on par with larger models like [Gemini Pro 1.5](/google/gemini-pro-1.5). It introduces notable enhancements in multimodal understanding, coding capabilities, complex instruction following, and function calling. These advancements come together to deliver more seamless and robust agentic experiences.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.3-70b-instruct:free": {"maxTokens": 13108, "contextWindow": 65536, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.\n\n[Model Card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_3/MODEL_CARD.md)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.3-70b-instruct": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.038000000000000006, "outputPrice": 0.12, "description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.\n\n[Model Card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_3/MODEL_CARD.md)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "amazon/nova-lite-v1": {"maxTokens": 5120, "contextWindow": 300000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.06, "outputPrice": 0.24, "description": "Amazon Nova Lite 1.0 is a very low-cost multimodal model from Amazon that focused on fast processing of image, video, and text inputs to generate text output. Amazon Nova Lite can handle real-time customer interactions, document analysis, and visual question-answering tasks with high accuracy.\n\nWith an input context of 300K tokens, it can analyze multiple images or up to 30 minutes of video in a single input.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "amazon/nova-micro-v1": {"maxTokens": 5120, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.035, "outputPrice": 0.14, "description": "Amazon Nova Micro 1.0 is a text-only model that delivers the lowest latency responses in the Amazon Nova family of models at a very low cost. With a context length of 128K tokens and optimized for speed and cost, Amazon Nova Micro excels at tasks such as text summarization, translation, content classification, interactive chat, and brainstorming. It has  simple mathematical reasoning and coding abilities.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "amazon/nova-pro-v1": {"maxTokens": 5120, "contextWindow": 300000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 3.1999999999999997, "description": "Amazon Nova Pro 1.0 is a capable multimodal model from Amazon focused on providing a combination of accuracy, speed, and cost for a wide range of tasks. As of December 2024, it achieves state-of-the-art performance on key benchmarks including visual question answering (TextVQA) and video understanding (VATEX).\n\nAmazon Nova Pro demonstrates strong capabilities in processing both visual and textual information and at analyzing financial documents.\n\n**NOTE**: Video input is not supported at this time.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwq-32b-preview": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.19999999999999998, "description": "QwQ-32B-Preview is an experimental research model focused on AI reasoning capabilities developed by the Qwen Team. As a preview release, it demonstrates promising analytical abilities while having several important limitations:\n\n1. **Language Mixing and Code-Switching**: The model may mix languages or switch between them unexpectedly, affecting response clarity.\n2. **Recursive Reasoning Loops**: The model may enter circular reasoning patterns, leading to lengthy responses without a conclusive answer.\n3. **Safety and Ethical Considerations**: The model requires enhanced safety measures to ensure reliable and secure performance, and users should exercise caution when deploying it.\n4. **Performance and Benchmark Limitations**: The model excels in math and coding but has room for improvement in other areas, such as common sense reasoning and nuanced language understanding.\n\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o-2024-11-20": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "cacheReadsPrice": 1.25, "description": "The 2024-11-20 version of GPT-4o offers a leveled-up creative writing ability with more natural, engaging, and tailored writing to improve relevance & readability. It’s also better at working with uploaded files, providing deeper insights & more thorough responses.\n\nGPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-large-2411": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 6, "description": "Mistral Large 2 2411 is an update of [Mistral Large 2](/mistralai/mistral-large) released together with [Pixtral Large 2411](/mistralai/pixtral-large-2411)\n\nIt provides a significant upgrade on the previous [Mistral Large 24.07](/mistralai/mistral-large-2407), with notable improvements in long context understanding, a new system prompt, and more accurate function calling.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-large-2407": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 6, "description": "This is Mistral AI's flagship model, Mistral Large 2 (version mistral-large-2407). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt supports dozens of languages including French, German, Spanish, Italian, Portuguese, Arabic, Hindi, Russian, Chinese, Japanese, and Korean, along with 80+ coding languages including Python, Java, C, C++, JavaScript, and Bash. Its long context window allows precise information recall from large documents.\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/pixtral-large-2411": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 6, "description": "Pixtral Large is a 124B parameter, open-weight, multimodal model built on top of [Mistral Large 2](/mistralai/mistral-large-2411). The model is able to understand documents, charts and natural images.\n\nThe model is available under the Mistral Research License (MRL) for research and educational use, and the Mistral Commercial License for experimentation, testing, and production for commercial purposes.\n\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "x-ai/grok-vision-beta": {"maxTokens": 1639, "contextWindow": 8192, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 5, "outputPrice": 15, "description": "Grok Vision Beta is xAI's experimental language model with vision capability.\n\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "infermatic/mn-inferor-12b": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.6, "outputPrice": 1, "description": "Inferor 12B is a merge of top roleplay models, expert on immersive narratives and storytelling.\n\nThis model was merged using the [Model Stock](https://arxiv.org/abs/2403.19522) merge method using [anthracite-org/magnum-v4-12b](https://openrouter.ai/anthracite-org/magnum-v4-72b) as a base.\n", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2.5-coder-32b-instruct:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2.5-coder-32b-instruct": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.06, "outputPrice": 0.15, "description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "raifle/sorcererlm-8x22b": {"maxTokens": 3200, "contextWindow": 16000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 4.5, "outputPrice": 4.5, "description": "SorcererLM is an advanced RP and storytelling model, built as a Low-rank 16-bit LoRA fine-tuned on [WizardLM-2 8x22B](/microsoft/wizardlm-2-8x22b).\n\n- Advanced reasoning and emotional intelligence for engaging and immersive interactions\n- Vivid writing capabilities enriched with spatial and contextual awareness\n- Enhanced narrative depth, promoting creative and dynamic storytelling", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thedrummer/unslopnemo-12b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.*****************, "outputPrice": 0.*****************, "description": "UnslopNemo v4.1 is the latest addition from the creator of Rocinante, designed for adventure writing and role-play scenarios.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.5-haiku-20241022": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.7999999999999999, "outputPrice": 4, "cacheWritesPrice": 1, "cacheReadsPrice": 0.08, "description": "Claude 3.5 Haiku features enhancements across all skill sets including coding, tool use, and reasoning. As the fastest model in the Anthropic lineup, it offers rapid response times suitable for applications that require high interactivity and low latency, such as user-facing chatbots and on-the-fly code completions. It also excels in specialized tasks like data extraction and real-time content moderation, making it a versatile tool for a broad range of industries.\n\nIt does not support image inputs.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/3-5-models-and-computer-use)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.5-haiku:beta": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.7999999999999999, "outputPrice": 4, "cacheWritesPrice": 1, "cacheReadsPrice": 0.08, "description": "Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.\n\nThis makes it highly suitable for environments that demand both speed and precision, such as software development, customer service bots, and data management systems.\n\nThis model is currently pointing to [Claude 3.5 Haiku (2024-10-22)](/anthropic/claude-3-5-haiku-20241022).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.5-haiku": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.7999999999999999, "outputPrice": 4, "cacheWritesPrice": 1, "cacheReadsPrice": 0.08, "description": "Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.\n\nThis makes it highly suitable for environments that demand both speed and precision, such as software development, customer service bots, and data management systems.\n\nThis model is currently pointing to [Claude 3.5 Haiku (2024-10-22)](/anthropic/claude-3-5-haiku-20241022).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.5-sonnet:beta": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "New Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"], "supportsComputerUse": true}, "anthropic/claude-3.5-sonnet": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "New Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"], "supportsComputerUse": true}, "anthracite-org/magnum-v4-72b": {"maxTokens": 1024, "contextWindow": 16384, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 3, "description": "This is a series of models designed to replicate the prose quality of the Claude 3 models, specifically Sonnet(https://openrouter.ai/anthropic/claude-3.5-sonnet) and Opus(https://openrouter.ai/anthropic/claude-3-opus).\n\nThe model is fine-tuned on top of [Qwen2.5 72B](https://openrouter.ai/qwen/qwen-2.5-72b-instruct).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/ministral-8b": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.09999999999999999, "description": "Ministral 8B is an 8B parameter model featuring a unique interleaved sliding-window attention pattern for faster, memory-efficient inference. Designed for edge use cases, it supports up to 128k context length and excels in knowledge and reasoning tasks. It outperforms peers in the sub-10B category, making it perfect for low-latency, privacy-first applications.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/ministral-3b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.04, "outputPrice": 0.04, "description": "Ministral 3B is a 3B parameter model optimized for on-device and edge computing. It excels in knowledge, commonsense reasoning, and function-calling, outperforming larger models like Mistral 7B on most benchmarks. Supporting up to 128k context length, it’s ideal for orchestrating agentic workflows and specialist tasks with efficient inference.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2.5-7b-instruct": {"maxTokens": 13108, "contextWindow": 65536, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.04, "outputPrice": 0.09999999999999999, "description": "Qwen2.5 7B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nvidia/llama-3.1-nemotron-70b-instruct": {"maxTokens": 131072, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.12, "outputPrice": 0.3, "description": "NVIDIA's Llama 3.1 Nemotron 70B is a language model designed for generating precise and useful responses. Leveraging [Llama 3.1 70B](/models/meta-llama/llama-3.1-70b-instruct) architecture and Reinforcement Learning from Human Feedback (RLHF), it excels in automatic alignment benchmarks. This model is tailored for applications requiring high accuracy in helpfulness and response generation, suitable for diverse user queries across multiple domains.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "inflection/inflection-3-productivity": {"maxTokens": 1024, "contextWindow": 8000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "description": "Inflection 3 Productivity is optimized for following instructions. It is better for tasks requiring JSON output or precise adherence to provided guidelines. It has access to recent news.\n\nFor emotional intelligence similar to Pi, see [Inflect 3 Pi](/inflection/inflection-3-pi)\n\nSee [Inflection's announcement](https://inflection.ai/blog/enterprise) for more details.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "inflection/inflection-3-pi": {"maxTokens": 1024, "contextWindow": 8000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "description": "Inflection 3 Pi powers Inflection's [<PERSON>](https://pi.ai) chatbot, including backstory, emotional intelligence, productivity, and safety. It has access to recent news, and excels in scenarios like customer support and roleplay.\n\n<PERSON> has been trained to mirror your tone and style, if you use more emojis, so will Pi! Try experimenting with various prompts and conversation styles.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-flash-1.5-8b": {"maxTokens": 8192, "contextWindow": 1000000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.0375, "outputPrice": 0.15, "cacheWritesPrice": 0.0583, "cacheReadsPrice": 0.01, "description": "Gemini Flash 1.5 8B is optimized for speed and efficiency, offering enhanced performance in small prompt tasks like chat, transcription, and translation. With reduced latency, it is highly effective for real-time and large-scale operations. This model focuses on cost-effective solutions while maintaining high-quality results.\n\n[Click here to learn more about this model](https://developers.googleblog.com/en/gemini-15-flash-8b-is-now-generally-available-for-use/).\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthracite-org/magnum-v2-72b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 3, "description": "From the maker of [Goliath](https://openrouter.ai/models/alpindale/goliath-120b), Magnum 72B is the seventh in a family of models designed to achieve the prose quality of the Claude 3 models, notably Opus & Sonnet.\n\nThe model is based on [Qwen2 72B](https://openrouter.ai/models/qwen/qwen-2-72b-instruct) and trained with 55 million tokens of highly curated roleplay (RP) data.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "thedrummer/rocinante-12b": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19, "outputPrice": 0.44999999999999996, "description": "Rocinante 12B is designed for engaging storytelling and rich prose.\n\nEarly testers have reported:\n- Expanded vocabulary with unique and expressive word choices\n- Enhanced creativity for vivid narratives\n- Adventure-filled and captivating stories", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "liquid/lfm-40b": {"maxTokens": 65536, "contextWindow": 65536, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.15, "description": "Liquid's 40.3B Mixture of Experts (MoE) model. Liquid Foundation Models (LFMs) are large neural networks built with computational units rooted in dynamic systems.\n\nLFMs are general-purpose AI models that can be used to model any kind of sequential data, including video, audio, text, time series, and signals.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.2-3b-instruct:free": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Llama 3.2 3B is a 3-billion-parameter multilingual large language model, optimized for advanced natural language processing tasks like dialogue generation, reasoning, and summarization. Designed with the latest transformer architecture, it supports eight languages, including English, Spanish, and Hindi, and is adaptable for additional languages.\n\nTrained on 9 trillion tokens, the Llama 3.2 3B model excels in instruction-following, complex reasoning, and tool use. Its balanced performance makes it ideal for applications needing accuracy and efficiency in text generation across multilingual settings.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.2-3b-instruct": {"maxTokens": 20000, "contextWindow": 20000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.003, "outputPrice": 0.006, "description": "Llama 3.2 3B is a 3-billion-parameter multilingual large language model, optimized for advanced natural language processing tasks like dialogue generation, reasoning, and summarization. Designed with the latest transformer architecture, it supports eight languages, including English, Spanish, and Hindi, and is adaptable for additional languages.\n\nTrained on 9 trillion tokens, the Llama 3.2 3B model excels in instruction-following, complex reasoning, and tool use. Its balanced performance makes it ideal for applications needing accuracy and efficiency in text generation across multilingual settings.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.2-11b-vision-instruct:free": {"maxTokens": 2048, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Llama 3.2 11B Vision is a multimodal model with 11 billion parameters, designed to handle tasks combining visual and textual data. It excels in tasks such as image captioning and visual question answering, bridging the gap between language generation and visual reasoning. Pre-trained on a massive dataset of image-text pairs, it performs well in complex, high-accuracy image analysis.\n\nIts ability to integrate visual understanding with language processing makes it an ideal solution for industries requiring comprehensive visual-linguistic AI applications, such as content creation, AI-driven customer service, and research.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.2-11b-vision-instruct": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.049, "outputPrice": 0.049, "description": "Llama 3.2 11B Vision is a multimodal model with 11 billion parameters, designed to handle tasks combining visual and textual data. It excels in tasks such as image captioning and visual question answering, bridging the gap between language generation and visual reasoning. Pre-trained on a massive dataset of image-text pairs, it performs well in complex, high-accuracy image analysis.\n\nIts ability to integrate visual understanding with language processing makes it an ideal solution for industries requiring comprehensive visual-linguistic AI applications, such as content creation, AI-driven customer service, and research.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.2-90b-vision-instruct": {"maxTokens": 2048, "contextWindow": 131072, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 1.2, "outputPrice": 1.2, "description": "The Llama 90B Vision model is a top-tier, 90-billion-parameter multimodal model designed for the most challenging visual reasoning and language tasks. It offers unparalleled accuracy in image captioning, visual question answering, and advanced image-text comprehension. Pre-trained on vast multimodal datasets and fine-tuned with human feedback, the Llama 90B Vision is engineered to handle the most demanding image-based AI tasks.\n\nThis model is perfect for industries requiring cutting-edge multimodal AI capabilities, particularly those dealing with complex, real-time visual and textual analysis.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.2-1b-instruct": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.005, "outputPrice": 0.01, "description": "Llama 3.2 1B is a 1-billion-parameter language model focused on efficiently performing natural language tasks, such as summarization, dialogue, and multilingual text analysis. Its smaller size allows it to operate efficiently in low-resource environments while maintaining strong task performance.\n\nSupporting eight core languages and fine-tunable for more, Llama 1.3B is ideal for businesses or developers seeking lightweight yet powerful AI solutions that can operate in diverse multilingual settings without the high computational demand of larger models.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2.5-72b-instruct:free": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Qwen2.5 72B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2.5-72b-instruct": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.101, "outputPrice": 0.101, "description": "Qwen2.5 72B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "neversleep/llama-3.1-lumimaid-8b": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.8999999999999999, "description": "Lumimaid v0.2 8B is a finetune of [Llama 3.1 8B](/models/meta-llama/llama-3.1-8b-instruct) with a \"HUGE step up dataset wise\" compared to Lumimaid v0.1. Sloppy chats output were purged.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/o1-mini": {"maxTokens": 65536, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheReadsPrice": 0.55, "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "openai/o1-mini-2024-09-12": {"maxTokens": 65536, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.1, "outputPrice": 4.4, "cacheReadsPrice": 0.55, "description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens"]}, "mistralai/pixtral-12b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.09999999999999999, "description": "The first multi-modal, text+image-to-text model from Mistral AI. Its weights were launched via torrent: https://x.com/mistralai/status/1833758285167722836.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r-plus-08-2024": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "description": "command-r-plus-08-2024 is an update of the [Command R+](/models/cohere/command-r-plus) with roughly 50% higher throughput and 25% lower latencies as compared to the previous Command R+ version, while keeping the hardware footprint the same.\n\nRead the launch post [here](https://docs.cohere.com/changelog/command-gets-refreshed).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r-08-2024": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.6, "description": "command-r-08-2024 is an update of the [Command R](/models/cohere/command-r) with improved performance for multilingual retrieval-augmented generation (RAG) and tool use. More broadly, it is better at math, code and reasoning and is competitive with the previous version of the larger Command R+ model.\n\nRead the launch post [here](https://docs.cohere.com/changelog/command-gets-refreshed).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2.5-vl-7b-instruct": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.19999999999999998, "description": "Qwen2.5 VL 7B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Understanding videos of 20min+: Qwen2.5-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "sao10k/l3.1-euryale-70b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.65, "outputPrice": 0.75, "description": "Euryale L3.1 70B v2.2 is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k). It is the successor of [Euryale L3 70B v2.1](/models/sao10k/l3-euryale-70b).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/phi-3.5-mini-128k-instruct": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.09999999999999999, "description": "Phi-3.5 models are lightweight, state-of-the-art open models. These models were trained with Phi-3 datasets that include both synthetic data and the filtered, publicly available websites data, with a focus on high quality and reasoning-dense properties. Phi-3.5 Mini uses 3.8B parameters, and is a dense decoder-only transformer model using the same tokenizer as [Phi-3 Mini](/models/microsoft/phi-3-mini-128k-instruct).\n\nThe models underwent a rigorous enhancement process, incorporating both supervised fine-tuning, proximal policy optimization, and direct preference optimization to ensure precise instruction adherence and robust safety measures. When assessed against benchmarks that test common sense, language understanding, math, code, long context and logical reasoning, Phi-3.5 models showcased robust and state-of-the-art performance among models with less than 13 billion parameters.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nousresearch/hermes-3-llama-3.1-70b": {"maxTokens": 26215, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.28, "description": "Hermes 3 is a generalist language model with many improvements over [Hermes 2](/models/nousresearch/nous-hermes-2-mistral-7b-dpo), including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 70B is a competitive, if not superior finetune of the [Llama-3.1 70B foundation model](/models/meta-llama/llama-3.1-70b-instruct), focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nousresearch/hermes-3-llama-3.1-405b": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7, "outputPrice": 0.7999999999999999, "description": "Hermes 3 is a generalist language model with many improvements over Hermes 2, including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 405B is a frontier-level, full-parameter finetune of the Llama-3.1 405B foundation model, focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.\n\nHermes 3 is competitive, if not superior, to Llama-3.1 Instruct models at general capabilities, with varying strengths and weaknesses attributable between the two.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/chatgpt-4o-latest": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 5, "outputPrice": 15, "description": "OpenAI ChatGPT 4o is continually updated by OpenAI to point to the current version of GPT-4o used by ChatGPT. It therefore differs slightly from the API version of [GPT-4o](/models/openai/gpt-4o) in that it has additional RLHF. It is intended for research and evaluation.\n\nOpenAI notes that this model is not suited for production use-cases as it may be removed or redirected to another model in the future.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "sao10k/l3-lunaris-8b": {"maxTokens": 1639, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.02, "outputPrice": 0.049999999999999996, "description": "Lunaris 8B is a versatile generalist and roleplaying model based on Llama 3. It's a strategic merge of multiple models, designed to balance creativity with improved logic and general knowledge.\n\nCreated by [Sao10k](https://huggingface.co/Sao10k), this model aims to offer an improved experience over Stheno v3.2, with enhanced creativity and logical reasoning.\n\nFor best results, use with Llama 3 Instruct context template, temperature 1.4, and min_p 0.1.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o-2024-08-06": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "cacheReadsPrice": 1.25, "description": "The 2024-08-06 version of GPT-4o offers improved performance in structured outputs, with the ability to supply a JSON schema in the respone_format. Read more [here](https://openai.com/index/introducing-structured-outputs-in-the-api/).\n\nGPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.1-405b": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 2, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This is the base 405B pre-trained version.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nothingiisreal/mn-celeste-12b": {"maxTokens": 4096, "contextWindow": 16384, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 1.2, "description": "A specialized story writing and roleplaying model based on Mistral's NeMo 12B Instruct. Fine-tuned on curated datasets including Reddit Writing Prompts and Opus Instruct 25K.\n\nThis model excels at creative writing, offering improved NSFW capabilities, with smarter and more active narration. It demonstrates remarkable versatility in both SFW and NSFW scenarios, with strong Out of Character (OOC) steering capabilities, allowing fine-tuned control over narrative direction and character behavior.\n\nCheck out the model's [HuggingFace page](https://huggingface.co/nothingiisreal/MN-12B-Celeste-V1.9) for details on what parameters and prompts work best!", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.1-405b-instruct:free": {"maxTokens": 13108, "contextWindow": 65536, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "The highly anticipated 400B class of Llama3 is here! Clocking in at 128k context with impressive eval scores, the Meta AI team continues to push the frontier of open-source LLMs.\n\nMeta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 405B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models including GPT-4o and Claude 3.5 Sonnet in evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.1-405b-instruct": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 0.7999999999999999, "description": "The highly anticipated 400B class of Llama3 is here! Clocking in at 128k context with impressive eval scores, the Meta AI team continues to push the frontier of open-source LLMs.\n\nMeta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 405B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models including GPT-4o and Claude 3.5 Sonnet in evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.1-8b-instruct": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.015, "outputPrice": 0.02, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 8B instruct-tuned version is fast and efficient.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3.1-70b-instruct": {"maxTokens": 16384, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.28, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 70B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3-1/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-nemo:free": {"maxTokens": 128000, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-nemo": {"maxTokens": 6400, "contextWindow": 32000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.0075, "outputPrice": 0.049999999999999996, "description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o-mini": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.6, "cacheReadsPrice": 0.075, "description": "GPT-4o mini is OpenAI's newest model after [GPT-4 Omni](/models/openai/gpt-4o), supporting both text and image inputs with text outputs.\n\nAs their most advanced small model, it is many multiples more affordable than other recent frontier models, and more than 60% cheaper than [GPT-3.5 Turbo](/models/openai/gpt-3.5-turbo). It maintains SOTA intelligence, while being significantly more cost-effective.\n\nGPT-4o mini achieves an 82% score on MMLU and presently ranks higher than GPT-4 on chat preferences [common leaderboards](https://arena.lmsys.org/).\n\nCheck out the [launch announcement](https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/) to learn more.\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o-mini-2024-07-18": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 0.15, "outputPrice": 0.6, "cacheReadsPrice": 0.075, "description": "GPT-4o mini is OpenAI's newest model after [GPT-4 Omni](/models/openai/gpt-4o), supporting both text and image inputs with text outputs.\n\nAs their most advanced small model, it is many multiples more affordable than other recent frontier models, and more than 60% cheaper than [GPT-3.5 Turbo](/models/openai/gpt-3.5-turbo). It maintains SOTA intelligence, while being significantly more cost-effective.\n\nGPT-4o mini achieves an 82% score on MMLU and presently ranks higher than GPT-4 on chat preferences [common leaderboards](https://arena.lmsys.org/).\n\nCheck out the [launch announcement](https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/) to learn more.\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-2-27b-it": {"maxTokens": 1639, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.65, "outputPrice": 0.65, "description": "Gemma 2 27B by Google is an open model built from the same research and technology used to create the [Gemini models](/models?q=gemini).\n\nGemma models are well-suited for a variety of text generation tasks, including question answering, summarization, and reasoning.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-2-9b-it:free": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\n\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemma-2-9b-it": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.004, "outputPrice": 0.004, "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\n\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.5-sonnet-20240620:beta": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Autonomously writes, edits, and runs code with reasoning and troubleshooting\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\nFor the latest version (2024-10-23), check out [Claude 3.5 Sonnet](/anthropic/claude-3.5-sonnet).\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3.5-sonnet-20240620": {"maxTokens": 8192, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 3, "outputPrice": 15, "cacheWritesPrice": 3.75, "cacheReadsPrice": 0.3, "description": "Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Autonomously writes, edits, and runs code with reasoning and troubleshooting\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\nFor the latest version (2024-10-23), check out [Claude 3.5 Sonnet](/anthropic/claude-3.5-sonnet).\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "sao10k/l3-euryale-70b": {"maxTokens": 8192, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.48, "outputPrice": 1.48, "description": "Euryale 70B v2.1 is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k).\n\n- Better prompt adherence.\n- Better anatomy / spatial awareness.\n- Adapts much better to unique and custom formatting / reply formats.\n- Very creative, lots of unique swipes.\n- Is not restrictive during roleplays.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cognitivecomputations/dolphin-mixtral-8x22b": {"maxTokens": 8192, "contextWindow": 16000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.8999999999999999, "outputPrice": 0.8999999999999999, "description": "Dolphin 2.9 is designed for instruction following, conversational, and coding. This model is a finetune of [Mixtral 8x22B Instruct](/models/mistralai/mixtral-8x22b-instruct). It features a 64k context length and was fine-tuned with a 16k sequence length using ChatML templates.\n\nThis model is a successor to [Dolphin Mixtral 8x7B](/models/cognitivecomputations/dolphin-mixtral-8x7b).\n\nThe model is uncensored and is stripped of alignment and bias. It requires an external alignment layer for ethical use. Users are cautioned to use this highly compliant model responsibly, as detailed in a blog post about uncensored models at [erichartford.com/uncensored-models](https://erichartford.com/uncensored-models).\n\n#moe #uncensored", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "qwen/qwen-2-72b-instruct": {"maxTokens": 4096, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.8999999999999999, "outputPrice": 0.8999999999999999, "description": "Qwen2 72B is a transformer-based model that excels in language understanding, multilingual capabilities, coding, mathematics, and reasoning.\n\nIt features SwiGLU activation, attention QKV bias, and group query attention. It is pretrained on extensive data with supervised finetuning and direct preference optimization.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2/) and [GitHub repo](https://github.com/QwenLM/Qwen2).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-7b-instruct-v0.3": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.028, "outputPrice": 0.054, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\nAn improved version of [Mistral 7B Instruct v0.2](/models/mistralai/mistral-7b-instruct-v0.2), with the following changes:\n\n- Extended vocabulary to 32768\n- Supports v3 Tokenizer\n- Supports function calling\n\nNOTE: Support for function calling depends on the provider.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-7b-instruct:free": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0, "outputPrice": 0, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-7b-instruct": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.028, "outputPrice": 0.054, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nousresearch/hermes-2-pro-llama-3-8b": {"maxTokens": 131072, "contextWindow": 131072, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.024999999999999998, "outputPrice": 0.04, "description": "Hermes 2 Pro is an upgraded, retrained version of Nous Hermes 2, consisting of an updated and cleaned version of the OpenHermes 2.5 Dataset, as well as a newly introduced Function Calling and JSON Mode dataset developed in-house.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/phi-3-mini-128k-instruct": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.09999999999999999, "outputPrice": 0.09999999999999999, "description": "Phi-3 Mini is a powerful 3.8B parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. This model is static, trained on an offline dataset with an October 2023 cutoff date.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/phi-3-medium-128k-instruct": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1, "outputPrice": 1, "description": "Phi-3 128K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 4k context length, try [Phi-3 Medium 4K](/models/microsoft/phi-3-medium-4k-instruct).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "neversleep/llama-3-lumimaid-70b": {"maxTokens": 4096, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 4, "outputPrice": 6, "description": "The NeverSleep team is back, with a Llama 3 70B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-flash-1.5": {"maxTokens": 8192, "contextWindow": 1000000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.075, "outputPrice": 0.3, "cacheWritesPrice": 0.1583, "cacheReadsPrice": 0.01875, "description": "Gemini 1.5 Flash is a foundation model that performs well at a variety of multimodal tasks such as visual understanding, classification, summarization, and creating content from image, audio and video. It's adept at processing visual and text inputs such as photographs, documents, infographics, and screenshots.\n\nGemini 1.5 Flash is designed for high-volume, high-frequency tasks where cost and latency matter. On most common tasks, Flash achieves comparable quality to other Gemini Pro models at a significantly reduced cost. Flash is well-suited for applications like chat assistants and on-demand content generation where speed and scale matter.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o-2024-05-13": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 5, "outputPrice": 15, "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o": {"maxTokens": 16384, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 2.5, "outputPrice": 10, "cacheReadsPrice": 1.25, "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4o:extended": {"maxTokens": 64000, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 6, "outputPrice": 18, "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-guard-2-8b": {"maxTokens": 1639, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.19999999999999998, "description": "This safeguard model has 8B parameters and is based on the Llama 3 family. Just like is predecessor, [LlamaGuard 1](https://huggingface.co/meta-llama/LlamaGuard-7b), it can do both prompt and response classification.\n\nLlamaGuard 2 acts as a normal LLM would, generating text that indicates whether the given input/output is safe/unsafe. If deemed unsafe, it will also share the content categories violated.\n\nFor best results, please use raw prompt input or the `/completions` endpoint, instead of the chat API.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "sao10k/fimbulvetr-11b-v2": {"maxTokens": 4096, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 1.2, "description": "Creative writing model, routed with permission. It's fast, it keeps the conversation going, and it stays in character.\n\nIf you submit a raw prompt, you can use Alpaca or Vicuna formats.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3-70b-instruct": {"maxTokens": 16384, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.3, "outputPrice": 0.*****************, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 70B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "meta-llama/llama-3-8b-instruct": {"maxTokens": 16384, "contextWindow": 8192, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.03, "outputPrice": 0.06, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mixtral-8x22b-instruct": {"maxTokens": 13108, "contextWindow": 65536, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.8999999999999999, "outputPrice": 0.8999999999999999, "description": "Mistral's official instruct fine-tuned version of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b). It uses 39B active parameters out of 141B, offering unparalleled cost efficiency for its size. Its strengths include:\n- strong math, coding, and reasoning\n- large context length (64k)\n- fluency in English, French, Italian, German, and Spanish\n\nSee benchmarks on the launch announcement [here](https://mistral.ai/news/mixtral-8x22b/).\n#moe", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "microsoft/wizardlm-2-8x22b": {"maxTokens": 65536, "contextWindow": 65536, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.48, "outputPrice": 0.48, "description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.\n\nIt is an instruct finetune of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b).\n\nTo read more about the model release, [click here](https://wizardlm.github.io/WizardLM2/).\n\n#moe", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4-turbo": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 10, "outputPrice": 30, "description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to December 2023.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "google/gemini-pro-1.5": {"maxTokens": 8192, "contextWindow": 2000000, "supportsImages": true, "supportsPromptCache": false, "inputPrice": 1.25, "outputPrice": 5, "description": "Google's latest multimodal model, supports image and video[0] in text or chat prompts.\n\nOptimized for language tasks including:\n\n- Code generation\n- Text generation\n- Text editing\n- Problem solving\n- Recommendations\n- Information extraction\n- Data extraction or generation\n- AI agents\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n* [0]: Video input is not available through OpenRouter at this time.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r-plus": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 15, "description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r-plus-04-2024": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 15, "description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "sophosympatheia/midnight-rose-70b": {"maxTokens": 2048, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 0.7999999999999999, "description": "A merge with a complex family tree, this model was crafted for roleplaying and storytelling. <PERSON> Rose is a successor to Rogue Rose and Aurora Nights and improves upon them both. It wants to produce lengthy output by default and is the best creative writing merge produced so far by sophosympatheia.\n\nDescending from earlier versions of <PERSON> Rose and [Wizard Tulu Dolphin 70B](https://huggingface.co/sophosympatheia/Wizard-Tu<PERSON>-Dolphin-70B-v1.0), it inherits the best qualities of each.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 1.5, "description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command": {"maxTokens": 4000, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1, "outputPrice": 2, "description": "Command is an instruction-following conversational model that performs language tasks with high quality, more reliably and with a longer context than our base generative models.\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3-haiku:beta": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.25, "outputPrice": 1.25, "cacheWritesPrice": 0.3, "cacheReadsPrice": 0.03, "description": "Claude 3 Haiku is Anthropic's fastest and most compact model for\nnear-instant responsiveness. Quick and accurate targeted performance.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-haiku)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3-haiku": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 0.25, "outputPrice": 1.25, "cacheWritesPrice": 0.3, "cacheReadsPrice": 0.03, "description": "Claude 3 Haiku is Anthropic's fastest and most compact model for\nnear-instant responsiveness. Quick and accurate targeted performance.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-haiku)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3-opus:beta": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 15, "outputPrice": 75, "cacheWritesPrice": 18.75, "cacheReadsPrice": 1.5, "description": "Claude 3 Opus is Anthropic's most powerful model for highly complex tasks. It boasts top-level performance, intelligence, fluency, and understanding.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "anthropic/claude-3-opus": {"maxTokens": 4096, "contextWindow": 200000, "supportsImages": true, "supportsPromptCache": true, "inputPrice": 15, "outputPrice": 75, "cacheWritesPrice": 18.75, "cacheReadsPrice": 1.5, "description": "Claude 3 Opus is Anthropic's most powerful model for highly complex tasks. It boasts top-level performance, intelligence, fluency, and understanding.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "cohere/command-r-03-2024": {"maxTokens": 4000, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 1.5, "description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-large": {"maxTokens": 25600, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 2, "outputPrice": 6, "description": "This is Mistral AI's flagship model, Mistral Large 2 (version `mistral-large-2407`). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt supports dozens of languages including French, German, Spanish, Italian, Portuguese, Arabic, Hindi, Russian, Chinese, Japanese, and Korean, along with 80+ coding languages including Python, Java, C, C++, JavaScript, and Bash. Its long context window allows precise information recall from large documents.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-3.5-turbo-0613": {"maxTokens": 4096, "contextWindow": 4095, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1, "outputPrice": 2, "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4-turbo-preview": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 10, "outputPrice": 30, "description": "The preview GPT-4 model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Dec 2023.\n\n**Note:** heavily rate limited by OpenAI while in preview.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "nousresearch/nous-hermes-2-mixtral-8x7b-dpo": {"maxTokens": 2048, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.6, "outputPrice": 0.6, "description": "Nous Hermes 2 Mixtral 8x7B DPO is the new flagship Nous Research model trained over the [Mixtral 8x7B MoE LLM](/models/mistralai/mixtral-8x7b).\n\nThe model was trained on over 1,000,000 entries of primarily [GPT-4](/models/openai/gpt-4) generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.\n\n#moe", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-small": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.6, "description": "With 22 billion parameters, Mistral Small v24.09 offers a convenient mid-point between (Mistral NeMo 12B)[/mistralai/mistral-nemo] and (Mistral Large 2)[/mistralai/mistral-large], providing a cost-effective solution that can be deployed across various platforms and environments. It has better reasoning, exhibits more capabilities, can produce and reason about code, and is multiligual, supporting English, French, German, Italian, and Spanish.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-tiny": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.25, "outputPrice": 0.25, "description": "Note: This model is being deprecated. Recommended replacement is the newer [Ministral 8B](/mistral/ministral-8b)\n\nThis model is currently powered by Mistral-7B-v0.2, and incorporates a \"better\" fine-tuning than [Mistral 7B](/models/mistralai/mistral-7b-instruct-v0.1), inspired by community work. It's best used for large batch processing tasks where cost is a significant factor but reasoning capabilities are not crucial.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-7b-instruct-v0.2": {"maxTokens": 6554, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.19999999999999998, "outputPrice": 0.19999999999999998, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\nAn improved version of [Mistral 7B Instruct](/modelsmistralai/mistral-7b-instruct-v0.1), with the following changes:\n\n- 32k context window (vs 8k context in v0.1)\n- Rope-theta = 1e6\n- No Sliding-Window Attention", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mixtral-8x7b-instruct": {"maxTokens": 16384, "contextWindow": 32768, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.08, "outputPrice": 0.24, "description": "Mixtral 8x7B Instruct is a pretrained generative Sparse Mixture of Experts, by Mistral AI, for chat and instruction use. Incorporates 8 experts (feed-forward networks) for a total of 47 billion parameters.\n\nInstruct model fine-tuned by Mistral. #moe", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "neversleep/noromaid-20b": {"maxTokens": 820, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1, "outputPrice": 1.75, "description": "A collab between IkariDev and Undi. This merge is suitable for RP, ERP, and general knowledge.\n\n#merge #uncensored", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "undi95/toppy-m-7b": {"maxTokens": 4096, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7999999999999999, "outputPrice": 1.2, "description": "A wild 7B parameter model that merges several models using the new task_arithmetic merge method from mergekit.\nList of merged models:\n- NousResearch/Nous-Capybara-7B-V1.9\n- [HuggingFaceH4/zephyr-7b-beta](/models/huggingfaceh4/zephyr-7b-beta)\n- lemonilia/AshhLimaRP-Mistral-7B\n- Vulkane/120-Days-of-Sodom-LoRA-Mistral-7b\n- Undi95/Mistral-pippa-sharegpt-7b-qlora\n\n#merge #uncensored", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "alpindale/goliath-120b": {"maxTokens": 1229, "contextWindow": 6144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 9, "outputPrice": 11, "description": "A large LLM created by combining two fine-tuned Llama 70B models into one 120B model. Combines Xwin and Euryale.\n\nCredits to\n- [@chargoddard](https://huggingface.co/chargoddard) for developing the framework used to merge the model - [mergekit](https://github.com/cg123/mergekit).\n- [@Undi95](https://huggingface.co/Undi95) for helping with the merge ratios.\n\n#merge", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openrouter/auto": {"maxTokens": 400000, "contextWindow": 2000000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": -1000000, "outputPrice": -1000000, "description": "Your prompt will be processed by a meta-model and routed to one of dozens of models (see below), optimizing for the best possible output.\n\nTo see which model was used, visit [Activity](/activity), or read the `model` attribute of the response. Your response will be priced at the same rate as the routed model.\n\nThe meta-model is powered by [Not Diamond](https://docs.notdiamond.ai/docs/how-not-diamond-works). Learn more in our [docs](/docs/model-routing).\n\nRequests will be routed to the following models:\n- [openai/gpt-4o-2024-08-06](/openai/gpt-4o-2024-08-06)\n- [openai/gpt-4o-2024-05-13](/openai/gpt-4o-2024-05-13)\n- [openai/gpt-4o-mini-2024-07-18](/openai/gpt-4o-mini-2024-07-18)\n- [openai/chatgpt-4o-latest](/openai/chatgpt-4o-latest)\n- [openai/o1-preview-2024-09-12](/openai/o1-preview-2024-09-12)\n- [openai/o1-mini-2024-09-12](/openai/o1-mini-2024-09-12)\n- [anthropic/claude-3.5-sonnet](/anthropic/claude-3.5-sonnet)\n- [anthropic/claude-3.5-haiku](/anthropic/claude-3.5-haiku)\n- [anthropic/claude-3-opus](/anthropic/claude-3-opus)\n- [anthropic/claude-2.1](/anthropic/claude-2.1)\n- [google/gemini-pro-1.5](/google/gemini-pro-1.5)\n- [google/gemini-flash-1.5](/google/gemini-flash-1.5)\n- [mistralai/mistral-large-2407](/mistralai/mistral-large-2407)\n- [mistralai/mistral-nemo](/mistralai/mistral-nemo)\n- [deepseek/deepseek-r1](/deepseek/deepseek-r1)\n- [meta-llama/llama-3.1-70b-instruct](/meta-llama/llama-3.1-70b-instruct)\n- [meta-llama/llama-3.1-405b-instruct](/meta-llama/llama-3.1-405b-instruct)\n- [mistralai/mixtral-8x22b-instruct](/mistralai/mixtral-8x22b-instruct)\n- [cohere/command-r-plus](/cohere/command-r-plus)\n- [cohere/command-r](/cohere/command-r)", "supportsReasoningEffort": false, "supportedParameters": []}, "openai/gpt-4-1106-preview": {"maxTokens": 4096, "contextWindow": 128000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 10, "outputPrice": 30, "description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to April 2023.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-3.5-turbo-instruct": {"maxTokens": 4096, "contextWindow": 4095, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.5, "outputPrice": 2, "description": "This model is a variant of GPT-3.5 Turbo tuned for instructional prompts and omitting chat-related optimizations. Training data: up to Sep 2021.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mistralai/mistral-7b-instruct-v0.1": {"maxTokens": 565, "contextWindow": 2824, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.11, "outputPrice": 0.19, "description": "A 7.3B parameter model that outperforms Llama 2 13B on all benchmarks, with optimizations for speed and context length.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "pygmalionai/mythalion-13b": {"maxTokens": 4096, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.6, "outputPrice": 1, "description": "A blend of the new Pygmalion-13b and MythoMax. #merge", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-3.5-turbo-16k": {"maxTokens": 4096, "contextWindow": 16385, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 3, "outputPrice": 4, "description": "This model offers four times the context length of gpt-3.5-turbo, allowing it to support approximately 20 pages of text in a single request at a higher cost. Training data: up to Sep 2021.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "mancer/weaver": {"maxTokens": 1000, "contextWindow": 8000, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 1.5, "outputPrice": 1.5, "description": "An attempt to recreate Claude-style verbosity, but don't expect the same level of coherence or memory. Meant for use in roleplay/narrative situations.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "undi95/remm-slerp-l2-13b": {"maxTokens": 1229, "contextWindow": 6144, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.7, "outputPrice": 1, "description": "A recreation trial of the original MythoMax-L2-B13 but with updated models. #merge", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "gryphe/mythomax-l2-13b": {"maxTokens": 820, "contextWindow": 4096, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.06, "outputPrice": 0.06, "description": "One of the highest performing and most popular fine-tunes of Llama 2 13B, with rich descriptions and roleplay. #merge", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-3.5-turbo": {"maxTokens": 4096, "contextWindow": 16385, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 0.5, "outputPrice": 1.5, "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4-0314": {"maxTokens": 4096, "contextWindow": 8191, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 30, "outputPrice": 60, "description": "GPT-4-0314 is the first version of GPT-4 released, with a context length of 8,192 tokens, and was supported until June 14. Training data: up to Sep 2021.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}, "openai/gpt-4": {"maxTokens": 4096, "contextWindow": 8191, "supportsImages": false, "supportsPromptCache": false, "inputPrice": 30, "outputPrice": 60, "description": "OpenAI's flagship model, GPT-4 is a large-scale multimodal language model capable of solving difficult problems with greater accuracy than previous models due to its broader general knowledge and advanced reasoning capabilities. Training data: up to Sep 2021.", "supportsReasoningEffort": false, "supportedParameters": ["max_tokens", "temperature"]}}