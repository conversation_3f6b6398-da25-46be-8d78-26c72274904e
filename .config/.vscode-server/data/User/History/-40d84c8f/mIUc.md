 
# Copilot Instructions for Project Parlay

## Overview
Project Parlay is a sports analytics platform with a Python/Flask backend and a React/Vite frontend. It delivers confidence-weighted predictions and optimization tools for sports betting, integrating machine learning, expert calibration, and crowd wisdom. The codebase also includes a separate "Handicapper Manager" fullstack app for managing handicappers.

## Architecture
- **fullstack/**: Main app. Contains `backend/` (Flask, data science modules, DB access) and `frontend/` (React, Vite, Tailwind).
- **handicapper_manager_webapp/**: Standalone fullstack app for managing handicappers, with its own backend and frontend.
- **data_collecion_automation/**: Python scripts for scraping and ingesting sports data.
- **ai_docs/**: Design docs, plans, and changelogs for AI-driven features.

## Key Workflows
- **Build (main app):**
  - `./build.sh` — Installs frontend and backend dependencies, builds frontend.
- **Development (main app):**
  - `./dev.sh` — Installs all dependencies, builds frontend, and starts dev servers (frontend: Vite, backend: Flask).
- **Handicapper Manager:**
  - See `handicapper_manager_webapp/README.md` for setup. Typical: `npm run setup` then `npm run dev`.

## Backend Patterns
- **Flask app** (`fullstack/backend/app.py`):
  - Uses CORS for local dev.
  - Relies on environment variables (see `.env` loading).
  - Key modules: `modules/ppObjects.py`, `modules/RROptimizer.py`, `modules/HandiCapperAccuracyModel/`.
  - Data access via `data_science_modules/planet_scale_port.py` (PlanetScale MySQL).
  - Date logic: global `auto_update_enabled` and `current_default_date` (see `ai_docs/add-auto-update-date-toggle-plan.md`).
  - Admin endpoints require password `ppadmin42` (see `update_default_date`).

## Frontend Patterns
- **React + Vite** (see `fullstack/frontend/`):
  - Uses Tailwind for styling.
  - API requests proxied to backend (see `proxy` in `package.json`).
  - Date settings UI and logic in `src/components/DateSettingsSection/`, `src/contexts/DateContext.tsx`, and `src/services/dateService.ts`.

## Project Conventions
- **Python:**
  - Use absolute imports for cross-module access.
  - Fallbacks for missing dependencies (see try/except in backend modules).
  - Admin actions require password (hardcoded for now).
- **Node/JS:**
  - Use `npm run dev` for local dev, `npm run build` for production builds.
  - TypeScript is used in frontend for type safety.
- **Data Flows:**
  - Frontend fetches predictions, date settings, and submits picks via REST endpoints.
  - Backend validates and processes picks, manages date logic, and interacts with MySQL.

## Integration Points
- **PlanetScale MySQL**: Used for persistent data (see `planet_scale_port.py`).
- **Discord/Sheets**: Data ingestion via scripts in `data_collecion_automation/`.
- **AI/ML Models**: See `HandiCapperAccuracyModel/` and `RROptimizer.py` for model logic.

## Examples
- To add a new API endpoint, follow the Flask route patterns in `backend/app.py`.
- To update date logic, see both backend (`get_default_date`, `update_default_date`) and frontend (`DateSettingsSection`, `DateContext`).
- For new data ingestion, add scripts to `data_collecion_automation/` and connect to backend modules as needed.

---
For more details, see `README.md` files in each major directory and `ai_docs/` for design docs.
