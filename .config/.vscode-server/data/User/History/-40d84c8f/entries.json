{"version": 1, "resource": "vscode-remote://ssh-remote%2Bc2a27110-b7a1-4b06-a011-9ba7e69fcba4-00-3rkww64qajcww.spock.replit.dev/home/<USER>/workspace/.github/copilot-instructions.md", "entries": [{"id": "u0F3.md", "source": "Chat Edit: 'Analyze this codebase to generate or update `.github/copilot-instructions.md` for guiding AI coding agents.\n\nFocus on discovering the essential knowledge that would help an AI agents be immediately productive in this codebase. Consider aspects like:\n- The \"big picture\" architecture that requires reading multiple files to understand - major components, service boundaries, data flows, and the \"why\" behind structural decisions\n- Critical developer workflows (builds, tests, debugging) especially commands that aren't obvious from file inspection alone\n- Project-specific conventions and patterns that differ from common practices\n- Integration points, external dependencies, and cross-component communication patterns\n\nSource existing AI conventions from `**/{.github/copilot-instructions.md,AGENT.md,AGENTS.md,CLAUDE.md,.cursorrules,.windsurfrules,.clinerules,.cursor/rules/**,.windsurf/rules/**,.clinerules/**,README.md}` (do one glob search).\n\nGuidelines (read more at https://aka.ms/vscode-instructions-docs):\n- If `.github/copilot-instructions.md` exists, merge intelligently - preserve valuable content while updating outdated sections\n- Write concise, actionable instructions (~20-50 lines) using markdown structure\n- Include specific examples from the codebase when describing patterns\n- Avoid generic advice (\"write tests\", \"handle errors\") - focus on THIS project's specific approaches\n- Document only discoverable patterns, not aspirational practices\n- Reference key files/directories that exemplify important patterns\n\nUpdate `.github/copilot-instructions.md` for the user, then ask for feedback on any unclear or incomplete sections to iterate.'", "timestamp": 1754553378343}, {"id": "mIUc.md", "timestamp": 1754553669615}]}