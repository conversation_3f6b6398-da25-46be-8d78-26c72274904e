import os
import sys
import json
import random
import sqlite3
import requests
from datetime import datetime
try:
    from zoneinfo import ZoneInfo
except ImportError:
    # Fallback for Python < 3.9
    from datetime import timezone, timedelta
    
    class ZoneInfo:
        def __init__(self, name):
            if name == "America/Los_Angeles":
                # PST is UTC-8, PDT is UTC-7
                # This is a simplified implementation - for production use pytz
                self.offset = timedelta(hours=-8)  # Simplified as PST
            else:
                self.offset = timedelta(0)
        
        def utcoffset(self, dt):
            return self.offset
from itertools import combinations
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
from typing import Dict
from functools import wraps
from dotenv import load_dotenv
from werkzeug.exceptions import BadRequest
import re

# Load environment variables
load_dotenv()

from modules.ppObjects import Pick, BoostPromo, ProtectedPromo
from modules.RROptimizer import analyze_all_splits
from modules.HandiCapperAccuracyModel import main_model
from modules.HandiCapperAccuracyModel.util.PlotUtils import PlotUtils
from data_science_modules.planet_scale_port import get_todays_events_with_handicappers, submit_event, generate_event_id, generate_admin_event_id, get_connection, get_dict_connection, normalize_stat_type_for_storage
try:
    from services.data_validation import validate_and_clean_pick_data
    print("[INFO] Successfully imported data validation service")
except ImportError as e:
    print(f"[ERROR] Failed to import data validation service: {e}")
    # Fallback validation function
    def validate_and_clean_pick_data(pick_data):
        errors = []
        # Basic validation
        required_fields = ['league', 'pick_type', 'prediction', 'expert_name', 'event_date']
        for field in required_fields:
            if not pick_data.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Prop pick validation
        if pick_data.get('pick_type') == 'Prop':
            prop_fields = ['player_name', 'stat_type', 'stat_threshold']
            for field in prop_fields:
                if not pick_data.get(field):
                    errors.append(f"Prop picks require field: {field}")
        
        return pick_data, errors

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), "../frontend/dist"), static_url_path="/")
CORS(app, origins=["http://localhost:5173", "http://localhost:5174"])

def get_current_california_date():
    """
    Get the current date in California timezone (Pacific Time).
    
    Returns:
        str: Current date in YYYY-MM-DD format for California
    """
    try:
        # Get current time in California timezone
        california_tz = ZoneInfo("America/Los_Angeles")
        california_time = datetime.now(california_tz)
        
        # Format as YYYY-MM-DD
        return california_time.strftime("%Y-%m-%d")
    except Exception as e:
        print(f"❌ Error getting California date, using fallback: {e}")
        return "2025-07-20"  # Fallback date

# Global date storage for admin settings - now defaults to current California date
current_default_date = get_current_california_date()
auto_update_enabled = True

def get_default_date():
    if auto_update_enabled:
        return get_current_california_date()
    return current_default_date

def validate_date_format(date_string):
    """
    Comprehensive date validation and sanitization.
    
    Args:
        date_string (str): Date string to validate
        
    Returns:
        tuple: (is_valid, sanitized_date, error_message)
    """
    import re
    from datetime import datetime, date
    
    if not date_string:
        return False, None, "Date is required"
    
    if not isinstance(date_string, str):
        return False, None, "Date must be a string"
    
    # Sanitize input - remove whitespace and common separators
    sanitized = date_string.strip()
    
    # Check for exact YYYY-MM-DD format
    if not re.match(r'^\d{4}-\d{2}-\d{2}$', sanitized):
        return False, None, f"Invalid date format. Please use YYYY-MM-DD format (e.g., {get_current_california_date()})"
    
    # Validate it's a real date
    try:
        parsed_date = datetime.strptime(sanitized, "%Y-%m-%d")
        
        # Additional validation checks
        year = parsed_date.year
        if year < 2020 or year > 2030:
            return False, None, "Date must be between 2020 and 2030"
        
        # Check if date is not too far in the past (more than 1 year)
        today = date.today()
        date_obj = parsed_date.date()
        days_diff = (date_obj - today).days
        
        if days_diff < -365:
            return False, None, "Date cannot be more than 1 year in the past"
        
        if days_diff > 365:
            return False, None, "Date cannot be more than 1 year in the future"
        
        return True, sanitized, None
        
    except ValueError as e:
        return False, None, f"Invalid date: {str(e)}"

def update_default_date(new_date, admin_password, auto_update=None):
    """
    Updates the default date setting with comprehensive validation.
    
    Args:
        new_date (str): New date in YYYY-MM-DD format
        admin_password (str): Admin password for authentication
        
    Returns:
        dict: Result with success status and message
    """
    global current_default_date, auto_update_enabled
    
    try:
        # Validate admin password
        if not admin_password:
            return {
                "success": False,
                "error": "Admin password is required",
                "code": "MISSING_PASSWORD"
            }
        
        if not isinstance(admin_password, str):
            return {
                "success": False,
                "error": "Admin password must be a string",
                "code": "INVALID_PASSWORD_TYPE"
            }
        
        if admin_password != "ppadmin42":
            return {
                "success": False,
                "error": "Invalid admin password",
                "code": "INVALID_PASSWORD"
            }
        
        # Comprehensive date validation
        is_valid, sanitized_date, error_message = validate_date_format(new_date)
        
        if not is_valid:
            return {
                "success": False,
                "error": error_message,
                "code": "INVALID_DATE"
            }
        
        # Store the previous date for rollback if needed
        previous_date = current_default_date
        
        # Update the global date setting
        current_default_date = sanitized_date
        
        if auto_update is not None:
            auto_update_enabled = bool(auto_update)
        
        print(f"[INFO] Date setting updated: {previous_date} -> {sanitized_date}")
        
        return {
            "success": True,
            "message": f"Date updated successfully from {previous_date} to {sanitized_date}",
            "previous_date": previous_date,
            "new_date": sanitized_date,
            "auto_update": auto_update_enabled
        }
        
    except Exception as e:
        print(f"[ERROR] Unexpected error in update_default_date: {e}")
        return {
            "success": False,
            "error": "Internal server error occurred while updating date",
            "code": "INTERNAL_ERROR"
        }

# Global object lists
pick_objects = []
boost_promo_objects = []
protected_promo_objects = []
next_id = 1
user_accuracy = 0.0

_WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
if _WORKSPACE_ROOT not in sys.path:
    sys.path.append(_WORKSPACE_ROOT)

_PROJECT_ROOT = os.path.dirname(_WORKSPACE_ROOT)
if _PROJECT_ROOT not in sys.path:
    sys.path.append(_PROJECT_ROOT)

DB_PATH = os.path.join(_WORKSPACE_ROOT, "data", "pick_confidence.db")
_DB_DIR = os.path.dirname(DB_PATH)
if not os.path.exists(_DB_DIR):
    os.makedirs(_DB_DIR)
    print(f"Created data directory: {_DB_DIR}")


# Note: PlanetScale doesn't allow DDL modifications, so we rely on application-level duplicate prevention
print("[INFO] Using application-level duplicate prevention for expert_predictions")

origin_profiles = {
    "ChalkBoardPI": 0.80,
    "HarryLock": 0.71,
    "DanGamblePOD": 0.78,
    "DanGambleAIEdge": 90.0,
    "GameScript": 0.80,
    "Winible": 0.83,
    "DoberMan": 0.76,
    "JoshMiller": 0.60,
    "Me": lambda: user_accuracy
}

def confidence_score(decimal_odds, expert_confidence, expert_accuracy):
    implied_prob = 1 / decimal_odds
    c = expert_confidence / 100
    a = expert_accuracy / 100
    score = 100 * (a * c + (1 - a) * implied_prob)
    return score

def calculate_dynamic_confidence(event_id, expert_confidence):
    """
    Calculate dynamic confidence based on ML model predictions vs expert confidence.
    
    Args:
        event_id (str): The event ID to get ML predictions for
        expert_confidence (float): Expert confidence as decimal (0.0-1.0)
    
    Returns:
        float: Scaled confidence value (0-100)
    """
    try:
        # Call ML model to get predictions
        prediction_result = main_model(event_id)
        
        # Extract model probability (prefer combined_prob, fallback to other probabilities)
        model_prob = prediction_result.get("combined_prob")
        if model_prob is None:
            model_prob = prediction_result.get("bayesian_prob", 0.5)
        
        # Calculate difference: model_prob - expert_confidence
        difference = model_prob - expert_confidence
        
        # Clamp difference to [-0.2, 0.2] range for mapping bounds
        clamped_difference = max(min(difference, 0.2), -0.2)
        
        # Scale clamped difference to 0-100% where -20% = 0% and 20% = 100%
        scaled_confidence = ((clamped_difference + 0.2) / 0.4) * 100
        
        return round(scaled_confidence, 2)
        
    except Exception as e:
        print(f"[ERROR] Dynamic confidence calculation failed for {event_id}: {e}")
        # Fallback to original expert confidence scaled to 0-100
        return expert_confidence * 100 if expert_confidence <= 1.0 else expert_confidence

def generate_round_robin_subparlays(pick_list, subgroup_size):
    """Returns a list of subparlays (combinations) of picks."""
    return [list(combo) for combo in combinations(pick_list, subgroup_size)]

def correct_event_id_format(event_id):
    """
    Corrects common event ID formatting issues.
    
    Args:
        event_id (str): The original event ID
        
    Returns:
        tuple: (corrected_event_id, list_of_corrections_made)
    """
    import re
    
    corrections = []
    original_event_id = event_id
    
    # Expected format: YYYY-MM-DD-LEAGUE-PLAYERNAME##.#STATTYPE
    pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)(.*?)$'
    match = re.match(pattern, event_id.upper())
    
    if not match:
        corrections.append(f"Invalid event ID format: {event_id}")
        return event_id, corrections
    
    date_part, league, player_part, threshold_part, stat_part = match.groups()
    
    # Fix player name: remove periods and spaces
    original_player = player_part
    clean_player = player_part.replace(".", "").replace(" ", "")
    if clean_player != original_player:
        corrections.append(f"Cleaned player name: '{original_player}' -> '{clean_player}'")
    
    # Fix threshold format: ensure decimal point
    original_threshold = threshold_part
    try:
        threshold_float = float(threshold_part)
        formatted_threshold = f"{threshold_float:.1f}"
        if formatted_threshold != original_threshold:
            corrections.append(f"Fixed threshold format: '{original_threshold}' -> '{formatted_threshold}'")
        threshold_part = formatted_threshold
    except ValueError:
        corrections.append(f"Invalid threshold format: '{original_threshold}'")
    
    # Map stat types to standard abbreviations by league
    original_stat = stat_part
    stat_type_map = {
        # NBA/NFL stats
        'POINTS': 'PTS',
        'POINT': 'PTS',
        'PTS': 'PTS',
        'REBOUNDS': 'REB',
        'REBOUND': 'REB',
        'REB': 'REB',
        'ASSISTS': 'AST',
        'ASSIST': 'AST',
        'AST': 'AST',
        'STEALS': 'STL',
        'STEAL': 'STL',
        'STL': 'STL',
        'BLOCKS': 'BLK',
        'BLOCK': 'BLK',
        'BLK': 'BLK',
        'TURNOVERS': 'TO',
        'TURNOVER': 'TO',
        'TO': 'TO',
        'THREESMADE': '3PM',
        'THREEPOINTSMADE': '3PM',
        '3PM': '3PM',
        'THREES': '3PM',
        # MLB stats
        'HOMERUNS': 'HR',
        'HOMERUN': 'HR',
        'HR': 'HR',
        'HITS': 'H',
        'HIT': 'H',
        'H': 'H',
        'RBI': 'RBI',
        'RUNS': 'R',
        'RUN': 'R',
        'R': 'R',
        'STRIKEOUTS': 'K',
        'STRIKEOUT': 'K',
        'K': 'K',
        'WALKS': 'BB',
        'WALK': 'BB',
        'BB': 'BB',
        # NFL stats
        'PASSINGTOUCHDOWNS': 'PASSTD',
        'PASSTD': 'PASSTD',
        'PASSINGTD': 'PASSTD',
        'RUSHINGYARD': 'RUSHYD',
        'RUSHINGYARD': 'RUSHYD',
        'RUSHYD': 'RUSHYD'
    }
    
    # Handle special case: MLB events using "POINTS" should map to appropriate MLB stats
    if league == 'MLB' and original_stat == 'POINTS':
        # Default to hits for MLB when POINTS is used incorrectly
        corrected_stat = 'H'
        corrections.append(f"Fixed MLB stat type: 'POINTS' -> 'H' (MLB doesn't use points)")
    else:
        corrected_stat = stat_type_map.get(original_stat.upper(), original_stat.upper())
        if corrected_stat != original_stat.upper():
            corrections.append(f"Standardized stat type: '{original_stat}' -> '{corrected_stat}'")
    
    # Reconstruct the corrected event ID
    corrected_event_id = f"{date_part}-{league}-{clean_player}{threshold_part}{corrected_stat}"
    
    if corrected_event_id != original_event_id:
        corrections.append(f"Final correction: '{original_event_id}' -> '{corrected_event_id}'")
    
    return corrected_event_id, corrections

def get_all_objects():
    return pick_objects, boost_promo_objects, protected_promo_objects

# API Key Authentication Middleware
def validate_api_key(api_key: str) -> bool:
    """
    Validates the provided API key against the environment variable.
    
    Args:
        api_key (str): The API key to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    expected_key = os.getenv("EXTERNAL_API_KEY")
    if not expected_key:
        print("[ERROR] EXTERNAL_API_KEY not configured in environment variables")
        return False
    
    is_valid = api_key == expected_key
    if not is_valid:
        print(f"[WARNING] Invalid API key attempt from {request.remote_addr if request else 'unknown'}")
    
    return is_valid

def require_api_key(f):
    """
    Decorator to require API key authentication for endpoints.
    Expects X-API-Key header with valid API key.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            print(f"[WARNING] Missing API key in request from {request.remote_addr}")
            return jsonify({
                "success": False,
                "message": "Missing API key. Include X-API-Key header."
            }), 401
        
        if not validate_api_key(api_key):
            print(f"[WARNING] Invalid API key provided from {request.remote_addr}")
            return jsonify({
                "success": False,
                "message": "Invalid API key."
            }), 401
        
        return f(*args, **kwargs)
    return decorated_function

@app.route("/api/external/insert_picks", methods=["POST"])
@require_api_key
def insert_picks():
    """
    External API endpoint for inserting sports prediction data.
    Requires X-API-Key header for authentication.
    
    Expected JSON payload:
    {
        "picks": [
            {
                "league": "NBA|MLB|NFL|NHL",
                "team_a": "string",
                "team_b": "string", 
                "pick_type": "MoneyLine|Spread|Prop",
                "player_name": "string (optional)",
                "stat_type": "string (optional)",
                "stat_threshold": "number (optional)",
                "odds": "string (+150, -110, etc.)",
                "prediction": "0|1",
                "expert_name": "string",
                "expert_confidence": "number (0.0-1.0)",
                "event_date": "YYYY-MM-DD",
                "prediction_time": "YYYY-MM-DD HH:MM:SS (optional)"
            }
        ]
    }
    
    Returns:
    {
        "success": true|false,
        "message": "string",
        "results": {
            "total_picks": "number",
            "successful_inserts": "number", 
            "failed_inserts": "number",
            "events_created": "number",
            "events_updated": "number"
        },
        "errors": [
            {
                "pick_index": "number",
                "error": "string",
                "details": "string"
            }
        ]
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "message": "No JSON data provided"
            }), 400
        
        picks = data.get("picks", [])
        
        if not picks:
            return jsonify({
                "success": False,
                "message": "No picks provided in request"
            }), 400
        
        if not isinstance(picks, list):
            return jsonify({
                "success": False,
                "message": "Picks must be an array"
            }), 400
        
        print(f"[INFO] External API: Processing {len(picks)} picks from {request.remote_addr}")
        
        # Initialize response tracking
        results = {
            "total_picks": len(picks),
            "successful_inserts": 0,
            "failed_inserts": 0,
            "events_created": 0,
            "events_updated": 0
        }
        errors = []
        
        # Process each pick
        for pick_index, pick_data in enumerate(picks):
            try:
                print(f"[INFO] Processing pick {pick_index + 1}: {pick_data}")
                
                # Validate and clean pick data
                print(f"[DEBUG] Validating pick data: {pick_data}")
                cleaned_data, validation_errors = validate_and_clean_pick_data(pick_data)
                print(f"[DEBUG] Validation errors: {validation_errors}")
                print(f"[DEBUG] Cleaned data: {cleaned_data}")
                
                if validation_errors:
                    print(f"[ERROR] Validation failed for pick {pick_index}: {validation_errors}")
                    results["failed_inserts"] += 1
                    errors.append({
                        "pick_index": pick_index,
                        "error": "Validation failed",
                        "details": "; ".join(validation_errors)
                    })
                    continue
                
                # Generate event ID based on pick type
                if cleaned_data.get('pick_type') in ['Prop', 'Total']:
                    # For prop picks and team totals, use stat-based event ID
                    event_id = generate_admin_event_id(
                        event_date=cleaned_data['event_date'],
                        league=cleaned_data['league'],
                        pick_type=cleaned_data['pick_type'],
                        team_a=cleaned_data.get('team_a', ''),
                        team_b=cleaned_data.get('team_b', ''),
                        player_name=cleaned_data.get('player_name', ''),
                        stat_threshold=cleaned_data['stat_threshold'],
                        stat_type=cleaned_data['stat_type']
                    )
                    team_a = "Over"
                    team_b = "Under"
                    # Use properly formatted team name for player_team
                    player_team = cleaned_data.get('team_a', '')
                else:
                    # For team picks, use consistent hyphen format like admin forms
                    event_id = generate_admin_event_id(
                        event_date=cleaned_data['event_date'],
                        league=cleaned_data['league'],
                        pick_type=cleaned_data['pick_type'],
                        team_a=cleaned_data['team_a'],
                        team_b=cleaned_data['team_b'],
                        player_name=None,
                        stat_threshold=None,
                        stat_type=None
                    )
                    # Use properly formatted team names (they're already cleaned by validation)
                    team_a = cleaned_data['team_a']
                    team_b = cleaned_data['team_b']
                    player_team = cleaned_data.get('team_a', '')
                
                print(f"[INFO] Generated event_id: {event_id}")
                
                # Prepare expert predictions with improved data
                prediction = 0
                initial_confidence = cleaned_data.get('expert_confidence', 0.75)
                
                expert_predictions = [(
                    cleaned_data['expert_name'],
                    prediction,
                    initial_confidence
                )]
                
                # Debug logging for stat_threshold
                stat_threshold_value = cleaned_data.get('stat_threshold')
                pick_type_value = cleaned_data.get('pick_type')
                player_name_value = cleaned_data.get('player_name')
                
                print(f"[DEBUG] Pick type: '{pick_type_value}', Stat threshold: {stat_threshold_value}, Player name: '{player_name_value}'")
                
                # Submit event to database (without expert predictions for now)
                success, message = submit_event(
                    event_id=event_id,
                    event_date=cleaned_data['event_date'],
                    league=cleaned_data['league'],
                    team_a=team_a,
                    team_b=team_b,
                    crowd_probability=cleaned_data.get('crowd_probability', 0.5),
                    expert_predictions=[],  # Handle expert predictions separately below
                    actual_result=None,
                    pick_type=cleaned_data['pick_type'],
                    player_team=player_team,
                    stat_type=cleaned_data.get('stat_type', ''),
                    player_name=player_name_value,  # Always pass player_name if exists
                    stat_threshold=stat_threshold_value  # Always pass stat_threshold if exists
                )
                
                # Handle expert predictions with enhanced data
                # Allow expert predictions even if event creation failed due to existing event
                if success or "already exists" in message.lower():
                    try:
                        from data_science_modules.planet_scale_port import get_connection
                        conn = get_connection()
                        cursor = conn.cursor()
                        
                        # Prepare enhanced expert prediction data
                        expert_name = cleaned_data['expert_name']
                        
                        # Determine the team for this pick
                        if cleaned_data.get('pick_type') == 'Prop':
                            # For prop picks, use the player's team or first valid team
                            pick_team = player_team if player_team not in ['Over', 'Under', ''] else cleaned_data.get('team_a', '')
                        else:
                            # For team picks, use team_a
                            pick_team = cleaned_data.get('team_a', '')
                        
                        # Use event_date as game_date (this is the actual game date)
                        game_date = cleaned_data.get('event_date')
                        if game_date and 'T' in game_date:
                            # Extract just the date part if it's a full datetime
                            game_date = game_date.split('T')[0]
                        
                        # Set prediction_time from AI-parsed data or use fallback
                        if cleaned_data.get('prediction_time'):
                            prediction_time = cleaned_data['prediction_time']
                            print(f"[INFO] Using AI-parsed prediction_time: {prediction_time}")
                        else:
                            # Fallback: use game_date with default time in California timezone  
                            prediction_time = f"{game_date} 09:00:00"
                            print(f"[INFO] Using fallback prediction_time (9 AM PT): {prediction_time}")
                        
                        # Check if expert prediction already exists to prevent duplicates
                        cursor.execute("""
                            SELECT event_id FROM expert_predictions 
                            WHERE event_id = %s AND expert_name = %s
                        """, (event_id, expert_name))
                        existing_prediction = cursor.fetchone()
                        
                        if existing_prediction:
                            # Update existing expert prediction
                            print(f"[INFO] Updating existing expert prediction for {expert_name} on {event_id}")
                            cursor.execute("""
                                UPDATE expert_predictions SET 
                                    prediction = %s, confidence = %s, team = %s, 
                                    league = %s, game_date = %s, stat_threshold = %s, 
                                    prediction_time = %s
                                WHERE event_id = %s AND expert_name = %s
                            """, (
                                prediction,
                                initial_confidence,
                                pick_team,
                                cleaned_data['league'],
                                game_date,
                                cleaned_data.get('stat_threshold'),
                                prediction_time,
                                event_id,
                                expert_name
                            ))
                        else:
                            # Insert new expert prediction
                            print(f"[INFO] Inserting new expert prediction for {expert_name} on {event_id}")
                            cursor.execute("""
                                INSERT INTO expert_predictions (
                                    event_id, expert_name, prediction, confidence,
                                    team, league, game_date, stat_threshold, prediction_time
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                event_id,
                                expert_name,
                                prediction,
                                initial_confidence,
                                pick_team,
                                cleaned_data['league'],
                                game_date,
                                cleaned_data.get('stat_threshold'),
                                prediction_time
                            ))
                        
                        # Now calculate dynamic confidence using your algorithm
                        try:
                            dynamic_confidence_percentage = calculate_dynamic_confidence_vs_expert(
                                event_id, initial_confidence * 100
                            )
                            
                            print(f"[INFO] Dynamic confidence for {expert_name}: {initial_confidence*100:.1f}% → {dynamic_confidence_percentage:.1f}%")
                            
                            # Update with dynamic confidence if calculation succeeded
                            if dynamic_confidence_percentage != 50.0:  # 50.0 is the fallback value
                                dynamic_confidence_decimal = dynamic_confidence_percentage / 100.0
                                cursor.execute("""
                                    UPDATE expert_predictions 
                                    SET confidence = %s 
                                    WHERE event_id = %s AND expert_name = %s
                                """, (dynamic_confidence_decimal, event_id, expert_name))
                                print(f"[INFO] Updated {expert_name} confidence to {dynamic_confidence_percentage:.1f}%")
                            else:
                                print(f"[WARNING] Dynamic confidence calculation failed for {expert_name} - keeping original {initial_confidence*100:.1f}%")
                        
                        except Exception as conf_error:
                            import traceback
                            conf_traceback = traceback.format_exc()
                            print(f"[ERROR] Dynamic confidence calculation failed: {conf_error}")
                            print(f"[ERROR] Confidence calculation traceback: {conf_traceback}")
                            print(f"[INFO] Keeping original confidence {initial_confidence*100:.1f}% for {expert_name}")
                        
                        # Ensure expert exists in reliability table
                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = %s", (expert_name,))
                        if not cursor.fetchone():
                            cursor.execute("INSERT INTO expert_reliability (expert_name) VALUES (%s)", (expert_name,))
                        
                        conn.commit()
                        conn.close()
                        
                        print(f"[INFO] Successfully inserted enhanced expert prediction for {expert_name}")
                        
                        # Mark as successful if expert prediction was added (even if event existed)
                        expert_prediction_success = True
                        
                    except Exception as ep_error:
                        print(f"[ERROR] Failed to insert enhanced expert prediction: {ep_error}")
                        expert_prediction_success = False
                        # Fall back to basic expert prediction insertion
                        try:
                            conn = get_connection()
                            cursor = conn.cursor()
                            
                            # Set prediction_time for fallback as well
                            fallback_game_date = cleaned_data.get('event_date')
                            if fallback_game_date and 'T' in fallback_game_date:
                                fallback_game_date = fallback_game_date.split('T')[0]
                            
                            # Use same prediction_time logic for fallback
                            if cleaned_data.get('prediction_time'):
                                fallback_prediction_time = cleaned_data['prediction_time']
                            else:
                                fallback_prediction_time = f"{fallback_game_date} 09:00:00"
                            
                            # Check if expert prediction already exists (fallback version)
                            cursor.execute("""
                                SELECT event_id FROM expert_predictions 
                                WHERE event_id = %s AND expert_name = %s
                            """, (event_id, expert_name))
                            existing_fallback = cursor.fetchone()
                            
                            if existing_fallback:
                                # Update existing expert prediction
                                print(f"[INFO] Fallback: Updating existing expert prediction for {expert_name} on {event_id}")
                                cursor.execute("""
                                    UPDATE expert_predictions SET 
                                        prediction = %s, confidence = %s, prediction_time = %s
                                    WHERE event_id = %s AND expert_name = %s
                                """, (prediction, initial_confidence, fallback_prediction_time, event_id, expert_name))
                            else:
                                # Insert new expert prediction
                                print(f"[INFO] Fallback: Inserting new expert prediction for {expert_name} on {event_id}")
                                cursor.execute("""
                                    INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence, prediction_time)
                                    VALUES (%s, %s, %s, %s, %s)
                                """, (event_id, expert_name, prediction, initial_confidence, fallback_prediction_time))
                            
                            cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = %s", (expert_name,))
                            if not cursor.fetchone():
                                cursor.execute("INSERT INTO expert_reliability (expert_name) VALUES (%s)", (expert_name,))
                            
                            conn.commit()
                            conn.close()
                            print(f"[INFO] Inserted basic expert prediction as fallback for {expert_name}")
                            expert_prediction_success = True
                        except Exception as fallback_error:
                            print(f"[ERROR] Even fallback expert prediction insertion failed: {fallback_error}")
                            expert_prediction_success = False
                else:
                    print(f"[ERROR] Event creation failed, skipping expert predictions: {message}")
                    expert_prediction_success = False
                
                # Determine overall success for this pick
                pick_success = success or ("already exists" in message.lower() and expert_prediction_success)
                
                if pick_success:
                    if success:
                        print(f"[INFO] Successfully inserted pick {pick_index + 1}: {message}")
                        results["events_created"] += 1
                    else:
                        print(f"[INFO] Successfully inserted pick {pick_index + 1}: Event, crowd prediction, and expert predictions submitted successfully.")
                        results["events_updated"] += 1
                    results["successful_inserts"] += 1
                else:
                    print(f"[ERROR] Failed to insert pick {pick_index + 1}: {message}")
                    results["failed_inserts"] += 1
                    errors.append({
                        "pick_index": pick_index,
                        "error": "Database insertion failed",
                        "details": message
                    })
                
            except Exception as pick_error:
                import traceback
                pick_traceback = traceback.format_exc()
                print(f"[ERROR] Failed to process pick {pick_index}: {pick_error}")
                print(f"[ERROR] Pick processing traceback: {pick_traceback}")
                results["failed_inserts"] += 1
                errors.append({
                    "pick_index": pick_index,
                    "error": str(pick_error),
                    "details": f"Error processing pick data: {pick_data}",
                    "traceback": pick_traceback
                })
        
        # Determine overall success
        success = results["failed_inserts"] == 0
        
        if success:
            message = f"Successfully processed all {results['total_picks']} picks"
        else:
            message = f"Processed {results['successful_inserts']}/{results['total_picks']} picks successfully"
        
        print(f"[INFO] External API completed: {message}")
        
        return jsonify({
            "success": success,
            "message": message,
            "results": results,
            "errors": errors
        })
        
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(f"[ERROR] External API endpoint error: {e}")
        print(f"[ERROR] Full traceback: {error_traceback}")
        return jsonify({
            "success": False,
            "message": "Internal server error",
            "results": {
                "total_picks": 0,
                "successful_inserts": 0,
                "failed_inserts": 0,
                "events_created": 0,
                "events_updated": 0
            },
            "errors": [{
                "pick_index": -1,
                "error": "Internal server error",
                "details": str(e),
                "traceback": error_traceback
            }]
        }), 500

@app.route("/api/external/mlb_projections", methods=["POST"])
@require_api_key
def insert_mlb_projections():
    """
    External API endpoint for inserting/updating MLB projection sheet data.
    Requires X-API-Key header for authentication.
    
    Expected JSON payload:
    {
        "projections": [
            {
                "game_date": "YYYY-MM-DD",
                "away_team": "string",
                "home_team": "string",
                "away_sp": "string",
                "home_sp": "string",
                "away_score": decimal,
                "home_score": decimal,
                "total_runs_scored": decimal,
                "home_team_run_diff": decimal,
                "winning_team": "string",
                "game_xHRs": decimal,
                "home_win_pct": decimal,
                "xRFI": decimal,
                "player_name": "string",
                "is_pitcher": 0|1,
                "AB": decimal,
                "Hits": decimal,
                "Singles": decimal,
                "Doubles": decimal,
                "Triples": decimal,
                "Homeruns": decimal,
                "TB": decimal,
                "Walks": decimal,
                "SB": decimal,
                "Runs": decimal,
                "RBIs": decimal,
                "HRR": decimal,
                "OBP": decimal,
                "HitterStrikeouts": decimal,
                "Pitches": decimal,
                "Outs": decimal,
                "BF": decimal,
                "Strikeouts": decimal,
                "PitcherWalks": decimal,
                "ER": decimal,
                "HA": decimal,
                "xRFI_Chance": decimal
            }
        ]
    }
    
    Returns:
    {
        "success": true|false,
        "message": "string",
        "results": {
            "total_projections": number,
            "successful_inserts": number,
            "successful_updates": number,
            "failed_operations": number
        },
        "errors": [...]
    }
    """
    try:
        # Robust JSON parsing with fallback
        try:
            if not request.is_json and request.content_type != 'application/json':
                print(f"[WARNING] Invalid content type: {request.content_type}")
            data = request.get_json(force=True, silent=False)
        except (UnicodeDecodeError, ValueError, BadRequest) as json_error:
            print(f"[ERROR] JSON parsing failed: {json_error}")
            raw_data = request.get_data(as_text=True)
            print(f"[DEBUG] Raw request data (first 200 chars): {raw_data[:200] if raw_data else 'None'}")
            # Repair possible unquoted keys
            raw_data = repair_json(raw_data)
            # Attempt to wrap if it's comma-separated objects without array
            if raw_data.strip().startswith('{') and ',' in raw_data and raw_data.count('{') > 1:
                raw_data = '[' + raw_data + ']'
            # Attempt to parse raw body JSON, including nested strings
            try:
                temp = json.loads(raw_data)
                # If it's a string, it means payload was double-encoded
                if isinstance(temp, str):
                    print("[DEBUG] Raw JSON is a string, attempting nested parse")
                    temp = json.loads(temp)
                    print("[DEBUG] Successfully parsed nested JSON string")
                data = temp
                print("[DEBUG] Fallback JSON parsing succeeded")
            except Exception as fallback_error:
                print(f"[ERROR] Fallback JSON parsing failed: {fallback_error}")
                return jsonify({
                    "success": False,
                    "message": "Invalid JSON format in request body",
                    "error_type": "json_parse_error",
                    "error_details": str(json_error)
                }), 400
         
        if not data:
            return jsonify({
                "success": False,
                "message": "No JSON data provided or empty request body"
            }), 400
        # Support top-level array or object with 'projections'
        if isinstance(data, list):
            # Check if list contains objects with 'projections' keys
            if len(data) > 0 and isinstance(data[0], dict) and "projections" in data[0]:
                # Extract projections from the first item (Make.com format)
                projections = data[0]["projections"]
            else:
                projections = data
        elif isinstance(data, dict):
            projections = data.get("projections", [])
        else:
            return jsonify({
                "success": False,
                "message": "Invalid request payload: expected 'projections' array or top-level array",
                "error_type": "json_parse_error"
            }), 400
        
        # Additional handling: if projections is dict, convert appropriately
        if isinstance(projections, dict):
            # If it has a 'projections' key, extract the array from it
            if "projections" in projections:
                projections = projections["projections"]
            # If it looks like a single projection object (contains game_date), wrap in list
            elif "game_date" in projections:
                projections = [projections]
            # If keys are numeric (e.g. "0", "1"), treat values as list
            elif all(str(k).isdigit() for k in projections.keys()):
                projections = list(projections.values())
            else:
                # Special handling for Make.com format: object with game metadata + array of player objects
                # Check if it contains both game metadata and player-specific fields
                game_fields = ['game_date', 'away_team', 'home_team']
                player_fields = ['player_name', 'is_pitcher']
                
                has_game_data = any(field in projections for field in game_fields)
                has_player_data = any(field in projections for field in player_fields)
                
                if has_game_data and has_player_data:
                    # This is the first object with combined game + player data
                    projections = [projections]
                else:
                    # Fallback: wrap entire dict inside list
                    projections = [projections]
        
        if not projections:
            return jsonify({
                "success": False,
                "message": "No projections provided in request"
            }), 400
        
        if not isinstance(projections, list):
            return jsonify({
                "success": False,
                "message": "Projections must be an array"
            }), 400
        
        print(f"[INFO] MLB Projections API: Processing {len(projections)} projections from {request.remote_addr}")
        print(f"[DEBUG] First projection sample: {projections[0] if projections else 'None'}")
        
        # Handle the case where game metadata is only in the first object
        # and subsequent objects only contain player data
        if len(projections) > 0:
            # Find the most complete projection with game metadata
            game_metadata = {}
            metadata_fields = [
                'game_date', 'away_team', 'home_team', 'away_sp', 'home_sp',
                'away_score', 'home_score', 'total_runs_scored', 'home_team_run_diff',
                'winning_team', 'game_xHRs', 'home_win_pct', 'xRFI'
            ]
            
            # First, collect all available metadata from all projections
            for projection in projections:
                for field in metadata_fields:
                    if field in projection and projection[field] is not None:
                        if field not in game_metadata:
                            game_metadata[field] = projection[field]
            
            # Apply game metadata to all projections that don't have it
            for i, projection in enumerate(projections):
                for field, value in game_metadata.items():
                    if field not in projection or projection[field] is None or projection[field] == "":
                        projections[i][field] = value
        
        results = {
            "total_projections": len(projections),
            "successful_inserts": 0,
            "successful_updates": 0,
            "failed_operations": 0
        }
        errors = []
        
        conn = get_connection()
        cursor = conn.cursor()
        
        def clean_projection_data(projection):
            """Clean and convert projection data for database insertion."""
            cleaned = {}
            
            for key, value in projection.items():
                # Convert empty strings to None
                if value == "" or value is None:
                    cleaned[key] = None
                # Handle percentage values
                elif isinstance(value, str) and value.endswith('%'):
                    try:
                        # Convert "97.00%" to 97.00 (keep as percentage for database)
                        cleaned[key] = float(value.rstrip('%'))
                    except ValueError:
                        cleaned[key] = None
                # Handle numeric strings
                elif isinstance(value, str) and key not in ['game_date', 'away_team', 'home_team', 'away_sp', 'home_sp', 'winning_team', 'player_name']:
                    try:
                        # Try to convert to float if it's a numeric field
                        if '.' in value or value.isdigit() or (value.startswith('-') and value[1:].replace('.', '').isdigit()):
                            numeric_value = float(value)
                            # Check for reasonable ranges to catch potential data issues
                            if abs(numeric_value) > 1000000:  # Sanity check for extremely large values
                                print(f"[WARNING] Unusually large value for {key}: {numeric_value}")
                            cleaned[key] = numeric_value
                        else:
                            cleaned[key] = value
                    except (ValueError, OverflowError):
                        print(f"[WARNING] Could not convert {key} value '{value}' to number, keeping as string")
                        cleaned[key] = value
                else:
                    cleaned[key] = value
                
                # Normalize game_date to YYYY-MM-DD format
                if key == 'game_date' and isinstance(cleaned[key], str):
                    # Handle various date formats
                    if 'T' in cleaned[key]:
                        # ISO format: 2025-07-27T00:00:00.000Z
                        cleaned[key] = cleaned[key].split('T')[0]
                    elif ' ' in cleaned[key]:
                        # Format with space: 2025-07-27 00:00:00
                        cleaned[key] = cleaned[key].split(' ')[0]
                    # Ensure it's a valid date format (YYYY-MM-DD)
                    try:
                        from datetime import datetime
                        datetime.strptime(cleaned[key], '%Y-%m-%d')
                    except ValueError:
                        print(f"[WARNING] Invalid date format for {key}: {cleaned[key]}")
                        cleaned[key] = None
            
            return cleaned
        
        for idx, projection in enumerate(projections):
            try:
                # Clean the projection data first
                cleaned_projection = clean_projection_data(projection)
                
                # Extract required fields from cleaned data
                game_date = cleaned_projection.get("game_date")
                away_team = cleaned_projection.get("away_team")
                home_team = cleaned_projection.get("home_team")
                player_name = cleaned_projection.get("player_name")
                
                # Validate required fields
                if not all([game_date, away_team, home_team, player_name]):
                    missing_fields = []
                    if not game_date: missing_fields.append("game_date")
                    if not away_team: missing_fields.append("away_team")
                    if not home_team: missing_fields.append("home_team")
                    if not player_name: missing_fields.append("player_name")
                    
                    errors.append({
                        "projection_index": idx,
                        "error": "Missing required fields",
                        "details": f"Missing: {', '.join(missing_fields)}"
                    })
                    results["failed_operations"] += 1
                    continue
                
                # Check if record exists (manual upsert without table constraints)
                check_query = """
                    SELECT projection_id FROM mlb_projection_sheet 
                    WHERE game_date = %s AND away_team = %s AND home_team = %s AND player_name = %s
                    LIMIT 1
                """
                
                try:
                    cursor.execute(check_query, (game_date, away_team, home_team, player_name))
                    existing_record = cursor.fetchone()
                except Exception as check_error:
                    print(f"[ERROR] Failed to check for existing record for {player_name}: {check_error}")
                    existing_record = None
                
                if existing_record:
                    # Update existing record
                    update_query = """
                        UPDATE mlb_projection_sheet SET
                            away_sp = %s, home_sp = %s, away_score = %s, home_score = %s,
                            total_runs_scored = %s, home_team_run_diff = %s, winning_team = %s,
                            game_xHRs = %s, home_win_pct = %s, xRFI = %s, is_pitcher = %s,
                            AB = %s, Hits = %s, Singles = %s, Doubles = %s, Triples = %s,
                            Homeruns = %s, TB = %s, Walks = %s, SB = %s, Runs = %s,
                            RBIs = %s, HRR = %s, OBP = %s, HitterStrikeouts = %s,
                            Pitches = %s, Outs = %s, BF = %s, Strikeouts = %s,
                            PitcherWalks = %s, ER = %s, HA = %s, xRFI_Chance = %s,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE game_date = %s AND away_team = %s AND home_team = %s AND player_name = %s
                    """
                    
                    update_values = (
                        cleaned_projection.get("away_sp"), cleaned_projection.get("home_sp"),
                        cleaned_projection.get("away_score"), cleaned_projection.get("home_score"),
                        cleaned_projection.get("total_runs_scored"), cleaned_projection.get("home_team_run_diff"),
                        cleaned_projection.get("winning_team"), cleaned_projection.get("game_xHRs"),
                        cleaned_projection.get("home_win_pct"), cleaned_projection.get("xRFI"),
                        cleaned_projection.get("is_pitcher"), cleaned_projection.get("AB"),
                        cleaned_projection.get("Hits"), cleaned_projection.get("Singles"),
                        cleaned_projection.get("Doubles"), cleaned_projection.get("Triples"),
                        cleaned_projection.get("Homeruns"), cleaned_projection.get("TB"),
                        cleaned_projection.get("Walks"), cleaned_projection.get("SB"),
                        cleaned_projection.get("Runs"), cleaned_projection.get("RBIs"),
                        cleaned_projection.get("HRR"), cleaned_projection.get("OBP"),
                        cleaned_projection.get("HitterStrikeouts"), cleaned_projection.get("Pitches"),
                        cleaned_projection.get("Outs"), cleaned_projection.get("BF"),
                        cleaned_projection.get("Strikeouts"), cleaned_projection.get("PitcherWalks"),
                        cleaned_projection.get("ER"), cleaned_projection.get("HA"),
                        cleaned_projection.get("xRFI_Chance"),
                        # WHERE clause values
                        game_date, away_team, home_team, player_name
                    )
                    
                    try:
                        cursor.execute(update_query, update_values)
                        results["successful_updates"] += 1
                        print(f"[INFO] Updated projection for {player_name}")
                    except Exception as update_error:
                        print(f"[ERROR] Failed to update {player_name}: {update_error}")
                        results["failed_operations"] += 1
                        errors.append({
                            "projection_index": idx,
                            "player_name": player_name,
                            "operation": "update",
                            "error": str(update_error),
                            "error_type": type(update_error).__name__
                        })
                        continue
                    
                else:
                    # Insert new record
                    insert_query = """
                        INSERT INTO mlb_projection_sheet (
                            game_date, away_team, home_team, away_sp, home_sp,
                            away_score, home_score, total_runs_scored, home_team_run_diff,
                            winning_team, game_xHRs, home_win_pct, xRFI, player_name,
                            is_pitcher, AB, Hits, Singles, Doubles, Triples, Homeruns,
                            TB, Walks, SB, Runs, RBIs, HRR, OBP, HitterStrikeouts,
                            Pitches, Outs, BF, Strikeouts, PitcherWalks, ER, HA, xRFI_Chance
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s
                        )
                    """
                    
                    insert_values = (
                        cleaned_projection.get("game_date"), cleaned_projection.get("away_team"),
                        cleaned_projection.get("home_team"), cleaned_projection.get("away_sp"),
                        cleaned_projection.get("home_sp"), cleaned_projection.get("away_score"),
                        cleaned_projection.get("home_score"), cleaned_projection.get("total_runs_scored"),
                        cleaned_projection.get("home_team_run_diff"), cleaned_projection.get("winning_team"),
                        cleaned_projection.get("game_xHRs"), cleaned_projection.get("home_win_pct"),
                        cleaned_projection.get("xRFI"), cleaned_projection.get("player_name"),
                        cleaned_projection.get("is_pitcher"), cleaned_projection.get("AB"),
                        cleaned_projection.get("Hits"), cleaned_projection.get("Singles"),
                        cleaned_projection.get("Doubles"), cleaned_projection.get("Triples"),
                        cleaned_projection.get("Homeruns"), cleaned_projection.get("TB"),
                        cleaned_projection.get("Walks"), cleaned_projection.get("SB"),
                        cleaned_projection.get("Runs"), cleaned_projection.get("RBIs"),
                        cleaned_projection.get("HRR"), cleaned_projection.get("OBP"),
                        cleaned_projection.get("HitterStrikeouts"), cleaned_projection.get("Pitches"),
                        cleaned_projection.get("Outs"), cleaned_projection.get("BF"),
                        cleaned_projection.get("Strikeouts"), cleaned_projection.get("PitcherWalks"),
                        cleaned_projection.get("ER"), cleaned_projection.get("HA"),
                        cleaned_projection.get("xRFI_Chance")
                    )
                    
                    try:
                        cursor.execute(insert_query, insert_values)
                        results["successful_inserts"] += 1
                        print(f"[INFO] Inserted new projection for {player_name}")
                    except Exception as insert_error:
                        print(f"[ERROR] Failed to insert {player_name}: {insert_error}")
                        results["failed_operations"] += 1
                        errors.append({
                            "projection_index": idx,
                            "player_name": player_name,
                            "operation": "insert",
                            "error": str(insert_error),
                            "error_type": type(insert_error).__name__
                        })
                        continue
                
            except Exception as projection_error:
                print(f"[ERROR] Failed to process projection {idx}: {projection_error}")
                print(f"[ERROR] Player: {cleaned_projection.get('player_name', 'Unknown')}")
                print(f"[ERROR] Game: {cleaned_projection.get('game_date')} {cleaned_projection.get('away_team')} @ {cleaned_projection.get('home_team')}")
                results["failed_operations"] += 1
                errors.append({
                    "projection_index": idx,
                    "player_name": cleaned_projection.get("player_name"),
                    "game_info": f"{cleaned_projection.get('game_date')} {cleaned_projection.get('away_team')} @ {cleaned_projection.get('home_team')}",
                    "error": str(projection_error),
                    "error_type": type(projection_error).__name__
                })
        
        try:
            conn.commit()
            print(f"[INFO] Database transaction committed successfully")
        except Exception as commit_error:
            print(f"[ERROR] Failed to commit transaction: {commit_error}")
            conn.rollback()
        finally:
            conn.close()
        
        total_processed = results['successful_inserts'] + results['successful_updates']
        print(f"[INFO] MLB Projections API completed: Processed {total_processed}/{results['total_projections']} projections successfully")
        
        if errors:
            print(f"[WARNING] {len(errors)} projections failed to process")
            for error in errors[:3]:  # Log first 3 errors for debugging
                print(f"[ERROR] {error}")
            if len(errors) > 3:
                print(f"[INFO] ... and {len(errors) - 3} more errors (check response for details)")
        
        # Determine overall success
        success = results["failed_operations"] == 0
        
        if success:
            message = f"Successfully processed all {results['total_projections']} projections"
        else:
            successful_ops = results["successful_inserts"] + results["successful_updates"]
            message = f"Processed {successful_ops}/{results['total_projections']} projections successfully"
        
        print(f"[INFO] MLB Projections API completed: {message}")
        
        return jsonify({
            "success": success,
            "message": message,
            "results": results,
            "errors": errors
        })
        
    except Exception as e:
        print(f"[ERROR] MLB Projections API endpoint error: {e}")
        return jsonify({
            "success": False,
            "message": "Internal server error",
            "results": {
                "total_projections": 0,
                "successful_inserts": 0,
                "successful_updates": 0,
                "failed_operations": 0
            },
            "errors": [{
                "projection_index": -1,
                "error": "Internal server error",
                "details": str(e)
            }]
        }), 500


@app.route("/api/external/process_mlb_projections", methods=["POST"])
@require_api_key
def process_mlb_projections():
    """
    External API endpoint for processing raw MLB projection sheet images from Discord scraper.
    Handles OCR, AI parsing, and database insertion in one endpoint.
    
    Expected JSON payload:
    {
        "batch_type": "string",
        "total_images": number,
        "images": [
            {
                "channel": "string",
                "content": "string", 
                "timestamp": "string",
                "image_url": "string",
                "image_filename": "string",
                "is_historical": boolean,
                "message_id": "string",
                "author": "string"
            }
        ],
        "scrape_timestamp": "string"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "message": "No JSON data provided"
            }), 400
        
        images = data.get("images", [])
        
        if not images:
            return jsonify({
                "success": False,
                "message": "No images provided in request"
            }), 400
        
        print(f"[INFO] Processing {len(images)} MLB projection images from {request.remote_addr}")
        
        # Get Google Cloud Vision API key
        google_api_key = os.getenv("GOOGLE_CLOUD_VISION_API_KEY")
        if not google_api_key:
            raise Exception("GOOGLE_CLOUD_VISION_API_KEY not configured")
        
        all_projections = []
        processing_errors = []
        
        for idx, image_data in enumerate(images):
            try:
                image_url = image_data.get("image_url")
                timestamp = image_data.get("timestamp")
                
                if not image_url:
                    processing_errors.append({
                        "image_index": idx,
                        "error": "Missing image_url"
                    })
                    continue
                
                # Step 1: OCR with Google Cloud Vision REST API
                print(f"[INFO] Processing OCR for image {idx + 1}/{len(images)}")
                
                vision_payload = {
                    "requests": [
                        {
                            "image": {
                                "source": {
                                    "imageUri": image_url
                                }
                            },
                            "features": [
                                {
                                    "type": "TEXT_DETECTION"
                                }
                            ]
                        }
                    ]
                }
                
                vision_response = requests.post(
                    f"https://vision.googleapis.com/v1/images:annotate?key={google_api_key}",
                    headers={"Content-Type": "application/json"},
                    json=vision_payload,
                    timeout=30
                )
                
                if vision_response.status_code != 200:
                    raise Exception(f"Google Vision API error: {vision_response.status_code} - {vision_response.text}")
                
                vision_result = vision_response.json()
                
                if "responses" not in vision_result or not vision_result["responses"]:
                    processing_errors.append({
                        "image_index": idx,
                        "error": "No response from Google Vision API"
                    })
                    continue
                
                response_data = vision_result["responses"][0]
                
                if "error" in response_data:
                    raise Exception(f'OCR Error: {response_data["error"]["message"]}')
                
                text_annotations = response_data.get("textAnnotations", [])
                
                if not text_annotations:
                    processing_errors.append({
                        "image_index": idx,
                        "error": "No text detected in image"
                    })
                    continue
                
                full_text = text_annotations[0].get("description", "") if text_annotations else ""
                
                # Step 2: Check if this is a projection sheet (contains "Total Runs Scored:")
                if "Total Runs Scored:" not in full_text:
                    print(f"[INFO] Skipping image {idx + 1} - not a projection sheet")
                    continue
                
                # Step 3: Parse with OpenRouter AI
                print(f"[INFO] Parsing projection data for image {idx + 1}/{len(images)}")
                
                openrouter_payload = {
                    "reasoning": {"effort": "high", "exclude": True, "enabled": True},
                    "model": "google/gemini-2.5-flash",
                    "provider": {"order": ["google-ai-studio"]},
                    "stream": False,
                    "messages": [
                        {
                            "role": "system",
                            "content": 'You are an accurate parser for complete MLB projection sheets. Return ONLY valid JSON matching {"projections":[{"game_date":"","away_team":"","home_team":"","away_sp":"","home_sp":"","away_score":"","home_score":"","total_runs_scored":"","home_team_run_diff":"","winning_team":"","game_xHRs":"","home_win_pct":"","xRFI":"","player_name":"","is_pitcher":"","AB":"","Hits":"","Singles":"","Doubles":"","Triples":"","Homeruns":"","TB":"","Walks":"","SB":"","Runs":"","RBIs":"","HRR":"","OBP":"","HitterStrikeouts":"","Pitches":"","Outs":"","BF":"","Strikeouts":"","PitcherWalks":"","ER":"","HA":"","xRFI_Chance":""}]}. Extract EVERY player (hitters AND pitchers) with ALL their projected stats. For each player: set away_team and home_team based on the matchup, populate ALL available stat fields, is_pitcher=1 for pitchers/0 for hitters.'
                        },
                        {
                            "role": "user",
                            "content": f'Parse this COMPLETE MLB projection sheet. Extract ALL players (both hitters and pitchers) with their full projected stats. It is CRITICAL that you respond with entirely correct and valid JSON syntax. And, do not wrap the JSON in a markdown codeblock (starting with ```json and ending with ```. Instead, just return the value inside of the codeblock, not wrapped. Current datetime reference: \'{datetime.now().isoformat()}\' . Timestamp from image: {timestamp}. \n\nProjection Sheet Text:\n{full_text.strip()}'
                        }
                    ]
                }
                
                openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
                if not openrouter_api_key:
                    raise Exception("OPENROUTER_API_KEY not configured")
                
                openrouter_response = requests.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {openrouter_api_key}",
                        "Content-Type": "application/json"
                    },
                    json=openrouter_payload,
                    timeout=60
                )
                
                if openrouter_response.status_code != 200:
                    raise Exception(f"OpenRouter API error: {openrouter_response.status_code} - {openrouter_response.text}")
                
                ai_result = openrouter_response.json()
                raw_content = ai_result["choices"][0]["message"]["content"]
                
                # Clean and parse the AI response
                cleaned_content = raw_content.replace("```json", "").replace("```", "").strip()
                
                try:
                    parsed_projections = json.loads(cleaned_content)
                    projections = parsed_projections.get("projections", [])
                    
                    if projections:
                        all_projections.extend(projections)
                        print(f"[INFO] Successfully parsed {len(projections)} projections from image {idx + 1}")
                    else:
                        processing_errors.append({
                            "image_index": idx,
                            "error": "No projections found in AI response"
                        })
                        
                except json.JSONDecodeError as e:
                    processing_errors.append({
                        "image_index": idx,
                        "error": f"Invalid JSON from AI: {str(e)}"
                    })
                    
            except Exception as e:
                print(f"[ERROR] Failed to process image {idx}: {e}")
                processing_errors.append({
                    "image_index": idx,
                    "error": str(e)
                })
        
        # Step 4: Insert projections into database using existing logic
        if all_projections:
            print(f"[INFO] Inserting {len(all_projections)} total projections into database")
            
            results = {
                "total_projections": len(all_projections),
                "successful_inserts": 0,
                "successful_updates": 0, 
                "failed_operations": 0
            }
            db_errors = []
            
            conn = get_connection()
            cursor = conn.cursor()
            
            for idx, projection in enumerate(all_projections):
                try:
                    # Validate required fields
                    game_date = projection.get("game_date")
                    away_team = projection.get("away_team")
                    home_team = projection.get("home_team")
                    player_name = projection.get("player_name")
                    
                    if not all([game_date, away_team, home_team, player_name]):
                        missing_fields = []
                        if not game_date: missing_fields.append("game_date")
                        if not away_team: missing_fields.append("away_team")
                        if not home_team: missing_fields.append("home_team")
                        if not player_name: missing_fields.append("player_name")
                        
                        db_errors.append({
                            "projection_index": idx,
                            "error": "Missing required fields",
                            "details": f"Missing: {', '.join(missing_fields)}"
                        })
                        results["failed_operations"] += 1
                        continue
                    
                    # Check if record exists
                    check_query = """
                        SELECT projection_id FROM mlb_projection_sheet 
                        WHERE game_date = %s AND away_team = %s AND home_team = %s AND player_name = %s
                        LIMIT 1
                    """
                    
                    cursor.execute(check_query, (game_date, away_team, home_team, player_name))
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # Update existing record
                        update_query = """
                            UPDATE mlb_projection_sheet SET
                            away_sp = %s, home_sp = %s, away_score = %s, home_score = %s,
                            total_runs_scored = %s, home_team_run_diff = %s, winning_team = %s,
                            game_xHRs = %s, home_win_pct = %s, xRFI = %s, is_pitcher = %s,
                            AB = %s, Hits = %s, Singles = %s, Doubles = %s, Triples = %s,
                            Homeruns = %s, TB = %s, Walks = %s, SB = %s, Runs = %s,
                            RBIs = %s, HRR = %s, OBP = %s, HitterStrikeouts = %s,
                            Pitches = %s, Outs = %s, BF = %s, Strikeouts = %s,
                            PitcherWalks = %s, ER = %s, HA = %s, xRFI_Chance = %s
                            WHERE game_date = %s AND away_team = %s AND home_team = %s AND player_name = %s
                        """
                        
                        update_values = (
                            projection.get("away_sp"), projection.get("home_sp"),
                            projection.get("away_score"), projection.get("home_score"),
                            projection.get("total_runs_scored"), projection.get("home_team_run_diff"),
                            projection.get("winning_team"), projection.get("game_xHRs"),
                            projection.get("home_win_pct"), projection.get("xRFI"),
                            projection.get("is_pitcher"), projection.get("AB"),
                            projection.get("Hits"), projection.get("Singles"),
                            projection.get("Doubles"), projection.get("Triples"),
                            projection.get("Homeruns"), projection.get("TB"),
                            projection.get("Walks"), projection.get("SB"),
                            projection.get("Runs"), projection.get("RBIs"),
                            projection.get("HRR"), projection.get("OBP"),
                            projection.get("HitterStrikeouts"), projection.get("Pitches"),
                            projection.get("Outs"), projection.get("BF"),
                            projection.get("Strikeouts"), projection.get("PitcherWalks"),
                            projection.get("ER"), projection.get("HA"),
                            projection.get("xRFI_Chance"), game_date, away_team, home_team, player_name
                        )
                        
                        cursor.execute(update_query, update_values)
                        results["successful_updates"] += 1
                    else:
                        # Insert new record
                        insert_query = """
                            INSERT INTO mlb_projection_sheet (
                                game_date, away_team, home_team, away_sp, home_sp, away_score,
                                home_score, total_runs_scored, home_team_run_diff, winning_team,
                                game_xHRs, home_win_pct, xRFI, player_name, is_pitcher, AB,
                                Hits, Singles, Doubles, Triples, Homeruns, TB, Walks, SB,
                                Runs, RBIs, HRR, OBP, HitterStrikeouts, Pitches, Outs, BF,
                                Strikeouts, PitcherWalks, ER, HA, xRFI_Chance
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        insert_values = (
                            projection.get("game_date"), projection.get("away_team"),
                            projection.get("home_team"), projection.get("away_sp"),
                            projection.get("home_sp"), projection.get("away_score"),
                            projection.get("home_score"), projection.get("total_runs_scored"),
                            projection.get("home_team_run_diff"), projection.get("winning_team"),
                            projection.get("game_xHRs"), projection.get("home_win_pct"),
                            projection.get("xRFI"), projection.get("player_name"),
                            projection.get("is_pitcher"), projection.get("AB"),
                            projection.get("Hits"), projection.get("Singles"),
                            projection.get("Doubles"), projection.get("Triples"),
                            projection.get("Homeruns"), projection.get("TB"),
                            projection.get("Walks"), projection.get("SB"),
                            projection.get("Runs"), projection.get("RBIs"),
                            projection.get("HRR"), projection.get("OBP"),
                            projection.get("HitterStrikeouts"), projection.get("Pitches"),
                            projection.get("Outs"), projection.get("BF"),
                            projection.get("Strikeouts"), projection.get("PitcherWalks"),
                            projection.get("ER"), projection.get("HA"),
                            projection.get("xRFI_Chance")
                        )
                        
                        cursor.execute(insert_query, insert_values)
                        results["successful_inserts"] += 1
                        
                except Exception as projection_error:
                    print(f"[ERROR] Failed to process projection {idx}: {projection_error}")
                    results["failed_operations"] += 1
                    db_errors.append({
                        "projection_index": idx,
                        "error": str(projection_error)
                    })
            
            conn.commit()
            conn.close()
            
            # Combine all errors
            all_errors = processing_errors + db_errors
            
            success = results["failed_operations"] == 0 and len(processing_errors) == 0
            
            if success:
                message = f"Successfully processed all {len(images)} images and {results['total_projections']} projections"
            else:
                successful_ops = results["successful_inserts"] + results["successful_updates"]
                message = f"Processed {len(images)} images, {successful_ops}/{results['total_projections']} projections successfully"
            
            print(f"[INFO] MLB Projection Processing completed: {message}")
            
            return jsonify({
                "success": success,
                "message": message,
                "results": {
                    "images_processed": len(images),
                    "images_with_errors": len(processing_errors),
                    "total_projections": results["total_projections"],
                    "successful_inserts": results["successful_inserts"],
                    "successful_updates": results["successful_updates"],
                    "failed_operations": results["failed_operations"]
                },
                "errors": all_errors
            })
        
        else:
            return jsonify({
                "success": False,
                "message": "No valid projections found in any images",
                "results": {
                    "images_processed": len(images),
                    "images_with_errors": len(processing_errors),
                    "total_projections": 0,
                    "successful_inserts": 0,
                    "successful_updates": 0,
                    "failed_operations": 0
                },
                "errors": processing_errors
            })
            
    except Exception as e:
        print(f"[ERROR] MLB Projection Processing endpoint error: {e}")
        return jsonify({
            "success": False,
            "message": "Internal server error",
            "results": {
                "images_processed": 0,
                "images_with_errors": 0,
                "total_projections": 0,
                "successful_inserts": 0,
                "successful_updates": 0,
                "failed_operations": 0
            },
            "errors": [{
                "error": "Internal server error",
                "details": str(e)
            }]
        }), 500


@app.route("/api/get_picks", methods=["GET"])
def get_picks():
    return jsonify({
    "objects": [p.to_dict() for p in pick_objects]
    })

@app.route("/api/settings/date", methods=["GET"])
def get_date_setting():
    """
    Get the current default date setting with comprehensive error handling.
    
    Returns:
        JSON response with current date setting and fallback handling
    """
    try:
        current_date = get_default_date()
        
        # Validate the stored date before returning it
        is_valid, sanitized_date, error_message = validate_date_format(current_date)
        
        if not is_valid:
            print(f"[WARNING] Stored date is invalid: {current_date}, error: {error_message}")
            # Reset to current California date
            global current_default_date
            current_default_date = get_current_california_date()
            current_date = current_default_date
            
            return jsonify({
                "success": True,
                "date": current_date,
                "warning": f"Date was reset to default due to invalid stored value: {error_message}",
                "auto_update": auto_update_enabled
            })
        
        return jsonify({
            "success": True,
            "date": current_date,
            "last_updated": datetime.now().isoformat(),
            "auto_update": auto_update_enabled
        })
        
    except Exception as e:
        print(f"[ERROR] Failed to get date setting: {e}")
        
        # Graceful fallback to current California date
        fallback_date = get_current_california_date()
        
        return jsonify({
            "success": True,  # Still return success with fallback
            "date": fallback_date,
            "warning": "Using fallback date due to service error",
            "error_details": str(e) if app.debug else None,
            "auto_update": auto_update_enabled
        }), 200  # Return 200 with fallback instead of 500

@app.route("/api/settings/date", methods=["POST"])
def update_date_setting():
    """
    Update the default date setting with comprehensive validation and error handling.
    
    Expected JSON payload:
    {
        "date": "YYYY-MM-DD",
        "admin_password": "ppadmin42"
    }
    
    Returns:
        JSON response with success status, message, and detailed error information
    """
    try:
        # Validate request content type
        if not request.is_json:
            return jsonify({
                "success": False,
                "error": "Request must be JSON",
                "code": "INVALID_CONTENT_TYPE"
            }), 400
        
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided",
                "code": "EMPTY_REQUEST"
            }), 400
        
        if not isinstance(data, dict):
            return jsonify({
                "success": False,
                "error": "Request data must be a JSON object",
                "code": "INVALID_REQUEST_FORMAT"
            }), 400
        
        new_date = data.get("date")
        admin_password = data.get("admin_password")
        
        # Validate required fields
        if not new_date:
            return jsonify({
                "success": False,
                "error": "Date field is required",
                "code": "MISSING_DATE"
            }), 400
        
        if not admin_password:
            return jsonify({
                "success": False,
                "error": "Admin password field is required",
                "code": "MISSING_PASSWORD"
            }), 400
        
        # Log the update attempt (without password)
        print(f"[INFO] Date update attempt: {new_date} from IP {request.remote_addr}")
        
        # Call the update function with comprehensive validation
        result = update_default_date(new_date, admin_password)
        
        # Determine appropriate HTTP status code based on error type
        if result["success"]:
            return jsonify(result), 200
        else:
            error_code = result.get("code", "UNKNOWN_ERROR")
            
            if error_code in ["MISSING_PASSWORD", "INVALID_PASSWORD_TYPE", "INVALID_PASSWORD"]:
                return jsonify(result), 401  # Unauthorized
            elif error_code in ["INVALID_DATE", "MISSING_DATE"]:
                return jsonify(result), 400  # Bad Request
            else:
                return jsonify(result), 500  # Internal Server Error
        
    except ValueError as e:
        print(f"[ERROR] Value error in update_date_setting: {e}")
        return jsonify({
            "success": False,
            "error": "Invalid request data format",
            "code": "VALUE_ERROR",
            "details": str(e) if app.debug else None
        }), 400
        
    except TypeError as e:
        print(f"[ERROR] Type error in update_date_setting: {e}")
        return jsonify({
            "success": False,
            "error": "Invalid data types in request",
            "code": "TYPE_ERROR",
            "details": str(e) if app.debug else None
        }), 400
        
    except Exception as e:
        print(f"[ERROR] Unexpected error in update_date_setting: {e}")
        return jsonify({
            "success": False,
            "error": "Internal server error occurred",
            "code": "INTERNAL_ERROR",
            "details": str(e) if app.debug else None
        }), 500

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def ServeReactApp(path):
    FilePath = os.path.join(app.static_folder, path)
    if path != "" and os.path.exists(FilePath):
        return send_from_directory(app.static_folder, path)
    return send_from_directory(app.static_folder, 'index.html')

# Ensure that API routes are defined BEFORE this point, 
# or that this catch-all is specific enough not to interfere with static assets if static_url_path is also '/'.
# If static_url_path="/", Flask automatically tries to serve static files first.
# If /favicon.ico or /assets/index-*.js is requested, Flask's static file handler should serve it.

@app.route("/api/optimize_split", methods=["GET"])
def optimize_split():
    sorted_picks = sorted(pick_objects, key=lambda x: getattr(x, "confidence_score", 0), reverse=True)
    print(f"Optimizer: Processing {len(pick_objects)} picks. Sorted: {[p.name for p in sorted_picks]}")

    if len(sorted_picks) < 2:
        print("Optimizer: Not enough picks to run analysis (need at least 2).")
        return jsonify({
            "best_score": 0.0,
            "best_config": "Not enough picks to optimize (Need at least 2).",
            "sorted_picks": [p.to_dict() for p in sorted_picks],
            "subparlays": []
        }), 200 # Return 200 so frontend can parse the message

    best_score, best_label = analyze_all_splits(sorted_picks)
    # total_capital = request.json.get("total_capital", 0)

    picks_to_use = []
    subgroup_size = 2  # Default

    # Ensure best_label is a string before string operations
    if isinstance(best_label, str):
        if "Full List" in best_label:
            picks_to_use = sorted_picks
        elif "Left" in best_label:
            try:
                split_index_str = best_label.split("index")[1].strip()
                split_index = int(split_index_str)
                picks_to_use = sorted_picks[:split_index]
            except (IndexError, ValueError) as e:
                print(f"Optimizer: Error parsing split_index from left-sided '{best_label}': {e}. Defaulting picks_to_use.")
                picks_to_use = [] 
        elif "Right" in best_label:
            try:
                split_index_str = best_label.split("index")[1].strip()
                split_index = int(split_index_str)
                picks_to_use = sorted_picks[split_index:]
            except (IndexError, ValueError) as e:
                print(f"Optimizer: Error parsing split_index from right-sided '{best_label}': {e}. Defaulting picks_to_use.")
                picks_to_use = []
        else:
            print(f"Optimizer: best_label '{best_label}' did not match expected patterns. Defaulting picks_to_use to empty.")
            picks_to_use = []

        import re
        match = re.search(r"Size (\d+)", best_label)
        if match:
            subgroup_size = int(match.group(1))
        else:
            print(f"Optimizer: Could not extract 'Size X' from best_label '{best_label}'. Defaulting subgroup_size to {subgroup_size}.")
    else:
        # This case should ideally not be hit if len(sorted_picks) >= 2 because analyze_all_splits should return a string label.
        # However, as a fallback if best_label is unexpectedly not a string (e.g. None, though covered by initial check).
        print(f"Optimizer: best_label was not a string ('{best_label}'). Defaulting picks_to_use and subgroup_size.")
        picks_to_use = []
        subgroup_size = 2
        best_label = "Error: Invalid optimization label received."
        best_score = 0.0

    subparlays = generate_round_robin_subparlays(picks_to_use, subgroup_size)
    final_best_score = best_score if isinstance(best_score, (int, float)) else 0.0

    return jsonify({
        "best_score": final_best_score,
        "best_config": best_label,
        "sorted_picks": [p.to_dict() for p in sorted_picks],
        "subparlays": [[p.to_dict() for p in sub] for sub in subparlays]
    })



@app.route("/api/process", methods=["POST"])
def process():
    global next_id
    data = request.get_json()

    try:
        print("[INFO] PAYLOAD RECEIVED:", data)

        name = data.get("name", "").strip()
        pick_origins = data.get("pick_origin", [])  # [{ name, confidence }]
        print("odds:" + str(data.get("odds", 0)))
        odds = float(data.get("odds", 0))
        leagues = data.get("league", [])
        reusable = data.get("reusable", True)
        capital_limit = int(data.get("capital_limit", 0))
        mutual_exclusion = int(data.get("mutual_exclusion", -1))
        pick_type = data.get("pick_type", "MoneyLine")
        player_team = data.get("player_team", "None")
        stat_type = data.get("stat_type", "MoneyLine")

        if not name or not odds or not pick_origins or not leagues:
            return jsonify({"response": "Missing required fields", "success": False}), 400

        implied_prob = round(1 / odds, 4)  # crowd probability from odds
        
        # Use eventDate from payload if provided, otherwise fall back to today's date
        event_date = data.get("eventDate")
        if not event_date:
            event_date = datetime.today().strftime("%Y-%m-%d")
            print(f"[WARNING] No eventDate provided in payload, using today's date: {event_date}")
        else:
            print(f"[INFO] Using eventDate from payload: {event_date}")

        expert_predictions = []
        total_score = 0

        for origin_obj in pick_origins:
            origin = origin_obj.get("name")
            print(origin_obj.get("confidence"))
            origin_conf = origin_obj.get("confidence")

            if not origin:
                continue  # Skip invalid entries

            # Ensure origin_conf is a usable float
            try:
                used_conf = float(origin_conf)
            except Exception as e:
                used_conf = 75.0  # fallback if None or not a number

            # Normalize the origin key by removing spaces
            origin_key = origin.replace(" ", "")

            if origin_key not in origin_profiles:
                raise KeyError(f"Origin key '{origin_key}' not found in origin_profiles")

            origin_accuracy = origin_profiles[origin_key]() if callable(origin_profiles[origin_key]) else origin_profiles[origin_key]


            norm_conf = used_conf / 100.0
            # Extract shared prediction direction
            prediction = int(data.get("prediction", 1))  # 1 = Higher, 0 = Lower

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence")

                if not origin:
                    continue  # Skip invalid entries

                # Ensure origin_conf is a usable float
                try:
                    used_conf = float(origin_conf)
                except Exception:
                    used_conf = 75.0  # fallback if None or not a number

                # 🧠 Historical accuracy
                origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]

                norm_conf = used_conf / 100.0

                # ⬅️ Use the shared prediction value for all experts
                expert_predictions.append((origin, prediction, norm_conf))

                # 🧮 Confidence score calculation
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

        # final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
        # print(final_score)

        if pick_type == "MoneyLine":
            team_a = name
            team_b = "Other"
            player_team = "None"
        else:
            team_a = "Over"
            team_b = "Under"

        print("🧠 Parsed expert predictions:", expert_predictions)

        status_messages = []
        success_count = 0

        # Process each league for this pick

        for league in leagues:
            event_id = generate_event_id(name, league)
            # Check if the event already exists
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM events WHERE event_id = ?", (event_id,))
            exists = cursor.fetchone() is not None
            conn.close()

            if not exists:
                # Insert event & predictions as usual
                print(f"[DEBUG] process: New event submission - event_id={event_id}, implied_prob={implied_prob}, pick_origins_count={len(pick_origins)}")
                success, message = submit_event(
                    event_id=event_id,
                    event_date=event_date,
                    league=league,
                    team_a=team_a,
                    team_b=team_b,
                    crowd_probability=implied_prob,
                    expert_predictions=expert_predictions,
                    actual_result=None,
                    pick_type=pick_type,
                    player_team=player_team,
                    stat_type=stat_type,
                    player_name=None,  # /api/process doesn't handle player props typically
                    stat_threshold=None
                )
                print(f"[DEBUG] process: submit_event returned - success={success}, message={message}")
            else:
                # Just insert additional expert predictions and update crowd prob
                try:
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    # Update crowd_probability (optional)
                    cursor.execute("""
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    # Append new expert predictions
                    for expert_name, prediction, confidence in expert_predictions:
                        # Prevent phantom ChartGeneration predictions
                        if expert_name.lower() == "chartgeneration":
                            continue
                            
                        cursor.execute("""
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, expert_name, prediction, confidence))

                        # Ensure expert exists in reliability table
                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = ?", (expert_name,))
                        if not cursor.fetchone():
                            cursor.execute("""
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (expert_name,))

                    conn.commit()
                    conn.close()
                    success = True
                    message = "Existing event updated with crowd prediction and new expert predictions."

                except Exception as e:
                    success = False
                    message = f"Error updating existing event: {e}"

            status_messages.append(f"[{league}] {message}")

            # Calculate average expert confidence for this event
            avg_expert_confidence = sum(conf for _, _, conf in expert_predictions) / len(expert_predictions) if expert_predictions else 0.75
            
            # Use dynamic confidence calculation with ML model
            try:
                final_score = calculate_dynamic_confidence(event_id, avg_expert_confidence)
                print(f"[INFO] Dynamic confidence calculated: {final_score} for event {event_id}")
            except Exception as e:
                print(f"[ERROR] Dynamic confidence calculation failed: {e}")
                # Fallback to average expert confidence scaled to 0-100
                final_score = avg_expert_confidence * 100
            
            # Get additional ML model data for Pick object
            try:
                prediction_result = main_model(event_id)
                ml_prob = prediction_result.get("combined_prob", 0.5)
                logistic_prob = prediction_result.get("logistic_prob", 0.5)
                bayesian_prob = prediction_result.get("bayesian_prob", 0.5)
                bayesian_conf = prediction_result.get("quality_score", 0.5)
            except Exception as e:
                print(f"[ERROR] Failed to get ML model data: {e}")
                ml_prob = logistic_prob = bayesian_prob = bayesian_conf = 0.5

            if success:
                new_pick = Pick(
                    name=name,
                    odds=odds,
                    confidence=final_score,
                    mutual_exclusion_group=mutual_exclusion,
                    league=league,
                    event_id=event_id,
                    bayesian_prob =bayesian_prob, 
                    logistic_prob=logistic_prob, 
                    bayesian_conf=bayesian_conf,
                    stat_type=stat_type,
                    reusable=reusable,
                    capital_limit=capital_limit
                )
                pick_objects.append(new_pick)
                next_id += 1
                success_count += 1



        return jsonify({
            "response": " | ".join(status_messages),
            "objects": [p.__dict__ for p in pick_objects],
            "success": success_count == len(leagues)
        })

    except Exception as e:
        print("[ERROR] SERVER ERROR:", e)
        return jsonify({"response": f"Server error: {str(e)}", "success": False}), 500



@app.route("/api/create_boost_promo", methods=["POST"])
def create_boost_promo():
    data = request.get_json()
    boost_percentage = int(data.get("boost_percentage", 0))
    required_picks = int(data.get("required_picks", 0))
    same_sport = data.get("same_sport", False)

    boost = BoostPromo(boost_percentage, required_picks, same_sport)
    boost_promo_objects.append(boost.__dict__)

    return jsonify({"response": f"Created Boost Promo: {boost.name}", "boost_promos": boost_promo_objects})

@app.route("/api/create_protected_promo", methods=["POST"])
def create_protected_promo():
    data = request.get_json()
    protected_amount = int(data.get("protected_amount", 0))
    eligible_leagues = data.get("eligible_leagues", [])

    protected = ProtectedPromo(protected_amount, eligible_leagues)
    protected_promo_objects.append(protected.__dict__)

    return jsonify({"response": f"Created Protected Play Promo: {protected.name}", "protected_promos": protected_promo_objects})

@app.route("/api/edit", methods=["POST"])
def edit():
    global pick_objects
    data = request.get_json()
    obj_id = data.get("id")

    for obj in pick_objects:
        if obj.pID == obj_id:
            # Update fields on the Pick object
            obj.name = data.get("name", obj.name)
            obj.decimalOdds = float(data.get("odds", obj.decimalOdds))
            obj.pick_origin = data.get("pick_origin", obj.pick_origin)
            obj.league = data.get("league", obj.league)
            obj.reusable = data.get("reusable", obj.reusable)
            obj.capital_limit = int(data.get("capital_limit", obj.capital_limit))
            obj.gameID = int(data.get("mutual_exclusion", obj.gameID))
            obj.pick_type = data.get("pick_type", obj.pick_type)
            obj.player_team = data.get("player_team", obj.player_team)
            obj.stat_type = data.get("stat_type", obj.stat_type)

            name = obj.name
            odds = obj.decimalOdds
            leagues = obj.league
            pick_origins = obj.pick_origin
            pick_type = obj.pick_type
            player_team = obj.player_team
            stat_type = obj.stat_type

            # Determine team_a and team_b based on pick_type
            if pick_type == "MoneyLine":
                team_a = name
                team_b = "Other"
                player_team = "None"
            else:
                team_a = "Over"
                team_b = "Under"

            implied_prob = round(1 / odds, 4)
            today = datetime.today().strftime("%Y-%m-%d")

            # Recalculate expert prediction score
            expert_predictions = []
            total_score = 0

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence", None)
                prediction = origin_obj.get("prediction", 1)  # default to Higher

                origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]
                norm_conf = origin_conf / 100 if origin_conf is not None else None

                expert_predictions.append((origin, prediction, norm_conf))

                used_conf = origin_conf if origin_conf is not None else 75.0
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

            final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
            obj.confidence = None
            obj.confidence_score = final_score

            # Update the database
            for league in leagues:
                event_id = generate_event_id(name, league)
                obj.event_id = event_id

                try:
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    cursor.execute("""
                        INSERT OR REPLACE INTO events (
                            event_id, event_date, league,
                            team_a, team_b, actual_result,
                            pick_type, player_team, stat_type
                        ) VALUES (?, ?, ?, ?, ?, COALESCE(
                            (SELECT actual_result FROM events WHERE event_id = ?), NULL
                        ), ?, ?, ?)
                    """, (
                        event_id, today, league,
                        team_a, team_b, event_id,
                        pick_type, player_team, stat_type
                    ))

                    cursor.execute("""
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    cursor.execute("DELETE FROM expert_predictions WHERE event_id = ?", (event_id,))
                    for origin, prediction, confidence in expert_predictions:
                        # Prevent phantom ChartGeneration predictions
                        if origin.lower() == "chartgeneration":
                            continue
                            
                        cursor.execute("""
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, origin, prediction, confidence))

                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = ?", (origin,))
                        if not cursor.fetchone():
                            cursor.execute("""
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (origin,))

                    conn.commit()
                    conn.close()

                except Exception as e:
                    print(f"[ERROR] DB Error while editing {event_id}: {e}")

            break

    return jsonify({"objects": [p.__dict__ for p in pick_objects]})

##### CURRENT SPOT IS CREATING RELIABLE EVENT ID FROM PICK ID ####
def get_event_id_from_pick_id(pick_id):
    for pick in pick_objects:
        print(pick.pID)
        if pick.pID == pick_id:
            return pick.event_id
    return None



@app.route("/api/submit_verified", methods=["POST"])
def submit_verified():
    data = request.get_json()
    verified = data.get("verified", [])

    print(pick_objects)

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    updated_ids = []
    localID = 0

    for item in verified:
        localID+=1
        pick_id = localID
        

        # Assume item["actual_result"] is 1 if user marked it "Verified"
        user_verification = item["actual_result"]

        # Find pick object by ID
        pick = next((p for p in pick_objects if hasattr(p, "pID") and p.pID == pick_id), None)
        if not pick:
            print(f"[ERROR] Pick ID {pick_id} not found in memory.")
            continue

        event_id = getattr(pick, "event_id", None)
        if not event_id:
            print(f"[ERROR] Pick ID {pick_id} has no event_id.")
            continue

        # 🔍 Interpret prediction direction:
        #  - If expert said "Higher" and user marked as 1 → event happened ✅
        #  - If expert said "Lower" and user marked as 1 → event did NOT happen ❌
        # We'll default to the first expert's prediction signal (they're all assumed to match)
        cursor.execute("SELECT prediction FROM expert_predictions WHERE event_id = ? LIMIT 1", (event_id,))
        row = cursor.fetchone()

        if row:
            expert_prediction = row[0]  # 1 = Higher, 0 = Lower

            if expert_prediction == 1:  # Higher = event is expected to occur
                actual_result = 1 if user_verification == 1 else 0
            else:  # Lower = expert expects event NOT to occur
                actual_result = 0 if user_verification == 1 else 1
        else:
            print(f"⚠️ No expert prediction found for {event_id}, assuming default.")
            actual_result = user_verification
        event_id = get_event_id_from_pick_id(pick_id)
        print(event_id)

        # ✅ Find pick object by ID (from actual class instances)
        pick = next((p for p in pick_objects if hasattr(p, "pID") and p.pID == pick_id), None)
        if not pick:
            print(f"❌ Pick ID {pick_id} not found in memory.")
            continue

        event_id = getattr(pick, "event_id", None)
        if not event_id:
            print(f"❌ Pick ID {pick_id} has no event_id.")
            continue

        print(f"✅ Updating event_id: {event_id} → actual_result: {actual_result}")
        try:
            cursor.execute("""
                UPDATE events
                SET actual_result = ?
                WHERE event_id = ?
            """, (actual_result, event_id))

            if cursor.rowcount > 0:
                updated_ids.append(event_id)
            else:
                print(f"⚠️ No rows updated for {event_id} (may not exist in DB).")

        except Exception as e:
            print(f"❌ DB error updating {event_id}: {e}")

    conn.commit()
    conn.close()

    return jsonify({"message": f"Updated {len(updated_ids)} events with actual results."})






@app.route("/api/load_sample_picks", methods=["POST"])
def load_sample_picks():
    global pick_objects
    pick_objects = []

    num_picks = 8
    example_names = ["Lakers ML", "Yankees -1.5", "Chiefs +3", "Over 8.5", "Under 220", "Dodgers ML", "Ravens -2.5", "Heat +6", "Bills ML", "Nets Over 230"]
    leagues = ["NBA", "NFL", "MLB", "NHL"]

    for i in range(num_picks):
        name = random.choice(example_names) + f" #{i+1}"
        odds = round(random.uniform(1.05, 2.5), 2)
        mutual_exclusion_group = random.randint(0, 5)
        league = random.choice(leagues)
        reusable = random.choice([True, False])
        capital_limit = random.randint(10, 100)
        stat_type = "MoneyLine"
        event_id = f"SAMPLE-{i+1}"

        # Generate synthetic model probabilities
        bayesian_prob = round(random.uniform(0.4, 0.9), 2)
        logistic_prob = round(random.uniform(0.4, 0.9), 2)
        bayesian_conf = round(random.uniform(0.5, 0.9), 2)

        # Calculate final confidence score using model-weighted blend
        combined_prob = round(
            bayesian_conf * bayesian_prob + (1 - bayesian_conf) * logistic_prob, 4
        )
        implied_prob = 1 / odds
        raw_score = combined_prob - implied_prob
        clamped = max(min(raw_score, 0.2), -0.2)
        scaled_score = round((clamped + 0.2) / 0.4 * 100, 2)

        # Create Pick object
        new_pick = Pick(
            name=name,
            odds=odds,
            confidence=scaled_score,
            mutual_exclusion_group=mutual_exclusion_group,
            league=league,
            event_id=event_id,
            bayesian_prob=bayesian_prob,
            logistic_prob=logistic_prob,
            bayesian_conf=bayesian_conf,
            stat_type=stat_type,
            reusable=reusable,
            capital_limit=capital_limit
        )

        pick_objects.append(new_pick)

    return jsonify({
        "message": f"{num_picks} sample picks loaded.",
        "objects": [p.to_dict() for p in pick_objects]
    })
    
@app.route("/api/load_user_picks", methods=["POST"])
def load_user_picks():
    """
    Load user picks from frontend PicksContext into backend pick_objects for optimizer functionality.
    Expects JSON payload with 'picks' array containing user pick data.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "message": "No data provided.",
                "objects": []
            }), 400

        user_picks = data.get("picks", [])

        global pick_objects
        pick_objects = []  # Clear existing picks
        Pick.pID_counter = 0  # Reset ID counter

        # Convert user picks to backend Pick objects
        for pick_data in user_picks:
            try:
                # Map PicksContext format to backend Pick format
                new_pick = Pick(
                    name=pick_data.get("playerName", "Unknown Player"),
                    odds=1.5,  # Default odds - could be enhanced to get from pick data
                    confidence=pick_data.get("confidence", 75),
                    mutual_exclusion_group=-1,  # Default group
                    league=["Unknown"],  # Default league as list
                    event_id=pick_data.get("id", f"user_pick_{len(pick_objects)}"),
                    bayesian_prob=0.5,  # Default probability
                    logistic_prob=0.5,  # Default probability
                    bayesian_conf=0.5,  # Default confidence
                    stat_type=pick_data.get("betType", "Unknown"),
                    reusable=True,
                    capital_limit=0
                )
                pick_objects.append(new_pick)
            except Exception as e:
                print(f"Error processing pick {pick_data}: {e}")
                continue

        return jsonify({
            "message": f"Loaded {len(pick_objects)} user picks successfully.",
            "objects": [p.to_dict() for p in pick_objects]
        })

    except Exception as e:
        print(f"Error in load_user_picks: {e}")
        return jsonify({
            "message": f"Error loading user picks: {str(e)}",
            "objects": []
        }), 500


@app.route("/api/clear_picks", methods=["POST"])
def clear_picks():
    global pick_objects
    pick_objects = []
    Pick.pID_counter = 0  # reset ID counter

    # Optional: clear optimizer results too if you're storing those separately
    # e.g., if you eventually save best_score or best_label in global vars

    return jsonify({
        "message": "All picks cleared.",
        "objects": pick_objects
    })


@app.route("/api/delete", methods=["POST"])
def delete():
    data = request.get_json()
    obj_id = data.get("id")

    global pick_objects
    deleted_pick = None

    # Find the pick
    for obj in pick_objects:
        if obj["id"] == obj_id:
            deleted_pick = obj
            break

    if deleted_pick:
        name = deleted_pick["name"]
        leagues = deleted_pick["league"]
        for league in leagues:
            event_id = generate_event_id(name, league)
            try:
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM expert_predictions WHERE event_id = ?", (event_id,))
                cursor.execute("DELETE FROM crowd_predictions WHERE event_id = ?", (event_id,))
                cursor.execute("DELETE FROM events WHERE event_id = ?", (event_id,))
                conn.commit()
                conn.close()
            except Exception as e:
                print(f"DB error while deleting event {event_id}: {e}")

        # Remove from in-memory list
        pick_objects = [obj for obj in pick_objects if obj["id"] != obj_id]

    return jsonify({
   "objects": [p.to_dict() for p in pick_objects]
    })


@app.route("/api/update_accuracy", methods=["POST"])
def update_accuracy():
    global user_accuracy
    data = request.get_json()
    try:
        user_accuracy = float(data.get("accuracy", 0.0))
    except ValueError:
        user_accuracy = 0.0
    return jsonify({"message": f"Accuracy updated to {user_accuracy}", "user_accuracy": user_accuracy})

@app.route("/api/handicappers", methods=["GET"])
def get_handicappers():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper data through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicappers endpoint placeholder",
        "handicappers": []
    })

@app.route("/api/handicappers/<int:handicapper_id>", methods=["GET"])
def get_handicapper_profile(handicapper_id):
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper profiles through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicapper profile endpoint placeholder",
        "handicapper": {
            "id": handicapper_id,
            "name": f"Handicapper {handicapper_id}",
            "accuracy": "N/A",
            "sports": "Multiple Sports",
            "picks": []
        }
    })

@app.route("/api/favorites", methods=["GET"])
def get_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites endpoint placeholder",
        "favorites": []
    })

@app.route("/api/favorites", methods=["POST"])
def save_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites saved (placeholder)",
        "favorites": []
    })

@app.route("/api/todays_events", methods=["GET"])
@app.route("/todays_events", methods=["GET"])
def get_todays_events():
    """
    API endpoint to retrieve events with handicapper predictions for a specific date.
    
    Query Parameters:
        date (optional): Date in YYYY-MM-DD format. If not provided, uses today's date.
    
    Returns:
        JSON response containing a list of events with their associated
        handicapper predictions, or an error message if something goes wrong.
    """
    try:
        # Get date parameter from query string, default to today if not provided
        custom_date = request.args.get('date')
        if custom_date:
            # Validate date format
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "Invalid date format. Please use YYYY-MM-DD format.",
                    "events": []
                }), 400
        else:
            target_date = datetime.today().strftime("%Y-%m-%d")
        
        events = get_todays_events_with_handicappers(target_date)
        
        events_data = []
        for event in events:
            events_data.append(event.to_dict())
        
        return jsonify({
            "success": True,
            "message": f"Retrieved {len(events_data)} events for {target_date}",
            "events": events_data,
            "date": target_date
        })
        
    except Exception as e:
        print(f"[ERROR] Error in /api/todays_events endpoint: {e}")
        return jsonify({
            "success": False,
            "message": f"Error retrieving events: {str(e)}",
            "events": []
        }), 500




@app.route("/api/add_event", methods=["POST"])
def add_event():
    """Insert a single event (or one per league) into the events table. Minimal logic, password-protected."""
    data = request.get_json()
    print("[DEBUG] /api/add_event payload:", data)
    print(f"[DEBUG] crowd_probability received: {data.get('crowd_probability')}")
    source_name = data.get("source")
    print(f"[DEBUG] source_name received: {source_name}")

    if data.get("admin_password") != "ppadmin42":
        return jsonify({"success": False, "message": "Unauthorized"}), 401

    name = data.get("name", "").strip()
    leagues = data.get("league", [])
    event_date = data.get("eventDate") or data.get("event_date") or datetime.today().strftime("%Y-%m-%d")
    pick_type = data.get("pick_type", "MoneyLine")
    player_team = data.get("player_team", "None")
    stat_type_raw = data.get("stat_type", "MoneyLine")
    # Normalize stat type for consistent database storage
    stat_type = normalize_stat_type_for_storage(stat_type_raw)
    player_name = data.get("player_name")
    stat_threshold = data.get("stat_threshold")
    team_a = data.get("team_a", name)  # Default to name if not provided
    team_b = data.get("team_b", "Other")  # Default to "Other" if not provided
    pick_origin = data.get('pick_origin', [])

    if not name or not leagues:
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    # Import PlanetScale connection function
    try:
        from data_science_modules.planet_scale_port import get_connection
    except ImportError:
        return jsonify({"success": False, "message": "Database connection module not available"}), 500

    inserted = 0

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            event_id = None  # Ensure event_id is defined outside the loop
            expert_predictions_inserted = 0

            # Test database connection
            cursor.execute("SELECT 1")
            test_result = cursor.fetchone()
            print(f"[INFO] Database connection test: {test_result}")

            # Check if tables exist
            cursor.execute("SHOW TABLES LIKE 'events'")
            events_table = cursor.fetchone()
            cursor.execute("SHOW TABLES LIKE 'expert_predictions'")
            predictions_table = cursor.fetchone()
            print(f"[INFO] Tables exist - events: {bool(events_table)}, expert_predictions: {bool(predictions_table)}")


            print(f"[INFO] Processing {len(leagues)} leagues: {leagues}")
            print(f"[INFO] Expert predictions to insert: {len(pick_origin) if pick_origin else 0}")

            for league in leagues:
                # Use original stat_type for event_id generation (for consistency)
                # but store normalized version in database
                event_id = generate_admin_event_id(
                    event_date, league, pick_type, team_a, team_b,
                    player_name, stat_threshold, stat_type_raw
                )
                print(f"[INFO] Generated event_id for {league}: {event_id}")
                print(f"[INFO] Event data: date={event_date}, teams={team_a} vs {team_b}, type={pick_type}")

                try:
                    insert_query = """INSERT INTO events (
                            event_id, event_date, league,
                            team_a, team_b, pick_type,
                            player_team, stat_type, player_name, stat_threshold)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                    insert_values = (
                        event_id, event_date, league, team_a, team_b, pick_type,
                        player_team, stat_type, player_name, stat_threshold
                    )
                    print(f"[INFO] Executing SQL: {insert_query}")
                    print(f"[INFO] With values: {insert_values}")

                    cursor.execute(insert_query, insert_values)
                    rows_affected = cursor.rowcount
                    print(f"[INFO] Rows affected by INSERT: {rows_affected}")

                    if rows_affected > 0:
                        inserted += 1
                        print(f"[SUCCESS] Inserted event: {event_id}")
                    else:
                        print(f"[WARNING] No rows inserted for event: {event_id}")

                except Exception as insert_error:
                    if "Duplicate entry" in str(insert_error):
                        print(f"[WARNING] Event already exists: {event_id}")
                    else:
                        lower_err = str(insert_error).lower()
                        if "unknown column" in lower_err or "column count" in lower_err:
                            try:
                                fallback_query = """INSERT INTO events (
                                        event_id, event_date, league,
                                        team_a, team_b, pick_type,
                                        player_team, stat_type)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"""
                                cursor.execute(
                                    fallback_query,
                                    (
                                        event_id,
                                        event_date,
                                        league,
                                        team_a,
                                        team_b,
                                        pick_type,
                                        player_team,
                                        stat_type,
                                    ),
                                )
                                if cursor.rowcount > 0:
                                    inserted += 1
                                    print(f"[SUCCESS] Fallback inserted event: {event_id}")
                            except Exception as fb_err:
                                print(f"[WARNING] Fallback failed, attempting minimal insert: {fb_err}")
                                try:
                                    minimal_query = """INSERT INTO events (
                                            event_id, event_date, league, team_a, team_b)
                                        VALUES (%s, %s, %s, %s, %s)"""
                                    cursor.execute(
                                        minimal_query,
                                        (
                                            event_id,
                                            event_date,
                                            league,
                                            team_a,
                                            team_b,
                                        ),
                                    )
                                    if cursor.rowcount > 0:
                                        inserted += 1
                                        print(f"[SUCCESS] Minimal fallback inserted event: {event_id}")
                                except Exception as min_err:
                                    print(f"[ERROR] Minimal fallback insert failed for {event_id}: {min_err}")
                                    # Do **not** raise here – continue processing so expert_predictions loop can still run if event exists
                        else:
                            print(f"[ERROR] Failed to insert event {event_id}: {insert_error}")
                            import traceback
                            traceback.print_exc()
                            raise  

                # Insert crowd predictions for this event
                crowd_prob = data.get("crowd_probability")
                try:
                    print(f"[DEBUG] /api/add_event inserting crowd_prediction: event_id={event_id}, crowd_probability={crowd_prob}")
                    cursor.execute(
                        "INSERT INTO crowd_predictions (event_id, crowd_probability) VALUES (%s, %s) ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)",
                        (event_id, crowd_prob)
                    )
                except Exception as crowd_err:
                    print(f"[ERROR] Failed to insert crowd_prediction for {event_id}: {crowd_err}")
                # After inserting the event, insert expert predictions referencing this specific event_id
                if pick_origin:
                    for expert_pick in pick_origin:
                        expert_name = expert_pick.get("name")
                        if expert_name and expert_name.strip():
                            # Validate and convert data types to match database constraints
                            try:
                                # Convert prediction to integer (0 or 1)
                                prediction = int(data.get("prediction", 1))
                                if prediction not in [0, 1]:
                                    prediction = 1  # Default to 1 if invalid

                                # Store original confidence for potential fallback
                                confidence_raw = expert_pick.get("confidence")
                                if confidence_raw is not None:
                                    # Extract numeric value from confidence
                                    import re
                                    match = re.search(r'(\d+\.?\d*)', str(confidence_raw))
                                    if match:
                                        confidence_str = match.group(1)
                                        original_confidence = float(confidence_str)
                                        # If confidence is > 1, assume it's a percentage and convert
                                        if original_confidence > 1.0:
                                            original_confidence = original_confidence / 100.0
                                        # Ensure it's within valid range
                                        confidence = max(0.0, min(1.0, original_confidence))
                                    else:
                                        confidence = 0.5  # Default if invalid format
                                else:
                                    confidence = 0.5  # Default if no confidence provided
                                
                                print(f"🔄 Using original confidence: {confidence*100:.1f}% for {expert_name} (will attempt dynamic calculation after insertion)")

                                # Convert stat_threshold to float if provided
                                stat_threshold_raw = expert_pick.get("stat_threshold")
                                stat_threshold = float(stat_threshold_raw) if stat_threshold_raw is not None else None

                                # Handle prediction_time field
                                prediction_time = expert_pick.get("prediction_time")
                                if prediction_time:
                                    # If only time is provided, combine with event_date
                                    if len(prediction_time) <= 8:  # Format like "14:30:00" or "14:30"
                                        prediction_time = f"{event_date} {prediction_time}"
                                    # If full datetime is provided, use as-is
                                else:
                                    # Default to game date at noon if not provided
                                    prediction_time = f"{event_date} 12:00:00"

                                print(f"🔄 Inserting prediction: expert={expert_name}, prediction={prediction}, confidence={confidence}, time={prediction_time}")

                                # Prevent phantom ChartGeneration predictions
                                if expert_name.lower() == "chartgeneration":
                                    continue
                                
                                # Check if this expert prediction already exists to prevent duplicates
                                cursor.execute(
                                    "SELECT event_id FROM expert_predictions WHERE event_id = %s AND expert_name = %s",
                                    (event_id, expert_name)
                                )
                                existing_record = cursor.fetchone()
                                
                                if existing_record:
                                    print(f"⚠️ Expert prediction already exists for {expert_name} on {event_id}, updating...")
                                    cursor.execute(
                                        """UPDATE expert_predictions SET 
                                            prediction = %s, confidence = %s, stat_threshold = %s, 
                                            team = %s, prediction_time = %s
                                        WHERE event_id = %s AND expert_name = %s""",
                                        (
                                            prediction,
                                            confidence,
                                            stat_threshold,
                                            expert_pick.get("team"),
                                            prediction_time,
                                            event_id,
                                            expert_name,
                                        ),
                                    )
                                else:
                                    cursor.execute(
                                        """INSERT INTO expert_predictions (
                                            event_id, expert_name, prediction, confidence,
                                            stat_threshold, team, game_date, league, prediction_time
                                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                        (
                                            event_id,
                                            expert_name,
                                            prediction,
                                            confidence,
                                            stat_threshold,
                                            expert_pick.get("team"),
                                            event_date,
                                            league,
                                            prediction_time,
                                        ),
                                    )
                                expert_predictions_inserted += 1
                                print(f"✅ Inserted expert prediction: {expert_name} for {event_id}")
                            except Exception as pred_error:
                                print(f"❌ Failed to insert expert prediction for {expert_name}: {pred_error}")
                                # Try fallback with basic columns only and validated data
                                try:
                                    prediction = int(data.get("prediction", 1))
                                    if prediction not in [0, 1]:
                                        prediction = 1

                                    confidence_raw = expert_pick.get("confidence")
                                    if confidence_raw is not None:
                                        confidence = float(confidence_raw)
                                        if confidence > 1.0:
                                            confidence = confidence / 100.0
                                        confidence = max(0.0, min(1.0, confidence))
                                    else:
                                        confidence = None

                                    # Use game date at noon for fallback
                                    fallback_time = f"{event_date} 12:00:00"

                                    cursor.execute(
                                        """INSERT INTO expert_predictions (
                                            event_id, expert_name, prediction, confidence, prediction_time
                                        ) VALUES (%s, %s, %s, %s, %s)
                                        ON DUPLICATE KEY UPDATE 
                                            prediction = VALUES(prediction),
                                            confidence = VALUES(confidence),
                                            prediction_time = VALUES(prediction_time)""",
                                        (
                                            event_id,
                                            expert_name,
                                            prediction,
                                            confidence,
                                            fallback_time,
                                        ),
                                    )
                                    expert_predictions_inserted += 1
                                    print(f"✅ Inserted expert prediction (basic): {expert_name} for {event_id}")
                                except Exception as fallback_error:
                                    print(f"❌ Failed to insert expert prediction (fallback): {fallback_error}")
                                    # Final minimal fallback with only required columns
                                    try:
                                        cursor.execute(
                                            "INSERT INTO expert_predictions (event_id, expert_name, prediction) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE prediction = VALUES(prediction)",
                                            (
                                                event_id,
                                                expert_name,
                                                int(data.get("prediction", 1)) if str(data.get("prediction", 1)).isdigit() else 1,
                                            ),
                                        )
                                        expert_predictions_inserted += 1
                                        print(f"✅ Inserted expert prediction (minimal) for {expert_name} → {event_id}")
                                    except Exception as final_err:
                                        print(f"🚨 All expert prediction insert attempts failed for {expert_name}: {final_err}")
                        else:
                            print(f"⚠️ Skipping empty expert name for {event_id}")

            # After all expert predictions are inserted, attempt to calculate and update dynamic confidence
            if event_id and expert_predictions_inserted > 0:
                print(f"🧮 Attempting dynamic confidence calculation for {event_id} with {expert_predictions_inserted} expert predictions...")
                try:
                    # Get all expert predictions for this event to calculate confidence based on their original values
                    cursor.execute(
                        "SELECT expert_name, confidence FROM expert_predictions WHERE event_id = %s",
                        (event_id,)
                    )
                    expert_records = cursor.fetchall()
                    
                    if expert_records:
                        # Calculate dynamic confidence for each expert
                        for expert_name, original_confidence in expert_records:
                            original_confidence_percentage = original_confidence * 100  # Convert to percentage
                            
                            # --- Dynamic confidence calculation ---
                            dynamic_confidence_percentage = calculate_dynamic_confidence_vs_expert(
                                event_id, original_confidence_percentage
                            )

                            print(f"🔧 Dynamic confidence result: {dynamic_confidence_percentage:.1f}%")
                            
                            # Only update if dynamic calculation succeeded (not the 50% default fallback)
                            if dynamic_confidence_percentage != 50.0:
                                confidence_decimal = dynamic_confidence_percentage / 100.0
                                
                                # Update this specific expert's confidence
                                cursor.execute(
                                    "UPDATE expert_predictions SET confidence = %s WHERE event_id = %s AND expert_name = %s",
                                    (confidence_decimal, event_id, expert_name)
                                )
                                print(f"✅ Updated {expert_name} confidence: {original_confidence_percentage:.1f}% → {dynamic_confidence_percentage:.1f}%")
                            else:
                                print(f"⚠️ Dynamic confidence calculation failed for {expert_name} - keeping original {original_confidence_percentage:.1f}%")
                    
                except Exception as conf_error:
                    print(f"❌ Failed to calculate dynamic confidence: {conf_error}")
                    print("ℹ️ Expert predictions will keep their original confidence values")

            conn.commit()
            print(f"[SUCCESS] Transaction committed: {inserted} events, {expert_predictions_inserted} expert predictions")
            
    except Exception as e:
        # The connection context manager should handle rollback on exception.
        print(f"[ERROR] Database transaction failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": f"Database transaction failed: {str(e)}"}), 500

    return jsonify({
        "success": True,
        "message": f"Successfully inserted {inserted} events, {inserted} crowd predictions, and {expert_predictions_inserted} expert predictions.",
        "event_id": event_id,
        "events_inserted": inserted,
        "crowd_predictions_inserted": inserted,
        "expert_predictions_inserted": expert_predictions_inserted
    })

@app.route("/api/experts", methods=["GET"])
def get_all_experts():
    """Return all distinct expert names from expert_predictions table."""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT expert_name FROM expert_predictions
                WHERE expert_name IS NOT NULL AND expert_name != ''
                ORDER BY expert_name
            """)
            rows = cursor.fetchall()
            experts = [row[0] for row in rows]
            return jsonify({"success": True, "experts": experts})
    except Exception as e:
        print(f"[ERROR] Error fetching all experts: {e}")
        return jsonify({"success": False, "message": str(e), "experts": []}), 500

@app.route("/api/experts_by_date", methods=["GET"])
def experts_by_date():
    """Return distinct expert names that have predictions for the given date."""
    target_date = request.args.get("date") or datetime.today().strftime("%Y-%m-%d")
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # Use JOIN with events table to filter by event_date (most reliable approach)
            cursor.execute("""
                SELECT DISTINCT ep.expert_name
                FROM expert_predictions ep
                JOIN events e ON ep.event_id = e.event_id
                WHERE e.event_date = %s
                AND ep.expert_name IS NOT NULL
                AND ep.expert_name != ''
            """, (target_date,))
            rows = cursor.fetchall()
            experts = [row[0] for row in rows]
            return jsonify({"success": True, "date": target_date, "experts": experts})

    except Exception as e:
        print(f"[ERROR] Error fetching experts for {target_date}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": str(e), "experts": []}), 500


@app.route("/api/event_handicappers/<event_id>", methods=["GET"])
def get_event_handicappers(event_id):
    """
    API endpoint to retrieve all handicappers who have made predictions for a specific event.

    Args:
        event_id (str): The event ID to get handicappers for.

    Returns:
        JSON response containing a list of handicapper names who have made predictions for this event.
    """
    try:
        conn = get_dict_connection()
        with conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT expert_name
                FROM expert_predictions
                WHERE event_id = %s
            """, (event_id,))
            rows = cursor.fetchall()

            handicappers = [row["expert_name"] for row in rows]

            return jsonify({
                "success": True,
                "handicappers": handicappers,
                "count": len(handicappers)
            })
    except Exception as e:
        print(f"❌ Error in /api/event_handicappers endpoint: {e}")
        return jsonify({
            "success": False,
            "message": f"Error retrieving handicappers: {str(e)}",
            "handicappers": []
        }), 500

@app.route("/api/cleanup_chart_generation", methods=["POST"])
def cleanup_chart_generation_endpoint():
    """Clean up phantom ChartGeneration expert predictions."""
    try:
        data = request.get_json() or {}
        if data.get("admin_password") != "ppadmin42":
            return jsonify({"success": False, "message": "Unauthorized"}), 401
        
        deleted_count = cleanup_chart_generation_predictions()
        
        return jsonify({
            "success": True,
            "message": f"Cleaned up {deleted_count} ChartGeneration predictions",
            "deleted_count": deleted_count
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Error: {str(e)}"
        }), 500

@app.route("/api/main_model/<event_id>", methods=["POST"])
def get_main_model_prediction(event_id):
    """
    API endpoint to get main model prediction including team_kde_prob for a specific event.
    
    Args:
        event_id (str): The event ID to get prediction for.
        
    Returns:
        JSON response containing the main model prediction results including team_kde_prob.
    """
    try:
        print(f"🔍 Running main_model for event_id: {event_id}")
        
        # Call the main_model function from HandiCapperAccuracyModel
        result = main_model(event_id)
        
        if "error" in result:
            return jsonify({
                "success": False,
                "message": f"Model prediction failed: {result['error']}",
                "team_kde_prob": None
            }), 400
            
        # Extract team_kde_prob and other model results
        team_kde_prob = result.get("team_kde_prob")
        
        return jsonify({
            "success": True,
            "event_id": event_id,
            "team_kde_prob": team_kde_prob,
            "combined_prob": result.get("combined_prob"),
            "bayesian_prob": result.get("bayesian_prob"),
            "logistic_prob": result.get("logistic_prob"),
            "quality_score": result.get("quality_score"),
            "predicted_class": result.get("predicted_class")
        })
        
    except Exception as e:
        print(f"❌ Error in /api/main_model endpoint for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"Error running main model: {str(e)}",
            "team_kde_prob": None
        }), 500

@app.route("/api/player_chart/<event_id>", methods=["GET"])
def get_player_chart(event_id):
    """
    Comprehensive player chart endpoint with robust error handling and fallbacks.
    """
    import re
    import json
    import base64
    import io
    import matplotlib
    matplotlib.use('Agg')  # Use non-GUI backend for web server
    import matplotlib.pyplot as plt
    import numpy as np
    
    try:
        print(f"🔍 Generating chart for event_id: {event_id}")
        
        # Step 1: Robust event ID correction with double-letter fix
        corrected_event_id, corrections_made = correct_event_id_format_robust(event_id)
        print(f"🔧 After correction: {corrected_event_id}")
        print(f"📝 Corrections made: {corrections_made}")
        
        # Step 2: Try to get existing distribution data
        from data_science_modules.kde_distributions_port import fetch_distributions
        distribution_data = fetch_distributions(corrected_event_id)
        
        if distribution_data is not None:
            print(f"✅ Found existing distribution data for {corrected_event_id}")
            # Generate chart from existing data using PlotUtils
            plotter = PlotUtils()
            chart_base64 = plotter.unpack_distribution_data(corrected_event_id)
            return jsonify({
                "success": True,
                "event_id": corrected_event_id,
                "chart_data": chart_base64,
                "corrections": corrections_made,
                "data_source": "database"
            })
        
        print(f"⚠️ No distribution data found for {corrected_event_id}")
        
        # Step 3: Try to generate data via MainModel
        print(f"🔄 Attempting to generate distribution data via MainModel for {corrected_event_id}")
        
        try:
            # Check if event exists in database before attempting MainModel
            import re
            pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
            match = re.match(pattern, corrected_event_id)
            
            if match:
                date_part, league, player_name, threshold, stat_type = match.groups()
                
                # Check if event exists (but don't create it)
                conn = get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM events WHERE event_id = %s", (corrected_event_id,))
                event_exists = cursor.fetchone() is not None
                conn.close()
                
                if event_exists:
                    print(f"✅ Event exists in database: {corrected_event_id}")
                    # Skip main_model to prevent phantom predictions during chart generation
                    model_result = None
                    if model_result is not None:
                        print(f"✅ MainModel completed for {corrected_event_id}")
                        
                        # Try to fetch distribution data again
                        distribution_data = fetch_distributions(corrected_event_id)
                        
                        if distribution_data is not None:
                            print(f"✅ Distribution data saved for {corrected_event_id}")
                            chart_base64 = plotter.unpack_distribution_data(corrected_event_id)
                            return jsonify({
                                "success": True,
                                "event_id": corrected_event_id,
                                "chart_data": chart_base64,
                                "corrections": corrections_made,
                                "data_source": "generated_via_mainmodel"
                            })
                        else:
                            print(f"⚠️ MainModel ran but no distribution data was saved for {corrected_event_id}")
                    else:
                        print(f"❌ MainModel returned None for {corrected_event_id}")
                else:
                    print(f"⚠️ Event does not exist in database: {corrected_event_id} - skipping MainModel")
            else:
                print(f"❌ Could not parse event ID format: {corrected_event_id}")
                
        except Exception as model_error:
            print(f"❌ MainModel failed for {corrected_event_id}: {model_error}")
            import traceback
            traceback.print_exc()
        
        # Step 4: Try alternative chart generation from historical data
        print(f"📊 Attempting alternative chart generation from historical data for {corrected_event_id}")
        from data_science_modules.planet_scale_port import generate_chart_from_historical_data
        
        historical_chart_result = generate_chart_from_historical_data(corrected_event_id)
        
        if historical_chart_result["success"] and historical_chart_result["data_source"] != "placeholder":
            print(f"✅ Generated chart from historical data for {corrected_event_id}")
            return jsonify({
                "success": True,
                "event_id": corrected_event_id,
                "chart_data": historical_chart_result["chart_data"],
                "corrections": corrections_made,
                "data_source": "historical_data",
                "stats": historical_chart_result.get("stats", {}),
                "message": "Chart generated from historical player data"
            })
        
        # Step 5: Final fallback to placeholder chart
        print(f"📊 Generating placeholder chart for {corrected_event_id}")
        placeholder_chart = generate_placeholder_chart_simple(corrected_event_id)
        
        return jsonify({
            "success": True,
            "event_id": corrected_event_id,
            "chart_data": placeholder_chart,
            "corrections": corrections_made,
            "data_source": "placeholder",
            "message": "Chart generated with placeholder data - no historical data available"
        })
        
    except Exception as e:
        print(f"❌ Error in player chart endpoint for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "event_id": event_id,
            "message": f"Error generating chart: {str(e)}",
            "chart_data": None
        }), 500

def correct_event_id_format_robust(event_id):
    """
    Robust event ID correction that handles double letter corruption and other issues.
    """
    import re
    
    corrections = []
    original_event_id = event_id
    
    print(f"🔍 Input event ID: {event_id}")
    
    # Step 1: Handle specific league corruption patterns only
    # Only fix known league corruptions, avoid touching player names
    league_fixes = {
        'NNBA': 'NBA',
        'MMLB': 'MLB', 
        'NNFL': 'NFL',
        'NNHL': 'NHL'
    }
    
    fixed_doubles = event_id
    for corrupt, correct in league_fixes.items():
        if f'-{corrupt}-' in event_id:
            fixed_doubles = event_id.replace(f'-{corrupt}-', f'-{correct}-')
            break
    
    if fixed_doubles != event_id:
        corrections.append(f"Fixed league corruption: '{event_id}' -> '{fixed_doubles}'")
        event_id = fixed_doubles
    
    # Step 2: Handle date corruption (20255 -> 2025)
    fixed_date = re.sub(r'(\d{4})\d+-(\d{2}-\d{2})', r'\1-\2', event_id)
    if fixed_date != event_id:
        corrections.append(f"Fixed date corruption: '{event_id}' -> '{fixed_date}'")
        event_id = fixed_date
    
    # Step 3: Standard event ID format correction
    pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)(.*?)$'
    match = re.match(pattern, event_id.upper())
    
    if not match:
        corrections.append(f"Invalid event ID format: {event_id}")
        return event_id, corrections
    
    date_part, league, player_part, threshold_part, stat_part = match.groups()
    
    # Fix player name: only remove spaces, keep periods for initials like "S.CURRY"
    original_player = player_part
    clean_player = player_part.replace(" ", "")
    if clean_player != original_player:
        corrections.append(f"Cleaned player name: '{original_player}' -> '{clean_player}'")
    
    # Fix threshold format: ensure decimal point
    original_threshold = threshold_part
    try:
        threshold_float = float(threshold_part)
        formatted_threshold = f"{threshold_float:.1f}"
        if formatted_threshold != original_threshold:
            corrections.append(f"Fixed threshold format: '{original_threshold}' -> '{formatted_threshold}'")
        threshold_part = formatted_threshold
    except ValueError:
        corrections.append(f"Invalid threshold format: '{original_threshold}'")
    
    # Map stat types to standard abbreviations by league
    original_stat = stat_part
    stat_type_map = {
        'POINTS': 'PTS',
        'POINT': 'PTS',
        'PTS': 'PTS',
        'REBOUNDS': 'REB',
        'REBOUND': 'REB',
        'REB': 'REB',
        'ASSISTS': 'AST',
        'ASSIST': 'AST',
        'AST': 'AST',
        'HOMERUNS': 'HR',
        'HOMERUN': 'HR',
        'HR': 'HR',
        'HITS': 'H',
        'HIT': 'H',
        'H': 'H',
        'RBI': 'RBI',
        'RUNS': 'R',
        'RUN': 'R',
        'R': 'R',
        'STRIKEOUTS': 'K',
        'STRIKEOUT': 'K',
        'K': 'K',
    }
    
    # Handle special case: MLB events using "POINTS" should map to appropriate MLB stats
    if league == 'MLB' and original_stat == 'POINTS':
        corrected_stat = 'H'
        corrections.append(f"Fixed MLB stat type: 'POINTS' -> 'H' (MLB doesn't use points)")
    else:
        corrected_stat = stat_type_map.get(original_stat.upper(), original_stat.upper())
        if corrected_stat != original_stat.upper():
            corrections.append(f"Standardized stat type: '{original_stat}' -> '{corrected_stat}'")
    
    # Reconstruct the corrected event ID
    corrected_event_id = f"{date_part}-{league}-{clean_player}{threshold_part}{corrected_stat}"
    
    if corrected_event_id != original_event_id:
        corrections.append(f"Final correction: '{original_event_id}' -> '{corrected_event_id}'")
    
    return corrected_event_id, corrections

def generate_placeholder_chart_simple(event_id):
    """
    Generate a simple placeholder chart when no data is available.
    """
    import matplotlib
    matplotlib.use('Agg')  # Use non-GUI backend for web server
    import matplotlib.pyplot as plt
    import numpy as np
    import io
    import base64
    import re
    
    try:
        # Parse event ID to get player info
        pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
        match = re.match(pattern, event_id)
        
        if match:
            date_part, league, player_name, threshold, stat_type = match.groups()
            title = f"{player_name} - {stat_type} Distribution"
            subtitle = f"Threshold: {threshold} | League: {league} | Date: {date_part}"
            threshold_val = float(threshold)
        else:
            title = "Player Performance Distribution"
            subtitle = f"Event ID: {event_id}"
            threshold_val = 5.0
        
        # Create a simple placeholder chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Generate sample distribution for visualization
        x = np.linspace(0, threshold_val * 2, 100)
        y = np.exp(-(x - threshold_val)**2 / 2) / np.sqrt(2 * np.pi)
        
        ax.plot(x, y, color='gray', linewidth=2, alpha=0.7, linestyle='--')
        ax.fill_between(x, y, alpha=0.2, color='gray')
        
        # Add threshold line
        ax.axvline(threshold_val, color='red', linestyle='-', linewidth=2, 
                  label=f'Threshold: {threshold_val}')
        ax.legend()
        
        ax.set_xlabel(stat_type if match else 'Stat Value')
        ax.set_ylabel('Probability Density')
        ax.set_title(title)
        ax.text(0.5, 0.95, subtitle, transform=ax.transAxes, ha='center', va='top', 
                fontsize=10, alpha=0.7)
        ax.text(0.5, 0.5, 'No Historical Data Available\nPlaceholder Distribution Shown', 
                transform=ax.transAxes, ha='center', va='center', 
                fontsize=12, alpha=0.5, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.3))
        
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # Save to base64
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        buf.seek(0)
        
        encoded = base64.b64encode(buf.read()).decode('utf-8')
        return encoded
        
    except Exception as e:
        print(f"❌ Error generating placeholder chart: {e}")
        # Return a minimal base64 encoded 1x1 pixel image as fallback
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    """
    API endpoint to get player statistical distribution chart for a specific event.
    
    Args:
        event_id (str): The event ID to get chart for.
        
    Returns:
        JSON response containing base64-encoded chart image.
    """
    try:
        print(f"🔍 Generating chart for event_id: {event_id}")
        
        # Basic event ID format correction
        corrected_event_id, corrections_made = correct_event_id_format(event_id)
        print(f"🔧 After correction: {corrected_event_id}")
        print(f"📝 Corrections made: {corrections_made}")
        
        # Check if distribution data exists in database
        from data_science_modules.kde_distributions_port import fetch_distributions
        distribution_data = fetch_distributions(corrected_event_id)
        
        if distribution_data is None:
            print(f"⚠️ No distribution data found for {corrected_event_id}, attempting to generate via MainModel")
            
            # First, ensure the event exists in the database with basic data
            # MainModel requires event to exist with crowd probability
            try:
                # Parse event ID to extract components
                import re
                pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
                match = re.match(pattern, corrected_event_id)
                
                if match:
                    date_part, league, player_name, threshold, stat_type = match.groups()
                    
                    # Check if event exists (but don't create it)
                    conn = get_connection()
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1 FROM events WHERE event_id = %s", (corrected_event_id,))
                    event_exists = cursor.fetchone() is not None
                    conn.close()
                    
                    if not event_exists:
                        print(f"⚠️ Event does not exist in database: {corrected_event_id} - skipping historical chart generation")
                        # Don't create database entries - skip to placeholder chart
                        raise Exception(f"Event {corrected_event_id} does not exist in database")
                    
                else:
                    print(f"❌ Could not parse event ID format: {corrected_event_id}")
                    return jsonify({
                        "success": False,
                        "event_id": corrected_event_id,
                        "message": f"Invalid event ID format: {corrected_event_id}",
                        "corrections": corrections_made,
                        "chart_data": None
                    }), 400
                    
            except Exception as db_error:
                print(f"❌ Failed to create event record: {db_error}")
                # Continue anyway - MainModel might still work
            
            # Skip main_model to prevent phantom predictions during chart generation
            try:
                model_result = None
                if model_result is not None:
                    print(f"✅ MainModel generated data for {corrected_event_id}")
                    # After MainModel runs, it should have saved distribution data to the database
                    # Try fetching again
                    distribution_data = fetch_distributions(corrected_event_id)
                    
                    if distribution_data is None:
                        print(f"⚠️ MainModel ran but no distribution data was saved for {corrected_event_id}")
                        return jsonify({
                            "success": False,
                            "event_id": corrected_event_id,
                            "message": "MainModel completed but no distribution data was generated. This may indicate the KDE step failed.",
                            "corrections": corrections_made,
                            "chart_data": None
                        }), 404
                else:
                    print(f"❌ MainModel returned None for {corrected_event_id}")
                    return jsonify({
                        "success": False,
                        "event_id": corrected_event_id,
                        "message": "Unable to generate distribution data - MainModel returned None",
                        "corrections": corrections_made,
                        "chart_data": None
                    }), 404
                    
            except Exception as model_error:
                print(f"❌ MainModel failed for {corrected_event_id}: {model_error}")
                return jsonify({
                    "success": False,
                    "event_id": corrected_event_id,
                    "message": f"Failed to generate distribution data: {str(model_error)}",
                    "corrections": corrections_made,
                    "chart_data": None
                }), 500
        
        # Generate chart using PlotUtils
        plotter = PlotUtils()
        chart_base64 = plotter.unpack_distribution_data(corrected_event_id)
        
        return jsonify({
            "success": True,
            "event_id": corrected_event_id,
            "chart_data": chart_base64,
            "corrections": corrections_made,
            "data_source": "database" if distribution_data else "generated"
        })
        
    except Exception as e:
        print(f"❌ Error in /api/player_chart endpoint for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "event_id": event_id,
            "message": f"Error generating chart: {str(e)}",
            "chart_data": None
        }), 500

def calculate_dynamic_confidence(event_id: str, expert_confidence: float) -> float:
    """
    Calculate dynamic confidence based on ML model prediction vs expert confidence.
    
    Args:
        event_id (str): The event ID to get ML prediction for
        expert_confidence (float): Expert confidence as decimal (0.0-1.0)
    
    Returns:
        float: Scaled confidence value (0-100)
    
    Steps:
    1. Call main_model(event_id) to get ML predictions
    2. Extract combined_probability or team_kde_prob from model output
    3. Calculate difference between model probability and expert confidence
    4. Scale difference to 0-100% where -20% = 0% and 20% = 100%
    """
    try:
        print(f"🔍 Running main_model for dynamic confidence calculation: {event_id}")
        
        # Step 1: Call main_model to get ML predictions
        model_result = main_model(event_id)
        
        if not model_result:
            print(f"❌ Model returned None for {event_id}, using fallback confidence")
            return expert_confidence * 100  # Return original expert confidence as percentage
            
        if isinstance(model_result, dict) and "error" in model_result:
            print(f"❌ Model returned error for {event_id}: {model_result.get('error', 'Unknown error')}")
            return expert_confidence * 100  # Return original expert confidence as percentage
        
        # Step 2: Extract combined_probability or team_kde_prob from model output
        model_prob = model_result.get("combined_prob")
        if model_prob is None:
            model_prob = model_result.get("team_kde_prob")
            
        if model_prob is None:
            print(f"❌ No model probability found in result for {event_id}, using fallback confidence")
            return expert_confidence * 100  # Return original expert confidence as percentage
        
        print(f"📊 Model probability: {model_prob:.4f}")
        print(f"👤 Expert confidence: {expert_confidence:.4f}")
        
        # Step 3: Calculate difference between model probability and expert confidence
        difference = model_prob - expert_confidence
        print(f"📈 Difference (Model - Expert): {difference:.4f}")
        
        # Step 4: Scale difference to 0-100% where -20% = 0% and 20% = 100%
        # Clamp difference to [-0.2, 0.2] range for mapping bounds
        clamped_difference = max(-0.2, min(0.2, difference))
        
        # Scale clamped difference to 0-100% where -20% = 0% and 20% = 100%
        # Formula: scaled_confidence = ((clamped_difference + 0.2) / 0.4) * 100
        scaled_confidence = ((clamped_difference + 0.2) / 0.4) * 100
        
        print(f"🎯 Calculated dynamic confidence: {scaled_confidence:.1f}% (clamped difference: {clamped_difference:.4f})")
        return scaled_confidence
        
    except Exception as e:
        print(f"❌ Error calculating dynamic confidence for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return expert_confidence * 100  # Return original expert confidence as percentage

def cleanup_chart_generation_predictions():
    """Clean up phantom ChartGeneration expert predictions."""
    try:
        from data_science_modules.planet_scale_port import get_connection
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM expert_predictions WHERE expert_name = %s", ("ChartGeneration",))
        deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return deleted_count
        
    except Exception as e:
        print(f"Error cleaning up ChartGeneration predictions: {e}")
        return 0

def calculate_dynamic_confidence_vs_expert(event_id: str, expert_confidence_percentage: float) -> float:
    """
    Calculate confidence based on model prediction vs expert's original confidence.
    
    Steps:
    1. Get model prediction (team_kde_prob) by running main_model
    2. Calculate difference: model_percentage - expert_confidence_percentage
    3. Map difference from [-20%, +20%] scale to [0%, 100%] confidence
    """
    try:
        # Step 1: Get model prediction
        print(f"🔍 Running main_model for event_id: {event_id}")
        try:
            model_result = main_model(event_id)
            
            if not model_result:
                print(f"❌ Model returned None for {event_id}, using default confidence")
                return 50.0
                
            if isinstance(model_result, dict) and "error" in model_result:
                print(f"❌ Model returned error for {event_id}: {model_result.get('error', 'Unknown error')}")
                return 50.0
                
        except Exception as model_error:
            print(f"❌ Model threw exception for {event_id}: {model_error}")
            return 50.0
        
        # Get team_kde_prob first, fall back to combined_prob if KDE failed
        team_kde_prob = model_result.get("team_kde_prob")
        if team_kde_prob is not None:
            print(f"✅ Using team_kde_prob: {team_kde_prob:.4f}")
            model_percentage = float(team_kde_prob) * 100
        else:
            combined_prob = model_result.get("combined_prob")
            if combined_prob is not None:
                print(f"🔄 Using combined_prob (team_kde_prob unavailable): {combined_prob:.4f}")
                model_percentage = float(combined_prob) * 100
            else:
                print(f"❌ No model probability returned for {event_id}, using default confidence")
                return 50.0
        print(f"📊 Model prediction: {model_percentage:.1f}%")
        print(f"👤 Expert confidence: {expert_confidence_percentage:.1f}%")
        
        # Step 2: Calculate difference: model - expert
        difference = model_percentage - expert_confidence_percentage
        print(f"📈 Difference (Model - Expert): {difference:.1f}%")
        
        # Step 3: Map difference to confidence scale
        # Clamp difference to [-20%, +20%] range
        clamped_difference = max(-20, min(20, difference))
        
        # Map [-20%, +20%] range to [0%, 100%] confidence scale:
        # -20% difference → 0% confidence (expert much better than model)
        # 0% difference → 50% confidence (model matches expert exactly)  
        # +20% difference → 100% confidence (model much better than expert)
        # Formula: confidence = ((clamped_difference + 20) / 40) * 100
        confidence = ((clamped_difference + 20) / 40) * 100
        
        print(f"🎯 Calculated confidence: {confidence:.1f}% (clamped difference: {clamped_difference:.1f}% mapped from range [-20%, +20%] to [0%, 100%])")
        return confidence
        
    except Exception as e:
        print(f"❌ Error calculating dynamic confidence vs expert for {event_id}: {e}")
        return 50.0  # Default to 50% if calculation fails



# Note: Removed DDL constraint creation - PlanetScale doesn't allow schema modifications
# Application-level duplicate prevention is handled in the insert logic

def repair_json(json_str):
    # Remove potential trailing commas
    json_str = re.sub(r',(\\s*[\\]}])', r'\\1', json_str)
    # Quote unquoted keys (improved regex to handle more cases)
    json_str = re.sub(r'(\\s*?{\\s*?|\\s*?,\\s*?)([\\w\\-]+?)\\s*?:\\s*([\"\'[{\\d]|true|false|null)', r'\\1"\\2": \\3', json_str)
    # Handle unquoted strings as values
    json_str = re.sub(r':\\s*([\\w\\-]+?)(?=[,}\\]])', r': "\\1"', json_str)
    
    # Handle projections with multiple objects separated by commas
    if '"projections"' in json_str:
        # Check if it's in the format: "projections": {obj1}, {obj2}, ...
        projections_pattern = r'"projections"\s*:\s*(\{[^}]*\}(?:\s*,\s*\{[^}]*\})*)\s*\}'
        match = re.search(projections_pattern, json_str, re.DOTALL)
        if match:
            objects_str = match.group(1)
            # Wrap the objects in array brackets
            fixed_projections = f'"projections": [ {objects_str} ]'
            json_str = re.sub(projections_pattern, fixed_projections + ' }', json_str, flags=re.DOTALL)
    
    return json_str

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000, debug=True)