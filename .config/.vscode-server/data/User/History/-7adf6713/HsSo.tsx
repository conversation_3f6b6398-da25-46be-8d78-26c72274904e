import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import ViewContainer from "../../components/ViewContainer";
import LoadingScreen from "../../components/LoadingScreen";
import { useTodaysEvents } from "../../hooks/useTodaysEvents";
import {
  convertEventsToPicks,
  convertEventsToHandicappers,
  Handicapper,
} from "../../utils/dataTransforms";
import { invalidateEventsCache } from "../../utils/cache";
import { useCurrentDate } from "../../contexts/DateContext";

function AddPicksPage() {
  const { navigateToView } = useAuth();

  // Get current date and auto-update flag from DateContext
  const currentDate = useCurrentDate();

  // Determine which date to use based on auto-update setting
  // When autoUpdateEnabled is true, we use the backend's current date (no custom date).
  // When autoUpdateEnabled is false, admin may have set a custom date via settings.
  // The DateContext's currentDate already reflects the correct date, so we pass undefined to let the hook use the default.
  const targetDate = undefined; // Let useTodaysEvents decide based on backend settings

  const {
    events,
    loading,
    error,
    refetch,
    forceRefresh,
    isCacheUsed,
    isDataFresh,
    cacheAge,
  } = useTodaysEvents(targetDate);
  const [picks, setPicks] = useState<any[]>([]);
  const [handicappers, setHandicappers] = useState<Handicapper[]>([]);
  const [showContent, setShowContent] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleBackToHome = () => {
    navigateToView("home");
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      console.log("🔄 Manual refresh triggered");
      // Invalidate cache first to ensure fresh data
      invalidateEventsCache();
      // Force refresh to bypass any remaining cache
      await forceRefresh();
      console.log("✅ Manual refresh completed");
    } catch (error) {
      console.error("❌ Manual refresh failed:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Refetch data when the current date changes (e.g., after admin updates date)
  useEffect(() => {
    console.log("📅 Date setting changed, refetching data for:", currentDate);
    // Invalidate cache to ensure fresh data for the new date
    invalidateEventsCache();
    // Refetch with the new date (if any) – passing undefined lets the hook use the current context date
    refetch(targetDate);
  }, [currentDate, refetch, targetDate]);

  // Convert events to picks format when events data changes
  useEffect(() => {
    if (events.length > 0) {
      console.log("✅ Converting", events.length, "events to picks format");
      const convertedPicks = convertEventsToPicks(events);
      console.log("🎯 Converted picks:", convertedPicks);

      console.log(
        "✅ Converting",
        events.length,
        "events to handicappers format"
      );
      const convertedHandicappers = convertEventsToHandicappers(events);
      console.log("🏆 Converted handicappers:", convertedHandicappers);

      setPicks(convertedPicks);
      setHandicappers(convertedHandicappers);
    } else {
      console.log(
        "❌ No events data received, clearing picks and handicappers"
      );
      setPicks([]); // Clear picks when no events
      setHandicappers([]); // Clear handicappers when no events
    }
  }, [events]);

  // Skip loading animation if cached data is available
  useEffect(() => {
    if (isCacheUsed) {
      console.log("⚡ Cached data available, skipping loading animation");
      setShowContent(true);
    }
  }, [isCacheUsed]);

  // Show loading state only if no cached data is available
  if (loading && !isCacheUsed) {
    return (
      <LoadingScreen
        videoSrc="/project_parlay_loading_anim.mp4"
        isLoading={loading}
        onAnimationComplete={() => setShowContent(true)}
        loadingText="Loading picks..."
        subText={`📅 Current date: ${currentDate}`}
      />
    );
  }

  // Show LoadingScreen while loading (without cache) or until animation completes
  if ((loading && !isCacheUsed) || (!showContent && !isCacheUsed)) {
    return (
      <LoadingScreen
        videoSrc="/project_parlay_loading_anim.mp4"
        isLoading={loading}
        onAnimationComplete={() => setShowContent(true)}
        loadingText="Loading picks..."
        subText={`📅 Current date: ${currentDate}`}
      />
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#061844] text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 text-lg mb-4">
            Error loading picks: {error}
          </p>
          <button
            onClick={() => {
              setShowContent(false); // Reset content visibility for potential loading
              refetch();
            }}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ViewContainer
      onBackToHome={handleBackToHome}
      picks={picks.length > 0 ? picks : []} // Use real data or empty array
      handicappers={handicappers.length > 0 ? handicappers : []} // Use real handicappers data
      events={events}
      onRefresh={handleRefresh}
      isRefreshing={isRefreshing}
      dataFreshness={{
        isDataFresh,
        cacheAge,
        isCacheUsed,
      }}
    />
  );
}

export default AddPicksPage;
