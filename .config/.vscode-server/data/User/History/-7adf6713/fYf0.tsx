import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import ViewContainer from "../../components/ViewContainer";
import LoadingScreen from "../../components/LoadingScreen";
import { useTodaysEvents } from "../../hooks/useTodaysEvents";
import { HiRefresh } from "react-icons/hi";
import {
  convertEventsToPicks,
  convertEventsToHandicappers,
  Handicapper,
} from "../../utils/dataTransforms";
import { invalidateEventsCache } from "../../utils/cache";
import { useCurrentDate } from "../../contexts/DateContext";

// NOTE: The date is now managed entirely by DateContext (auto‑update or manual).
// The component always uses the currentDate from the context.
// The previous ENABLE_CUSTOM_DATE flag has been removed.

function AddPicksPage() {
  const { navigateToView } = useAuth();
  
  // Get current date from DateContext
  const currentDate = useCurrentDate();

  // Pass the current date to the hook so it fetches events for that date
  const {
    events,
    loading,
    error,
    refetch,
    forceRefresh,
    isCacheUsed,
    isDataFresh,
    cacheAge,
  } = useTodaysEvents(currentDate);
  const [picks, setPicks] = useState<any[]>([]);
  const [handicappers, setHandicappers] = useState<Handicapper[]>([]);
  const [showContent, setShowContent] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleBackToHome = () => {
    navigateToView("home");
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      console.log("🔄 Manual refresh triggered");
      // Invalidate cache first to ensure fresh data
      invalidateEventsCache();
      // Force refresh to bypass any remaining cache
      await forceRefresh();
      console.log("✅ Manual refresh completed");
    } catch (error) {
      console.error("❌ Manual refresh failed:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Refetch data when the current date changes (auto‑update or manual change)
  useEffect(() => {
    console.log("📅 Date setting changed, refetching data for:", currentDate);
    // Invalidate cache to ensure fresh data for the new date
    invalidateEventsCache();
    // Refetch with the new date (currentDate)
    refetch(currentDate);
  }, [currentDate, refetch]);

  // Convert events to picks format when events data changes
  useEffect(() => {
    if (events.length > 0) {
      console.log("✅ Converting", events.length, "events to picks format");
      const convertedPicks = convertEventsToPicks(events);
      console.log("🎯 Converted picks:", convertedPicks);

      console.log(
        "✅ Converting",
        events.length,
        "events to handicappers format"
      );
      const convertedHandicappers = convertEventsToHandicappers(events);
      console.log("🏆 Converted handicappers:", convertedHandicappers);

      setPicks(convertedPicks);
      setHandicappers(convertedHandicappers);
    } else {
      console.log(
        "❌ No events data received, clearing picks and handicappers"
      );
      setPicks([]); // Clear picks when no events
      setHandicappers([]); // Clear handicappers when no events
    }
  }, [events]);

  // Skip loading animation if cached data is available
  useEffect(() => {
    if (isCacheUsed) {
      console.log("⚡ Cached data available, skipping loading animation");
      setShowContent(true);
    }
  }, [isCacheUsed]);

  // Show loading state only if no cached data is available
  if (loading && !isCacheUsed) {
    return (
      <LoadingScreen
        videoSrc="/project_parlay_loading_anim.mp4"
        isLoading={loading}
        onAnimationComplete={() => setShowContent(true)}
        loadingText="Loading picks..."
        // Show the current date in the loading screen for context
        subText={`📅 Current date: ${currentDate}`}
      />
    );
  }

  // Show LoadingScreen while loading (without cache) or until animation completes
  if ((loading && !isCacheUsed) || (!showContent && !isCacheUsed)) {
    return (
      <LoadingScreen
        videoSrc="/project_parlay_loading_anim.mp4"
        isLoading={loading}
        onAnimationComplete={() => setShowContent(true)}
        loadingText="Loading picks..."
        // Show the current date in the loading screen for context
        subText={`📅 Current date: ${currentDate}`}
      />
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#061844] text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 text-lg mb-4">
            Error loading picks: {error}
          </p>
          <button
            onClick={() => {
              setShowContent(false); // Reset content visibility for potential loading
              refetch();
            }}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ViewContainer
      onBackToHome={handleBackToHome}
      picks={picks.length > 0 ? picks : []} // Use real data or empty array
      handicappers={handicappers.length > 0 ? handicappers : []} // Use real handicappers data
      events={events}
      onRefresh={handleRefresh}
      isRefreshing={isRefreshing}
      dataFreshness={{
        isDataFresh,
        cacheAge,
        isCacheUsed,
      }}
    />
  );
}

export default AddPicksPage;
