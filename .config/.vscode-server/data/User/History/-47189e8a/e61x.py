#!/usr/bin/env python3
"""
Debug script for the insert_picks endpoint.
This script can help test the endpoint locally and diagnose issues.
"""

import os
import sys
import json
import requests
from datetime import datetime

def test_local_endpoint():
    """Test the endpoint locally using Flask test client"""
    try:
        from app import app
        
        test_data = {
            "picks": [{
                "league": "MLB",
                "team_a": "Astros",
                "team_b": "Red Sox",
                "pick_type": "MoneyLine",
                "player_name": "",
                "stat_type": "MoneyLine",
                "stat_threshold": 0,
                "odds": "-130",
                "prediction": 0,
                "expert_name": "Sports Corner Daily",
                "event_date": "2025-08-03",
                "prediction_time": "2025-08-03 11:00:00"
            }]
        }
        
        with app.test_client() as client:
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': 'pk_ext_api_2025_ppadmin'
            }
            
            response = client.post('/api/external/insert_picks', 
                                 data=json.dumps(test_data), 
                                 headers=headers)
            
            print(f"Local Test Status: {response.status_code}")
            if response.status_code == 200:
                print(f"Success Response: {response.get_json()}")
            else:
                print(f"Error Response: {response.data.decode()}")
                
    except Exception as e:
        print(f"Local test failed: {e}")
        import traceback
        traceback.print_exc()

def test_remote_endpoint(base_url="https://project-parlay-mattfavela.replit.app"):
    """Test the remote endpoint"""
    try:
        test_data = {
            "picks": [{
                "league": "MLB",
                "team_a": "Astros",
                "team_b": "Red Sox",
                "pick_type": "MoneyLine",
                "player_name": "",
                "stat_type": "MoneyLine",
                "stat_threshold": 0,
                "odds": "-130",
                "prediction": 0,
                "expert_name": "Sports Corner Daily",
                "event_date": "2025-08-03",
                "prediction_time": "2025-08-03 11:00:00"
            }]
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': 'pk_ext_api_2025_ppadmin'
        }
        
        url = f"{base_url}/api/external/insert_picks"
        print(f"Testing remote endpoint: {url}")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"Remote Test Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"Success Response: {response.json()}")
        else:
            print(f"Error Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Remote test failed: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()

def check_dependencies():
    """Check if all required dependencies are available"""
    required_modules = [
        'flask', 'flask_cors', 'mysql.connector', 'pymysql', 
        'pandas', 'numpy', 'scikit-learn', 'requests'
    ]
    
    print("Checking dependencies...")
    missing = []
    
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module} - MISSING")
            missing.append(module)
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
    else:
        print("\nAll dependencies are available!")

def check_environment():
    """Check environment variables"""
    required_env_vars = [
        'DATABASE_HOST', 'DATABASE_USERNAME', 'DATABASE_PASSWORD', 
        'DATABASE', 'EXTERNAL_API_KEY'
    ]
    
    print("Checking environment variables...")
    missing_env = []
    
    for var in required_env_vars:
        value = os.getenv(var)
        if value:
            print(f"✓ {var} = {value[:10]}..." if len(value) > 10 else f"✓ {var} = {value}")
        else:
            print(f"✗ {var} - MISSING")
            missing_env.append(var)
    
    if missing_env:
        print(f"\nMissing environment variables: {', '.join(missing_env)}")
    else:
        print("\nAll environment variables are configured!")

def test_database_connection():
    """Test database connection"""
    try:
        from data_science_modules.planet_scale_port import get_connection
        
        print("Testing database connection...")
        conn = get_connection()
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT COUNT(*) FROM events LIMIT 1")
        result = cursor.fetchone()
        
        print(f"✓ Database connection successful! Events table has {result[0] if result else 0} records")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("=== Project Parlay Insert Picks Debug Script ===")
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Check dependencies
    check_dependencies()
    print()
    
    # Check environment
    check_environment()
    print()
    
    # Test database connection
    test_database_connection()
    print()
    
    # Test local endpoint
    print("=== Local Endpoint Test ===")
    test_local_endpoint()
    print()
    
    # Test remote endpoint
    print("=== Remote Endpoint Test ===")
    test_remote_endpoint()

if __name__ == "__main__":
    main()
