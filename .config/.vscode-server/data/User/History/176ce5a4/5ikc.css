/* Login page and animation overlay styles */
.login-root {
  width: 100vw;
  height: 100vh;
  background: #fff;
  position: relative;
  overflow: hidden;
}

.animation-overlay {
  position: fixed;
  z-index: 10000;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.8s ease;
  opacity: 1;
  pointer-events: all;
}
.animation-overlay.fade-out {
  opacity: 0;
  pointer-events: none;
}

.animation-video {
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  background: #fff;
  display: block;
}

.animation-placeholder {
  width: 100vw;
  height: 100vh;
  background: #fff;
}

.login-content {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}
