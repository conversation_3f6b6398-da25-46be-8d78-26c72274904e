import React, { useEffect, useRef, useState } from 'react';
import './Login.css';

const ANIMATION_VIDEO = '/project_parlay_loading_anim.mp4';

function Login() {
  const [showAnimation, setShowAnimation] = useState(true);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [videoStarted, setVideoStarted] = useState(false);
  const videoRef = useRef(null);
  const [firstFrameLoaded, setFirstFrameLoaded] = useState(false);

  useEffect(() => {
    if (videoLoaded && !videoStarted) {
      // Start animation after video is ready
      setTimeout(() => {
        setVideoStarted(true);
        videoRef.current.play();
      }, 200); // slight delay for UX
    }
  }, [videoLoaded, videoStarted]);

  const handleVideoLoadedData = () => {
    setFirstFrameLoaded(true);
    setVideoLoaded(true);
    videoRef.current.currentTime = 0;
    videoRef.current.pause();
  };

  const handleVideoEnded = () => {
    setShowAnimation(false);
  };

  return (
    <div className="login-root">
      {showAnimation && (
        <div className={`animation-overlay${videoStarted ? ' fade-out' : ''}`}> 
          <video
            ref={videoRef}
            className="animation-video"
            src={ANIMATION_VIDEO}
            preload="auto"
            playsInline
            onLoadedData={handleVideoLoadedData}
            onEnded={handleVideoEnded}
            style={{ objectFit: 'cover', width: '100vw', height: '100vh', display: firstFrameLoaded ? 'block' : 'none' }}
          />
          {!firstFrameLoaded && <div className="animation-placeholder" />}
        </div>
      )}
      <div className="login-content">
        {/* Blank login page for now */}
      </div>
    </div>
  );
}

export default Login;
