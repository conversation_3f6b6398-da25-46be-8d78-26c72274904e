// Place your key bindings in this file to override the defaultsauto[]
[
  {
    "key": "ctrl+r",
    "command": "editor.action.changeAll",
    "when": "editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+f2",
    "command": "-editor.action.changeAll",
    "when": "editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+i",
    "command": "editor.action.inlineSuggest.trigger",
    "when": "config.github.copilot.inlineSuggest.enable && editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"
  },
  {
    "key": "alt+\\",
    "command": "-editor.action.inlineSuggest.trigger",
    "when": "config.github.copilot.inlineSuggest.enable && editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"
  },
  {
    "key": "ctrl+i",
    "command": "-editor.action.triggerSuggest",
    "when": "editorHasCompletionItemProvider && textInputFocus && !editorReadonly && !suggestWidgetVisible"
  },
  {
    "key": "ctrl+i",
    "command": "-focusSuggestion",
    "when": "suggestWidgetVisible && textInputFocus && !suggestWidgetHasFocusedSuggestion"
  },
  {
    "key": "ctrl+i",
    "command": "-inlineChat.holdForSpeech",
    "when": "hasSpeechProvider && inlineChatHasProvider && inlineChatVisible && textInputFocus"
  },
  {
    "key": "ctrl+i",
    "command": "-toggleSuggestionDetails",
    "when": "suggestWidgetHasFocusedSuggestion && suggestWidgetVisible && textInputFocus"
  },
  {
    "key": "ctrl+i",
    "command": "-workbench.action.chat.holdToVoiceChatInChatView",
    "when": "hasChatProvider && hasSpeechProvider && !editorFocus && !inChatInput && !inlineChatFocused"
  },
  {
    "key": "ctrl+i",
    "command": "-workbench.action.chat.startVoiceChat",
    "when": "hasChatProvider && hasSpeechProvider && inChatInput && !chatSessionRequestInProgress && !editorFocus && !inlineChatHasActiveRequest && !inlineVoiceChatInProgress && !quickVoiceChatInProgress && !voiceChatGettingReady && !voiceChatInEditorInProgress && !voiceChatInViewInProgress || hasChatProvider && hasSpeechProvider && inlineChatFocused && !chatSessionRequestInProgress && !editorFocus && !inlineChatHasActiveRequest && !inlineVoiceChatInProgress && !quickVoiceChatInProgress && !voiceChatGettingReady && !voiceChatInEditorInProgress && !voiceChatInViewInProgress"
  },
  {
    "key": "ctrl+i",
    "command": "-workbench.action.chat.stopListeningAndSubmit",
    "when": "hasChatProvider && hasSpeechProvider && inChatInput && voiceChatInProgress || hasChatProvider && hasSpeechProvider && inlineChatFocused && voiceChatInProgress"
  },
  {
    "key": "ctrl+i",
    "command": "-inlineChat.start",
    "when": "editorFocus && inlineChatHasProvider && !editorReadonly"
  },
  {
    "key": "ctrl+i",
    "command": "editor.action.accessibleViewAcceptInlineCompletion",
    "when": "accessibleViewIsShown && accessibleViewCurrentProviderId == 'inlineCompletions'"
  },
  {
    "key": "ctrl+/",
    "command": "-editor.action.accessibleViewAcceptInlineCompletion",
    "when": "accessibleViewIsShown && accessibleViewCurrentProviderId == 'inlineCompletions'"
  },
  {
    "key": "ctrl+i",
    "command": "workbench.action.openQuickChat.copilot"
  },
  {
    "key": "ctrl+i",
    "command": "workbench.action.openQuickChat"
  },
  {
    "key": "home",
    "command": "workbench.action.chat.startVoiceChat",
    "when": "chatIsEnabled && hasSpeechProvider && !chatSessionRequestInProgress && !scopedVoiceChatGettingReady && !speechToTextInProgress"
  }
]