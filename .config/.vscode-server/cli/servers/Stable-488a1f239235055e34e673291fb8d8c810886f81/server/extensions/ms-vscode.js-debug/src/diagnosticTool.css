*{margin:0;padding:0}:root{--subtle-bg: rgba(0, 0, 0, .05)}body{color:var(--vscode-foreground)}a{cursor:pointer;text-decoration:underline}a:hover{text-decoration:none}.back{padding:1rem 0;display:inline-block}.intro{display:flex;height:100vh;justify-content:center;align-items:center}.intro>div{overflow:hidden;box-shadow:0 5px 30px #0003;border:1px solid var(--vscode-notebook-focusedCellBorder)}.intro>div,.content,.source-container.expanded{background:var(--vscode-notebook-focusedCellBackground);border:1px solid var(--vscode-notebook-focusedCellBorder)}.intro header{background:var(--subtle-bg);padding:1rem}.intro-content{padding:1rem}.intro-content li{list-style-type:none}.intro-content li a{display:block;margin-top:.5rem;padding:.5rem}.intro-content li a:hover{background:var(--subtle-bg)}.content{margin:2rem;padding:1rem}.source-filter{display:block;width:100%;padding:.5rem;font-size:1.1rem;margin-bottom:.5rem;background:var(--vscode-input-background);color:var(--vscode-input-foreground);border:none}.source-container{padding:.5rem}.source-container code{word-break:break-all}.source-container.expanded+.source-container.expanded{border-top:none}.source-container h2{font-family:var(--editor-font-family);font-weight:300;cursor:pointer;opacity:.8;font-size:1.2rem;user-select:none}.source-container h2:hover{opacity:1}.source-container.expanded h2{margin-bottom:.5rem}.source-data-grid{display:grid;grid-template-columns:25% 75%;row-gap:.5rem;padding:.5rem .25rem}.source-data-grid li{list-style-type:none}.source-breadcrumbs{padding:.5rem .25rem .75rem;border-bottom:1px solid rgba(0,0,0,.1)}.source-breadcrumbs li{display:inline;list-style-type:none}.bp-tracing code{font-size:var(--vscode-editor-font-size)}.bp-tracing li{margin:1rem 0 1rem 1rem}.bp-tracing p{margin:.5rem 0}.bp-tracing li ul{margin:.5rem 1rem}.decision-buttons{margin:.5rem 0}button{background:var(--vscode-button-background);color:var(--vscode-button-foreground);border:0;padding:.3rem .6rem;cursor:pointer}button:hover,button:focus{background:var(--vscode-button-hoverBackground)}.decision-buttons button+button{margin-left:.25rem}.decision-buttons button.active{background:var(--vscode-button-hoverBackground)}.text-diff .add{background:var(--vscode-diffEditor-insertedTextBackground);border:1px solid var(--vscode-diffEditor-insertedTextBorder)}.text-diff .rm{background:var(--vscode-diffEditor-removedTextBackground);text-decoration:line-through;border:1px solid var(--vscode-diffEditor-removedTextBorder)}
/*# sourceMappingURL=diagnosticTool.css.map */
