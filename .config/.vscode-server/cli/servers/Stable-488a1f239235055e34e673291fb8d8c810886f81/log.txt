*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[07:52:16] 




[07:52:17] Extension host agent started.
[07:52:17] [<unknown>][12b43e50][ExtensionHostConnection] New connection established.
[07:52:17] [<unknown>][387ee8a8][ManagementConnection] New connection established.
[07:52:17] [<unknown>][12b43e50][ExtensionHostConnection] <979> Launched Extension Host Process.
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/github.copilot-1.338.1648'
    at async Object.readdir (node:internal/fs/promises:949:18)
    at async GL (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:88034)
    at async Object.Fu (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:87963)
    at async uu.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:67:15795)
    at async tl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:52500)
    at async tl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:51616)
    at async Yh.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:25985)
    at async Promise.all (index 2)
    at async Yh.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:20524)
    at async N7.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:9952)
    at async N7.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/github.copilot-1.338.1648'
}
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3'
    at async Object.readdir (node:internal/fs/promises:949:18)
    at async GL (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:88034)
    at async Object.Fu (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:87963)
    at async uu.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:67:15795)
    at async tl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:52500)
    at async tl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:51616)
    at async Yh.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:25985)
    at async Promise.all (index 4)
    at async Yh.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:20524)
    at async N7.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:9952)
    at async N7.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3'
}
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.112.0'
    at async Object.readdir (node:internal/fs/promises:949:18)
    at async GL (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:88034)
    at async Object.Fu (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:87963)
    at async uu.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:67:15795)
    at async tl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:52500)
    at async tl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:51616)
    at async Yh.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:25985)
    at async Promise.all (index 6)
    at async Yh.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:20524)
    at async N7.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:9952)
    at async N7.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.112.0'
}
[node.js fs] readdir with filetypes failed with error:  Error: ENOENT: no such file or directory, scandir '/home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.22.5'
    at async Object.readdir (node:internal/fs/promises:949:18)
    at async GL (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:88034)
    at async Object.Fu (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:27:87963)
    at async uu.readdir (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:67:15795)
    at async tl.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:52500)
    at async tl.resolve (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:64:51616)
    at async Yh.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:25985)
    at async Promise.all (index 14)
    at async Yh.scanAllUserExtensions (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:20524)
    at async N7.H (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:9952)
    at async N7.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/out/server-main.js:55:7847) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.22.5'
}
[07:52:17] Deleted marked for removal extension from disk github.vscode-pull-request-github /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.112.0
[07:52:17] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3
[07:52:17] Deleted marked for removal extension from disk rooveterinaryinc.roo-cline /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.22.5
[07:52:17] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.338.1648
[07:52:20] Getting Manifest... augment.vscode-augment
[07:52:20] Getting Manifest... github.copilot
[07:52:20] Getting Manifest... ms-windows-ai-studio.windows-ai-studio
[07:52:20] Getting Manifest... rooveterinaryinc.roo-cline
[07:52:21] Installing extension: ms-windows-ai-studio.windows-ai-studio {
  installPreReleaseVersion: true,
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[07:52:21] Installing extension: rooveterinaryinc.roo-cline {
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[07:52:24] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[07:52:25] Extension signature verification result for rooveterinaryinc.roo-cline: Success. Internal Code: 0. Executed: true. Duration: 2828ms.
[07:52:25] Installing extension: github.copilot {
  installPreReleaseVersion: true,
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[07:52:25] Extension signature verification result for ms-windows-ai-studio.windows-ai-studio: Success. Internal Code: 0. Executed: true. Duration: 1722ms.
[07:52:27] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1557ms.
[07:52:29] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1970ms.
[07:52:29] [<unknown>][387ee8a8][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[07:52:31] [<unknown>][12b43e50][ExtensionHostConnection] <979> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[07:52:31] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[07:52:31] Last EH closed, waiting before shutting down
[07:52:32] Extracted extension to file:///home/<USER>/.vscode-server/extensions/ms-windows-ai-studio.windows-ai-studio-0.18.2-linux-x64: ms-windows-ai-studio.windows-ai-studio
[07:52:33] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.353.1721: github.copilot
[07:52:33] [<unknown>][0bdc2f44][ManagementConnection] New connection established.
[07:52:33] [<unknown>][a2352ee4][ExtensionHostConnection] New connection established.
[07:52:33] [<unknown>][a2352ee4][ExtensionHostConnection] <1166> Launched Extension Host Process.
[07:52:34] Renamed to /home/<USER>/.vscode-server/extensions/ms-windows-ai-studio.windows-ai-studio-0.18.2-linux-x64
[07:52:34] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.353.1721
[07:52:34] Marked extension as removed ms-windows-ai-studio.windows-ai-studio-0.18.0-linux-x64
[07:52:34] Extension installed successfully: ms-windows-ai-studio.windows-ai-studio file:///home/<USER>/.vscode-server/extensions/extensions.json
[07:52:34] Marked extension as removed github.copilot-1.351.1711
[07:52:34] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[07:52:35] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.524.1: augment.vscode-augment
[07:52:35] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.524.1
[07:52:35] Marked extension as removed augment.vscode-augment-0.516.3
[07:52:35] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[07:52:36] Getting Manifest... rooveterinaryinc.roo-cline
[07:52:36] Extension is already requested to install rooveterinaryinc.roo-cline file:///home/<USER>/.vscode-server/extensions/extensions.json
[07:52:36] Extracted extension to file:///home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8: rooveterinaryinc.roo-cline
[07:52:36] Renamed to /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8
[07:52:36] Extension installed successfully: rooveterinaryinc.roo-cline file:///home/<USER>/.vscode-server/extensions/extensions.json
[07:52:36] Marked extension as removed rooveterinaryinc.roo-cline-3.25.6
[07:52:42] Downloaded extension to file:///home/<USER>/.vscode-server/data/CachedExtensionVSIXs/925444ed-8511-4115-8740-0b8c81b3a539
[07:52:42] Installing extension: rooveterinaryinc.roo-cline {
  productVersion: { version: '1.102.3', date: '2025-07-29T03:00:23.339Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  donotIncludePackAndDependencies: true,
  keepExisting: true,
  profileLocation: xr {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[07:52:42] Installing the extension without checking dependencies and pack rooveterinaryinc.roo-cline
[07:52:42] Deleted existing extension from disk rooveterinaryinc.roo-cline /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8
[07:52:48] Extracted extension to file:///home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8: rooveterinaryinc.roo-cline
[07:52:48] Renamed to /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.25.8
[07:52:48] Extension installed successfully: rooveterinaryinc.roo-cline file:///home/<USER>/.vscode-server/extensions/extensions.json
[07:52:56] [<unknown>][0bdc2f44][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[07:53:00] [<unknown>][9aabde87][ExtensionHostConnection] New connection established.
[07:53:00] [<unknown>][53240904][ManagementConnection] New connection established.
[07:53:00] [<unknown>][9aabde87][ExtensionHostConnection] <1712> Launched Extension Host Process.
[07:53:02] [<unknown>][a2352ee4][ExtensionHostConnection] <1166> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[07:53:02] Cancelling previous shutdown timeout
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:62270
stack trace: Error: connect ECONNREFUSED 127.0.0.1:62270
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[07:53:27] Error: connect ECONNREFUSED 127.0.0.1:62270
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 62270
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:62270
stack trace: Error: connect ECONNREFUSED 127.0.0.1:62270
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[07:53:27] Error: connect ECONNREFUSED 127.0.0.1:62270
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 62270
}
