*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[19:12:15] 




[19:12:15] Extension host agent started.
[19:12:15] [<unknown>][f199505b][ManagementConnection] New connection established.
[19:12:15] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.0]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[19:12:15] [<unknown>][3cc86977][ExtensionHostConnection] New connection established.
[19:12:15] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.0]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[19:12:15] [<unknown>][3cc86977][ExtensionHostConnection] <2202> Launched Extension Host Process.
[19:12:15] Deleted marked for removal extension from disk github.vscode-pull-request-github /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0
[19:12:15] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.0
[19:12:15] Deleted marked for removal extension from disk rooveterinaryinc.roo-cline /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.17.0
[19:12:16] ComputeTargetPlatform: linux-x64
[19:12:16] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.321.1568
[19:12:16] [<unknown>][f199505b][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[19:12:17] [<unknown>][3cc86977][ExtensionHostConnection] <2202> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[19:12:17] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[19:12:17] Last EH closed, waiting before shutting down
