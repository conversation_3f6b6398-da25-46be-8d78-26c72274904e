*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[15:36:06] 




[15:36:06] Extension host agent started.
[15:36:06] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[15:36:06] ComputeTargetPlatform: linux-x64
[15:36:06] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[15:36:06] [<unknown>][95c42e29][ExtensionHostConnection] New connection established.
[15:36:06] [<unknown>][765edf14][ManagementConnection] New connection established.
[15:36:06] [<unknown>][95c42e29][ExtensionHostConnection] <739> Launched Extension Host Process.
[15:36:07] ComputeTargetPlatform: linux-x64
[15:36:07] Getting Manifest... github.copilot
[15:36:07] Getting Manifest... github.copilot-chat
[15:36:07] Getting Manifest... github.vscode-pull-request-github
[15:36:07] Installing extension: github.copilot {
  installPreReleaseVersion: true,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.1', date: '2025-05-09T15:43:50.040Z' }
}
[15:36:07] Installing the extension without checking dependencies and pack github.copilot
[15:36:07] Installing extension: github.copilot-chat {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.1', date: '2025-05-09T15:43:50.040Z' }
}
[15:36:07] Installing the extension without checking dependencies and pack github.copilot-chat
[15:36:07] Installing extension: github.vscode-pull-request-github {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.1', date: '2025-05-09T15:43:50.040Z' }
}
[15:36:07] Installing the extension without checking dependencies and pack github.vscode-pull-request-github
[15:36:08] Extension signature verification result for github.vscode-pull-request-github: Success. Internal Code: 0. Executed: true. Duration: 1163ms.
[15:36:08] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 1186ms.
[15:36:08] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1211ms.
[15:36:09] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0: github.vscode-pull-request-github
[15:36:09] Renamed to /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0
[15:36:09] Extension installed successfully: github.vscode-pull-request-github file:///home/<USER>/.vscode-server/extensions/extensions.json
[15:36:09] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.0: github.copilot-chat
[15:36:09] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.0
[15:36:09] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[15:36:09] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.321.1568: github.copilot
[15:36:09] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.321.1568
[15:36:09] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[15:36:34] [<unknown>][765edf14][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[15:36:34] [<unknown>][95c42e29][ExtensionHostConnection] <739> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[15:36:34] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[15:36:34] Last EH closed, waiting before shutting down
[15:36:36] [<unknown>][9d8b53dc][ManagementConnection] New connection established.
[15:36:36] [<unknown>][435da59c][ExtensionHostConnection] New connection established.
[15:36:36] [<unknown>][435da59c][ExtensionHostConnection] <987> Launched Extension Host Process.
[15:37:27] Getting Manifest... rooveterinaryinc.roo-cline
[15:37:27] Installing extension: rooveterinaryinc.roo-cline {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'win32-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.1', date: '2025-05-09T15:43:50.040Z' }
}
[15:37:28] Extension signature verification result for rooveterinaryinc.roo-cline: Success. Internal Code: 0. Executed: true. Duration: 667ms.
[15:37:29] Extracted extension to file:///home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.17.0: rooveterinaryinc.roo-cline
[15:37:30] Renamed to /home/<USER>/.vscode-server/extensions/rooveterinaryinc.roo-cline-3.17.0
[15:37:30] Extension installed successfully: rooveterinaryinc.roo-cline file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[15:41:34] New EH opened, aborting shutdown
