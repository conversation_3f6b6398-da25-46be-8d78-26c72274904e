trigger:
  branches:
    include:
      - main

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishExtension
    displayName: 🚀 Publish Extension
    type: boolean
    default: false

extends:
  template: azure-pipelines/extension/stable.yml@templates
  parameters:
    workingDirectory: $(Build.SourcesDirectory)/packages/vscode-js-profile-table
    publishExtension: ${{ parameters.publishExtension }}
    vscePackageArgs: --no-dependencies
    ghCreateRelease: true
    ghReleaseAddChangeLog: true
    buildSteps:
      - script: npm ci
        displayName: Install dependencies

      - script: npm run compile
        displayName: Compile
    tsa:
      config:
        areaPath: 'Visual Studio Code Debugging Extensions'
        serviceTreeID: '053e3ba6-924d-456c-ace0-67812c5ccc52'
      enabled: true