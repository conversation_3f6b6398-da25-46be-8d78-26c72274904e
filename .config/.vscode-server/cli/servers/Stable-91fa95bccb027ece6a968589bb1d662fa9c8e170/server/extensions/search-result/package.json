{"name": "search-result", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "icon": "images/icon.png", "engines": {"vscode": "^1.39.0"}, "main": "./dist/extension.js", "browser": "./dist/extension.js", "activationEvents": ["onLanguage:search-result"], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "enabledApiProposals": ["documentFiltersExclusive"], "contributes": {"configurationDefaults": {"[search-result]": {"editor.lineNumbers": "off"}}, "languages": [{"id": "search-result", "extensions": [".code-search"], "aliases": ["Search Result"]}], "grammars": [{"language": "search-result", "scopeName": "text.searchResult", "path": "./syntaxes/searchResult.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}