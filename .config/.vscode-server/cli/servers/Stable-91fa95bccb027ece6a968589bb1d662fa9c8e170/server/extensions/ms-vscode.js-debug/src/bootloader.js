"use strict";(()=>{var Hs=Object.create;var ur=Object.defineProperty;var Ms=Object.getOwnPropertyDescriptor;var $s=Object.getOwnPropertyNames;var Fs=Object.getPrototypeOf,Us=Object.prototype.hasOwnProperty;var E=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var w=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var js=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of $s(t))!Us.call(e,s)&&s!==r&&ur(e,s,{get:()=>t[s],enumerable:!(n=Ms(t,s))||n.enumerable});return e};var se=(e,t,r)=>(r=e!=null?Hs(Fs(e)):{},js(t||!e||!e.__esModule?ur(r,"default",{value:e,enumerable:!0}):r,e));var Ye=w(Z=>{"use strict";Z.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1;Z.find=(e,t)=>e.nodes.find(r=>r.type===t);Z.exceedsLimit=(e,t,r=1,n)=>n===!1||!Z.isInteger(e)||!Z.isInteger(t)?!1:(Number(t)-Number(e))/Number(r)>=n;Z.escapeNode=(e,t=0,r)=>{let n=e.nodes[t];n&&(r&&n.type===r||n.type==="open"||n.type==="close")&&n.escaped!==!0&&(n.value="\\"+n.value,n.escaped=!0)};Z.encloseBrace=e=>e.type!=="brace"?!1:e.commas>>0+e.ranges>>0===0?(e.invalid=!0,!0):!1;Z.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:e.commas>>0+e.ranges>>0===0||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1;Z.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0;Z.reduce=e=>e.reduce((t,r)=>(r.type==="text"&&t.push(r.value),r.type==="range"&&(r.type="text"),t),[]);Z.flatten=(...e)=>{let t=[],r=n=>{for(let s=0;s<n.length;s++){let o=n[s];if(Array.isArray(o)){r(o);continue}o!==void 0&&t.push(o)}return t};return r(e),t}});var Ze=w((Va,fr)=>{"use strict";var cr=Ye();fr.exports=(e,t={})=>{let r=(n,s={})=>{let o=t.escapeInvalid&&cr.isInvalidBrace(s),i=n.invalid===!0&&t.escapeInvalid===!0,a="";if(n.value)return(o||i)&&cr.isOpenOrClose(n)?"\\"+n.value:n.value;if(n.value)return n.value;if(n.nodes)for(let l of n.nodes)a+=r(l);return a};return r(e)}});var dr=w((Ka,pr)=>{"use strict";pr.exports=function(e){return typeof e=="number"?e-e===0:typeof e=="string"&&e.trim()!==""?Number.isFinite?Number.isFinite(+e):isFinite(+e):!1}});var vr=w((Xa,Sr)=>{"use strict";var hr=dr(),ye=(e,t,r)=>{if(hr(e)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(t===void 0||e===t)return String(e);if(hr(t)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let n={relaxZeros:!0,...r};typeof n.strictZeros=="boolean"&&(n.relaxZeros=n.strictZeros===!1);let s=String(n.relaxZeros),o=String(n.shorthand),i=String(n.capture),a=String(n.wrap),l=e+":"+t+"="+s+o+i+a;if(ye.cache.hasOwnProperty(l))return ye.cache[l].result;let u=Math.min(e,t),c=Math.max(e,t);if(Math.abs(u-c)===1){let m=e+"|"+t;return n.capture?`(${m})`:n.wrap===!1?m:`(?:${m})`}let g=br(e)||br(t),p={min:e,max:t,a:u,b:c},b=[],_=[];if(g&&(p.isPadded=g,p.maxLen=String(p.max).length),u<0){let m=c<0?Math.abs(c):1;_=gr(m,Math.abs(u),p,n),u=p.a=0}return c>=0&&(b=gr(u,c,p,n)),p.negatives=_,p.positives=b,p.result=qs(_,b,n),n.capture===!0?p.result=`(${p.result})`:n.wrap!==!1&&b.length+_.length>1&&(p.result=`(?:${p.result})`),ye.cache[l]=p,p.result};function qs(e,t,r){let n=Tt(e,t,"-",!1,r)||[],s=Tt(t,e,"",!1,r)||[],o=Tt(e,t,"-?",!0,r)||[];return n.concat(o).concat(s).join("|")}function Ws(e,t){let r=1,n=1,s=_r(e,r),o=new Set([t]);for(;e<=s&&s<=t;)o.add(s),r+=1,s=_r(e,r);for(s=yr(t+1,n)-1;e<s&&s<=t;)o.add(s),n+=1,s=yr(t+1,n)-1;return o=[...o],o.sort(Ks),o}function Gs(e,t,r){if(e===t)return{pattern:e,count:[],digits:0};let n=Vs(e,t),s=n.length,o="",i=0;for(let a=0;a<s;a++){let[l,u]=n[a];l===u?o+=l:l!=="0"||u!=="9"?o+=Xs(l,u,r):i++}return i&&(o+=r.shorthand===!0?"\\d":"[0-9]"),{pattern:o,count:[i],digits:s}}function gr(e,t,r,n){let s=Ws(e,t),o=[],i=e,a;for(let l=0;l<s.length;l++){let u=s[l],c=Gs(String(i),String(u),n),g="";if(!r.isPadded&&a&&a.pattern===c.pattern){a.count.length>1&&a.count.pop(),a.count.push(c.count[0]),a.string=a.pattern+xr(a.count),i=u+1;continue}r.isPadded&&(g=zs(u,r,n)),c.string=g+c.pattern+xr(c.count),o.push(c),i=u+1,a=c}return o}function Tt(e,t,r,n,s){let o=[];for(let i of e){let{string:a}=i;!n&&!mr(t,"string",a)&&o.push(r+a),n&&mr(t,"string",a)&&o.push(r+a)}return o}function Vs(e,t){let r=[];for(let n=0;n<e.length;n++)r.push([e[n],t[n]]);return r}function Ks(e,t){return e>t?1:t>e?-1:0}function mr(e,t,r){return e.some(n=>n[t]===r)}function _r(e,t){return Number(String(e).slice(0,-t)+"9".repeat(t))}function yr(e,t){return e-e%Math.pow(10,t)}function xr(e){let[t=0,r=""]=e;return r||t>1?`{${t+(r?","+r:"")}}`:""}function Xs(e,t,r){return`[${e}${t-e===1?"":"-"}${t}]`}function br(e){return/^-?(0+)\d/.test(e)}function zs(e,t,r){if(!t.isPadded)return e;let n=Math.abs(t.maxLen-String(e).length),s=r.relaxZeros!==!1;switch(n){case 0:return"";case 1:return s?"0?":"0";case 2:return s?"0{0,2}":"00";default:return s?`0{0,${n}}`:`0{${n}}`}}ye.cache={};ye.clearCache=()=>ye.cache={};Sr.exports=ye});var At=w((za,kr)=>{"use strict";var Qs=E("util"),Tr=vr(),Er=e=>e!==null&&typeof e=="object"&&!Array.isArray(e),Ys=e=>t=>e===!0?Number(t):String(t),Ct=e=>typeof e=="number"||typeof e=="string"&&e!=="",Ne=e=>Number.isInteger(+e),Rt=e=>{let t=`${e}`,r=-1;if(t[0]==="-"&&(t=t.slice(1)),t==="0")return!1;for(;t[++r]==="0";);return r>0},Zs=(e,t,r)=>typeof e=="string"||typeof t=="string"?!0:r.stringify===!0,Js=(e,t,r)=>{if(t>0){let n=e[0]==="-"?"-":"";n&&(e=e.slice(1)),e=n+e.padStart(n?t-1:t,"0")}return r===!1?String(e):e},et=(e,t)=>{let r=e[0]==="-"?"-":"";for(r&&(e=e.slice(1),t--);e.length<t;)e="0"+e;return r?"-"+e:e},eo=(e,t,r)=>{e.negatives.sort((a,l)=>a<l?-1:a>l?1:0),e.positives.sort((a,l)=>a<l?-1:a>l?1:0);let n=t.capture?"":"?:",s="",o="",i;return e.positives.length&&(s=e.positives.map(a=>et(String(a),r)).join("|")),e.negatives.length&&(o=`-(${n}${e.negatives.map(a=>et(String(a),r)).join("|")})`),s&&o?i=`${s}|${o}`:i=s||o,t.wrap?`(${n}${i})`:i},Cr=(e,t,r,n)=>{if(r)return Tr(e,t,{wrap:!1,...n});let s=String.fromCharCode(e);if(e===t)return s;let o=String.fromCharCode(t);return`[${s}-${o}]`},Rr=(e,t,r)=>{if(Array.isArray(e)){let n=r.wrap===!0,s=r.capture?"":"?:";return n?`(${s}${e.join("|")})`:e.join("|")}return Tr(e,t,r)},Ar=(...e)=>new RangeError("Invalid range arguments: "+Qs.inspect(...e)),wr=(e,t,r)=>{if(r.strictRanges===!0)throw Ar([e,t]);return[]},to=(e,t)=>{if(t.strictRanges===!0)throw new TypeError(`Expected step "${e}" to be a number`);return[]},ro=(e,t,r=1,n={})=>{let s=Number(e),o=Number(t);if(!Number.isInteger(s)||!Number.isInteger(o)){if(n.strictRanges===!0)throw Ar([e,t]);return[]}s===0&&(s=0),o===0&&(o=0);let i=s>o,a=String(e),l=String(t),u=String(r);r=Math.max(Math.abs(r),1);let c=Rt(a)||Rt(l)||Rt(u),g=c?Math.max(a.length,l.length,u.length):0,p=c===!1&&Zs(e,t,n)===!1,b=n.transform||Ys(p);if(n.toRegex&&r===1)return Cr(et(e,g),et(t,g),!0,n);let _={negatives:[],positives:[]},m=$=>_[$<0?"negatives":"positives"].push(Math.abs($)),v=[],k=0;for(;i?s>=o:s<=o;)n.toRegex===!0&&r>1?m(s):v.push(Js(b(s,k),g,p)),s=i?s-r:s+r,k++;return n.toRegex===!0?r>1?eo(_,n,g):Rr(v,null,{wrap:!1,...n}):v},no=(e,t,r=1,n={})=>{if(!Ne(e)&&e.length>1||!Ne(t)&&t.length>1)return wr(e,t,n);let s=n.transform||(p=>String.fromCharCode(p)),o=`${e}`.charCodeAt(0),i=`${t}`.charCodeAt(0),a=o>i,l=Math.min(o,i),u=Math.max(o,i);if(n.toRegex&&r===1)return Cr(l,u,!1,n);let c=[],g=0;for(;a?o>=i:o<=i;)c.push(s(o,g)),o=a?o-r:o+r,g++;return n.toRegex===!0?Rr(c,null,{wrap:!1,options:n}):c},Je=(e,t,r,n={})=>{if(t==null&&Ct(e))return[e];if(!Ct(e)||!Ct(t))return wr(e,t,n);if(typeof r=="function")return Je(e,t,1,{transform:r});if(Er(r))return Je(e,t,0,r);let s={...n};return s.capture===!0&&(s.wrap=!0),r=r||s.step||1,Ne(r)?Ne(e)&&Ne(t)?ro(e,t,r,s):no(e,t,Math.max(Math.abs(r),1),s):r!=null&&!Er(r)?to(r,s):Je(e,t,1,r)};kr.exports=Je});var Nr=w((Qa,Ir)=>{"use strict";var so=At(),Or=Ye(),oo=(e,t={})=>{let r=(n,s={})=>{let o=Or.isInvalidBrace(s),i=n.invalid===!0&&t.escapeInvalid===!0,a=o===!0||i===!0,l=t.escapeInvalid===!0?"\\":"",u="";if(n.isOpen===!0)return l+n.value;if(n.isClose===!0)return console.log("node.isClose",l,n.value),l+n.value;if(n.type==="open")return a?l+n.value:"(";if(n.type==="close")return a?l+n.value:")";if(n.type==="comma")return n.prev.type==="comma"?"":a?n.value:"|";if(n.value)return n.value;if(n.nodes&&n.ranges>0){let c=Or.reduce(n.nodes),g=so(...c,{...t,wrap:!1,toRegex:!0,strictZeros:!0});if(g.length!==0)return c.length>1&&g.length>1?`(${g})`:g}if(n.nodes)for(let c of n.nodes)u+=r(c,n);return u};return r(e)};Ir.exports=oo});var Dr=w((Ya,Lr)=>{"use strict";var io=At(),Pr=Ze(),Ce=Ye(),xe=(e="",t="",r=!1)=>{let n=[];if(e=[].concat(e),t=[].concat(t),!t.length)return e;if(!e.length)return r?Ce.flatten(t).map(s=>`{${s}}`):t;for(let s of e)if(Array.isArray(s))for(let o of s)n.push(xe(o,t,r));else for(let o of t)r===!0&&typeof o=="string"&&(o=`{${o}}`),n.push(Array.isArray(o)?xe(s,o,r):s+o);return Ce.flatten(n)},ao=(e,t={})=>{let r=t.rangeLimit===void 0?1e3:t.rangeLimit,n=(s,o={})=>{s.queue=[];let i=o,a=o.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,a=i.queue;if(s.invalid||s.dollar){a.push(xe(a.pop(),Pr(s,t)));return}if(s.type==="brace"&&s.invalid!==!0&&s.nodes.length===2){a.push(xe(a.pop(),["{}"]));return}if(s.nodes&&s.ranges>0){let g=Ce.reduce(s.nodes);if(Ce.exceedsLimit(...g,t.step,r))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let p=io(...g,t);p.length===0&&(p=Pr(s,t)),a.push(xe(a.pop(),p)),s.nodes=[];return}let l=Ce.encloseBrace(s),u=s.queue,c=s;for(;c.type!=="brace"&&c.type!=="root"&&c.parent;)c=c.parent,u=c.queue;for(let g=0;g<s.nodes.length;g++){let p=s.nodes[g];if(p.type==="comma"&&s.type==="brace"){g===1&&u.push(""),u.push("");continue}if(p.type==="close"){a.push(xe(a.pop(),u,l));continue}if(p.value&&p.type!=="open"){u.push(xe(u.pop(),p.value));continue}p.nodes&&n(p,s)}return u};return Ce.flatten(n(e))};Lr.exports=ao});var Hr=w((Za,Br)=>{"use strict";Br.exports={MAX_LENGTH:1e4,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"}});var jr=w((Ja,Ur)=>{"use strict";var lo=Ze(),{MAX_LENGTH:Mr,CHAR_BACKSLASH:wt,CHAR_BACKTICK:uo,CHAR_COMMA:co,CHAR_DOT:fo,CHAR_LEFT_PARENTHESES:po,CHAR_RIGHT_PARENTHESES:ho,CHAR_LEFT_CURLY_BRACE:go,CHAR_RIGHT_CURLY_BRACE:mo,CHAR_LEFT_SQUARE_BRACKET:$r,CHAR_RIGHT_SQUARE_BRACKET:Fr,CHAR_DOUBLE_QUOTE:_o,CHAR_SINGLE_QUOTE:yo,CHAR_NO_BREAK_SPACE:xo,CHAR_ZERO_WIDTH_NOBREAK_SPACE:bo}=Hr(),So=(e,t={})=>{if(typeof e!="string")throw new TypeError("Expected a string");let r=t||{},n=typeof r.maxLength=="number"?Math.min(Mr,r.maxLength):Mr;if(e.length>n)throw new SyntaxError(`Input length (${e.length}), exceeds max characters (${n})`);let s={type:"root",input:e,nodes:[]},o=[s],i=s,a=s,l=0,u=e.length,c=0,g=0,p,b=()=>e[c++],_=m=>{if(m.type==="text"&&a.type==="dot"&&(a.type="text"),a&&a.type==="text"&&m.type==="text"){a.value+=m.value;return}return i.nodes.push(m),m.parent=i,m.prev=a,a=m,m};for(_({type:"bos"});c<u;)if(i=o[o.length-1],p=b(),!(p===bo||p===xo)){if(p===wt){_({type:"text",value:(t.keepEscaping?p:"")+b()});continue}if(p===Fr){_({type:"text",value:"\\"+p});continue}if(p===$r){l++;let m;for(;c<u&&(m=b());){if(p+=m,m===$r){l++;continue}if(m===wt){p+=b();continue}if(m===Fr&&(l--,l===0))break}_({type:"text",value:p});continue}if(p===po){i=_({type:"paren",nodes:[]}),o.push(i),_({type:"text",value:p});continue}if(p===ho){if(i.type!=="paren"){_({type:"text",value:p});continue}i=o.pop(),_({type:"text",value:p}),i=o[o.length-1];continue}if(p===_o||p===yo||p===uo){let m=p,v;for(t.keepQuotes!==!0&&(p="");c<u&&(v=b());){if(v===wt){p+=v+b();continue}if(v===m){t.keepQuotes===!0&&(p+=v);break}p+=v}_({type:"text",value:p});continue}if(p===go){g++;let v={type:"brace",open:!0,close:!1,dollar:a.value&&a.value.slice(-1)==="$"||i.dollar===!0,depth:g,commas:0,ranges:0,nodes:[]};i=_(v),o.push(i),_({type:"open",value:p});continue}if(p===mo){if(i.type!=="brace"){_({type:"text",value:p});continue}let m="close";i=o.pop(),i.close=!0,_({type:m,value:p}),g--,i=o[o.length-1];continue}if(p===co&&g>0){if(i.ranges>0){i.ranges=0;let m=i.nodes.shift();i.nodes=[m,{type:"text",value:lo(i)}]}_({type:"comma",value:p}),i.commas++;continue}if(p===fo&&g>0&&i.commas===0){let m=i.nodes;if(g===0||m.length===0){_({type:"text",value:p});continue}if(a.type==="dot"){if(i.range=[],a.value+=p,a.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,a.type="text";continue}i.ranges++,i.args=[];continue}if(a.type==="range"){m.pop();let v=m[m.length-1];v.value+=a.value+p,a=v,i.ranges--;continue}_({type:"dot",value:p});continue}_({type:"text",value:p})}do if(i=o.pop(),i.type!=="root"){i.nodes.forEach(k=>{k.nodes||(k.type==="open"&&(k.isOpen=!0),k.type==="close"&&(k.isClose=!0),k.nodes||(k.type="text"),k.invalid=!0)});let m=o[o.length-1],v=m.nodes.indexOf(i);m.nodes.splice(v,1,...i.nodes)}while(o.length>0);return _({type:"eos"}),s};Ur.exports=So});var Gr=w((el,Wr)=>{"use strict";var qr=Ze(),vo=Nr(),Eo=Dr(),To=jr(),z=(e,t={})=>{let r=[];if(Array.isArray(e))for(let n of e){let s=z.create(n,t);Array.isArray(s)?r.push(...s):r.push(s)}else r=[].concat(z.create(e,t));return t&&t.expand===!0&&t.nodupes===!0&&(r=[...new Set(r)]),r};z.parse=(e,t={})=>To(e,t);z.stringify=(e,t={})=>qr(typeof e=="string"?z.parse(e,t):e,t);z.compile=(e,t={})=>(typeof e=="string"&&(e=z.parse(e,t)),vo(e,t));z.expand=(e,t={})=>{typeof e=="string"&&(e=z.parse(e,t));let r=Eo(e,t);return t.noempty===!0&&(r=r.filter(Boolean)),t.nodupes===!0&&(r=[...new Set(r)]),r};z.create=(e,t={})=>e===""||e.length<3?[e]:t.expand!==!0?z.compile(e,t):z.expand(e,t);Wr.exports=z});var Pe=w((tl,Qr)=>{"use strict";var Co=E("path"),oe="\\\\/",Vr=`[^${oe}]`,ue="\\.",Ro="\\+",Ao="\\?",tt="\\/",wo="(?=.)",Kr="[^/]",kt=`(?:${tt}|$)`,Xr=`(?:^|${tt})`,Ot=`${ue}{1,2}${kt}`,ko=`(?!${ue})`,Oo=`(?!${Xr}${Ot})`,Io=`(?!${ue}{0,1}${kt})`,No=`(?!${Ot})`,Po=`[^.${tt}]`,Lo=`${Kr}*?`,zr={DOT_LITERAL:ue,PLUS_LITERAL:Ro,QMARK_LITERAL:Ao,SLASH_LITERAL:tt,ONE_CHAR:wo,QMARK:Kr,END_ANCHOR:kt,DOTS_SLASH:Ot,NO_DOT:ko,NO_DOTS:Oo,NO_DOT_SLASH:Io,NO_DOTS_SLASH:No,QMARK_NO_DOT:Po,STAR:Lo,START_ANCHOR:Xr},Do={...zr,SLASH_LITERAL:`[${oe}]`,QMARK:Vr,STAR:`${Vr}*?`,DOTS_SLASH:`${ue}{1,2}(?:[${oe}]|$)`,NO_DOT:`(?!${ue})`,NO_DOTS:`(?!(?:^|[${oe}])${ue}{1,2}(?:[${oe}]|$))`,NO_DOT_SLASH:`(?!${ue}{0,1}(?:[${oe}]|$))`,NO_DOTS_SLASH:`(?!${ue}{1,2}(?:[${oe}]|$))`,QMARK_NO_DOT:`[^.${oe}]`,START_ANCHOR:`(?:^|[${oe}])`,END_ANCHOR:`(?:[${oe}]|$)`},Bo={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};Qr.exports={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:Bo,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:Co.sep,extglobChars(e){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${e.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(e){return e===!0?Do:zr}}});var Le=w(K=>{"use strict";var Ho=E("path"),Mo=process.platform==="win32",{REGEX_BACKSLASH:$o,REGEX_REMOVE_BACKSLASH:Fo,REGEX_SPECIAL_CHARS:Uo,REGEX_SPECIAL_CHARS_GLOBAL:jo}=Pe();K.isObject=e=>e!==null&&typeof e=="object"&&!Array.isArray(e);K.hasRegexChars=e=>Uo.test(e);K.isRegexChar=e=>e.length===1&&K.hasRegexChars(e);K.escapeRegex=e=>e.replace(jo,"\\$1");K.toPosixSlashes=e=>e.replace($o,"/");K.removeBackslashes=e=>e.replace(Fo,t=>t==="\\"?"":t);K.supportsLookbehinds=()=>{let e=process.version.slice(1).split(".").map(Number);return e.length===3&&e[0]>=9||e[0]===8&&e[1]>=10};K.isWindows=e=>e&&typeof e.windows=="boolean"?e.windows:Mo===!0||Ho.sep==="\\";K.escapeLast=(e,t,r)=>{let n=e.lastIndexOf(t,r);return n===-1?e:e[n-1]==="\\"?K.escapeLast(e,t,n-1):`${e.slice(0,n)}\\${e.slice(n)}`};K.removePrefix=(e,t={})=>{let r=e;return r.startsWith("./")&&(r=r.slice(2),t.prefix="./"),r};K.wrapOutput=(e,t={},r={})=>{let n=r.contains?"":"^",s=r.contains?"":"$",o=`${n}(?:${e})${s}`;return t.negated===!0&&(o=`(?:^(?!${o}).*$)`),o}});var sn=w((nl,nn)=>{"use strict";var Yr=Le(),{CHAR_ASTERISK:It,CHAR_AT:qo,CHAR_BACKWARD_SLASH:De,CHAR_COMMA:Wo,CHAR_DOT:Nt,CHAR_EXCLAMATION_MARK:Pt,CHAR_FORWARD_SLASH:rn,CHAR_LEFT_CURLY_BRACE:Lt,CHAR_LEFT_PARENTHESES:Dt,CHAR_LEFT_SQUARE_BRACKET:Go,CHAR_PLUS:Vo,CHAR_QUESTION_MARK:Zr,CHAR_RIGHT_CURLY_BRACE:Ko,CHAR_RIGHT_PARENTHESES:Jr,CHAR_RIGHT_SQUARE_BRACKET:Xo}=Pe(),en=e=>e===rn||e===De,tn=e=>{e.isPrefix!==!0&&(e.depth=e.isGlobstar?1/0:1)},zo=(e,t)=>{let r=t||{},n=e.length-1,s=r.parts===!0||r.scanToEnd===!0,o=[],i=[],a=[],l=e,u=-1,c=0,g=0,p=!1,b=!1,_=!1,m=!1,v=!1,k=!1,$=!1,O=!1,q=!1,B=!1,ee=0,F,S,R={value:"",depth:0,isGlob:!1},U=()=>u>=n,h=()=>l.charCodeAt(u+1),L=()=>(F=S,l.charCodeAt(++u));for(;u<n;){S=L();let G;if(S===De){$=R.backslashes=!0,S=L(),S===Lt&&(k=!0);continue}if(k===!0||S===Lt){for(ee++;U()!==!0&&(S=L());){if(S===De){$=R.backslashes=!0,L();continue}if(S===Lt){ee++;continue}if(k!==!0&&S===Nt&&(S=L())===Nt){if(p=R.isBrace=!0,_=R.isGlob=!0,B=!0,s===!0)continue;break}if(k!==!0&&S===Wo){if(p=R.isBrace=!0,_=R.isGlob=!0,B=!0,s===!0)continue;break}if(S===Ko&&(ee--,ee===0)){k=!1,p=R.isBrace=!0,B=!0;break}}if(s===!0)continue;break}if(S===rn){if(o.push(u),i.push(R),R={value:"",depth:0,isGlob:!1},B===!0)continue;if(F===Nt&&u===c+1){c+=2;continue}g=u+1;continue}if(r.noext!==!0&&(S===Vo||S===qo||S===It||S===Zr||S===Pt)===!0&&h()===Dt){if(_=R.isGlob=!0,m=R.isExtglob=!0,B=!0,S===Pt&&u===c&&(q=!0),s===!0){for(;U()!==!0&&(S=L());){if(S===De){$=R.backslashes=!0,S=L();continue}if(S===Jr){_=R.isGlob=!0,B=!0;break}}continue}break}if(S===It){if(F===It&&(v=R.isGlobstar=!0),_=R.isGlob=!0,B=!0,s===!0)continue;break}if(S===Zr){if(_=R.isGlob=!0,B=!0,s===!0)continue;break}if(S===Go){for(;U()!==!0&&(G=L());){if(G===De){$=R.backslashes=!0,L();continue}if(G===Xo){b=R.isBracket=!0,_=R.isGlob=!0,B=!0;break}}if(s===!0)continue;break}if(r.nonegate!==!0&&S===Pt&&u===c){O=R.negated=!0,c++;continue}if(r.noparen!==!0&&S===Dt){if(_=R.isGlob=!0,s===!0){for(;U()!==!0&&(S=L());){if(S===Dt){$=R.backslashes=!0,S=L();continue}if(S===Jr){B=!0;break}}continue}break}if(_===!0){if(B=!0,s===!0)continue;break}}r.noext===!0&&(m=!1,_=!1);let I=l,de="",f="";c>0&&(de=l.slice(0,c),l=l.slice(c),g-=c),I&&_===!0&&g>0?(I=l.slice(0,g),f=l.slice(g)):_===!0?(I="",f=l):I=l,I&&I!==""&&I!=="/"&&I!==l&&en(I.charCodeAt(I.length-1))&&(I=I.slice(0,-1)),r.unescape===!0&&(f&&(f=Yr.removeBackslashes(f)),I&&$===!0&&(I=Yr.removeBackslashes(I)));let d={prefix:de,input:e,start:c,base:I,glob:f,isBrace:p,isBracket:b,isGlob:_,isExtglob:m,isGlobstar:v,negated:O,negatedExtglob:q};if(r.tokens===!0&&(d.maxDepth=0,en(S)||i.push(R),d.tokens=i),r.parts===!0||r.tokens===!0){let G;for(let A=0;A<o.length;A++){let re=G?G+1:c,ne=o[A],X=e.slice(re,ne);r.tokens&&(A===0&&c!==0?(i[A].isPrefix=!0,i[A].value=de):i[A].value=X,tn(i[A]),d.maxDepth+=i[A].depth),(A!==0||X!=="")&&a.push(X),G=ne}if(G&&G+1<e.length){let A=e.slice(G+1);a.push(A),r.tokens&&(i[i.length-1].value=A,tn(i[i.length-1]),d.maxDepth+=i[i.length-1].depth)}d.slashes=o,d.parts=a}return d};nn.exports=zo});var ln=w((sl,an)=>{"use strict";var rt=Pe(),Q=Le(),{MAX_LENGTH:nt,POSIX_REGEX_SOURCE:Qo,REGEX_NON_SPECIAL_CHARS:Yo,REGEX_SPECIAL_CHARS_BACKREF:Zo,REPLACEMENTS:on}=rt,Jo=(e,t)=>{if(typeof t.expandRange=="function")return t.expandRange(...e,t);e.sort();let r=`[${e.join("-")}]`;try{new RegExp(r)}catch{return e.map(s=>Q.escapeRegex(s)).join("..")}return r},Re=(e,t)=>`Missing ${e}: "${t}" - use "\\\\${t}" to match literal characters`,Bt=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a string");e=on[e]||e;let r={...t},n=typeof r.maxLength=="number"?Math.min(nt,r.maxLength):nt,s=e.length;if(s>n)throw new SyntaxError(`Input length: ${s}, exceeds maximum allowed length: ${n}`);let o={type:"bos",value:"",output:r.prepend||""},i=[o],a=r.capture?"":"?:",l=Q.isWindows(t),u=rt.globChars(l),c=rt.extglobChars(u),{DOT_LITERAL:g,PLUS_LITERAL:p,SLASH_LITERAL:b,ONE_CHAR:_,DOTS_SLASH:m,NO_DOT:v,NO_DOT_SLASH:k,NO_DOTS_SLASH:$,QMARK:O,QMARK_NO_DOT:q,STAR:B,START_ANCHOR:ee}=u,F=x=>`(${a}(?:(?!${ee}${x.dot?m:g}).)*?)`,S=r.dot?"":v,R=r.dot?O:q,U=r.bash===!0?F(r):B;r.capture&&(U=`(${U})`),typeof r.noext=="boolean"&&(r.noextglob=r.noext);let h={input:e,index:-1,start:0,dot:r.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};e=Q.removePrefix(e,h),s=e.length;let L=[],I=[],de=[],f=o,d,G=()=>h.index===s-1,A=h.peek=(x=1)=>e[h.index+x],re=h.advance=()=>e[++h.index]||"",ne=()=>e.slice(h.index+1),X=(x="",N=0)=>{h.consumed+=x,h.index+=N},Ke=x=>{h.output+=x.output!=null?x.output:x.value,X(x.value)},Ds=()=>{let x=1;for(;A()==="!"&&(A(2)!=="("||A(3)==="?");)re(),h.start++,x++;return x%2===0?!1:(h.negated=!0,h.start++,!0)},Xe=x=>{h[x]++,de.push(x)},_e=x=>{h[x]--,de.pop()},C=x=>{if(f.type==="globstar"){let N=h.braces>0&&(x.type==="comma"||x.type==="brace"),y=x.extglob===!0||L.length&&(x.type==="pipe"||x.type==="paren");x.type!=="slash"&&x.type!=="paren"&&!N&&!y&&(h.output=h.output.slice(0,-f.output.length),f.type="star",f.value="*",f.output=U,h.output+=f.output)}if(L.length&&x.type!=="paren"&&(L[L.length-1].inner+=x.value),(x.value||x.output)&&Ke(x),f&&f.type==="text"&&x.type==="text"){f.output=(f.output||f.value)+x.value,f.value+=x.value;return}x.prev=f,i.push(x),f=x},ze=(x,N)=>{let y={...c[N],conditions:1,inner:""};y.prev=f,y.parens=h.parens,y.output=h.output;let T=(r.capture?"(":"")+y.open;Xe("parens"),C({type:x,value:N,output:h.output?"":_}),C({type:"paren",extglob:!0,value:re(),output:T}),L.push(y)},Bs=x=>{let N=x.close+(r.capture?")":""),y;if(x.type==="negate"){let T=U;if(x.inner&&x.inner.length>1&&x.inner.includes("/")&&(T=F(r)),(T!==U||G()||/^\)+$/.test(ne()))&&(N=x.close=`)$))${T}`),x.inner.includes("*")&&(y=ne())&&/^\.[^\\/.]+$/.test(y)){let D=Bt(y,{...t,fastpaths:!1}).output;N=x.close=`)${D})${T})`}x.prev.type==="bos"&&(h.negatedExtglob=!0)}C({type:"paren",extglob:!0,value:d,output:N}),_e("parens")};if(r.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(e)){let x=!1,N=e.replace(Zo,(y,T,D,V,j,Et)=>V==="\\"?(x=!0,y):V==="?"?T?T+V+(j?O.repeat(j.length):""):Et===0?R+(j?O.repeat(j.length):""):O.repeat(D.length):V==="."?g.repeat(D.length):V==="*"?T?T+V+(j?U:""):U:T?y:`\\${y}`);return x===!0&&(r.unescape===!0?N=N.replace(/\\/g,""):N=N.replace(/\\+/g,y=>y.length%2===0?"\\\\":y?"\\":"")),N===e&&r.contains===!0?(h.output=e,h):(h.output=Q.wrapOutput(N,h,t),h)}for(;!G();){if(d=re(),d==="\0")continue;if(d==="\\"){let y=A();if(y==="/"&&r.bash!==!0||y==="."||y===";")continue;if(!y){d+="\\",C({type:"text",value:d});continue}let T=/^\\+/.exec(ne()),D=0;if(T&&T[0].length>2&&(D=T[0].length,h.index+=D,D%2!==0&&(d+="\\")),r.unescape===!0?d=re():d+=re(),h.brackets===0){C({type:"text",value:d});continue}}if(h.brackets>0&&(d!=="]"||f.value==="["||f.value==="[^")){if(r.posix!==!1&&d===":"){let y=f.value.slice(1);if(y.includes("[")&&(f.posix=!0,y.includes(":"))){let T=f.value.lastIndexOf("["),D=f.value.slice(0,T),V=f.value.slice(T+2),j=Qo[V];if(j){f.value=D+j,h.backtrack=!0,re(),!o.output&&i.indexOf(f)===1&&(o.output=_);continue}}}(d==="["&&A()!==":"||d==="-"&&A()==="]")&&(d=`\\${d}`),d==="]"&&(f.value==="["||f.value==="[^")&&(d=`\\${d}`),r.posix===!0&&d==="!"&&f.value==="["&&(d="^"),f.value+=d,Ke({value:d});continue}if(h.quotes===1&&d!=='"'){d=Q.escapeRegex(d),f.value+=d,Ke({value:d});continue}if(d==='"'){h.quotes=h.quotes===1?0:1,r.keepQuotes===!0&&C({type:"text",value:d});continue}if(d==="("){Xe("parens"),C({type:"paren",value:d});continue}if(d===")"){if(h.parens===0&&r.strictBrackets===!0)throw new SyntaxError(Re("opening","("));let y=L[L.length-1];if(y&&h.parens===y.parens+1){Bs(L.pop());continue}C({type:"paren",value:d,output:h.parens?")":"\\)"}),_e("parens");continue}if(d==="["){if(r.nobracket===!0||!ne().includes("]")){if(r.nobracket!==!0&&r.strictBrackets===!0)throw new SyntaxError(Re("closing","]"));d=`\\${d}`}else Xe("brackets");C({type:"bracket",value:d});continue}if(d==="]"){if(r.nobracket===!0||f&&f.type==="bracket"&&f.value.length===1){C({type:"text",value:d,output:`\\${d}`});continue}if(h.brackets===0){if(r.strictBrackets===!0)throw new SyntaxError(Re("opening","["));C({type:"text",value:d,output:`\\${d}`});continue}_e("brackets");let y=f.value.slice(1);if(f.posix!==!0&&y[0]==="^"&&!y.includes("/")&&(d=`/${d}`),f.value+=d,Ke({value:d}),r.literalBrackets===!1||Q.hasRegexChars(y))continue;let T=Q.escapeRegex(f.value);if(h.output=h.output.slice(0,-f.value.length),r.literalBrackets===!0){h.output+=T,f.value=T;continue}f.value=`(${a}${T}|${f.value})`,h.output+=f.value;continue}if(d==="{"&&r.nobrace!==!0){Xe("braces");let y={type:"brace",value:d,output:"(",outputIndex:h.output.length,tokensIndex:h.tokens.length};I.push(y),C(y);continue}if(d==="}"){let y=I[I.length-1];if(r.nobrace===!0||!y){C({type:"text",value:d,output:d});continue}let T=")";if(y.dots===!0){let D=i.slice(),V=[];for(let j=D.length-1;j>=0&&(i.pop(),D[j].type!=="brace");j--)D[j].type!=="dots"&&V.unshift(D[j].value);T=Jo(V,r),h.backtrack=!0}if(y.comma!==!0&&y.dots!==!0){let D=h.output.slice(0,y.outputIndex),V=h.tokens.slice(y.tokensIndex);y.value=y.output="\\{",d=T="\\}",h.output=D;for(let j of V)h.output+=j.output||j.value}C({type:"brace",value:d,output:T}),_e("braces"),I.pop();continue}if(d==="|"){L.length>0&&L[L.length-1].conditions++,C({type:"text",value:d});continue}if(d===","){let y=d,T=I[I.length-1];T&&de[de.length-1]==="braces"&&(T.comma=!0,y="|"),C({type:"comma",value:d,output:y});continue}if(d==="/"){if(f.type==="dot"&&h.index===h.start+1){h.start=h.index+1,h.consumed="",h.output="",i.pop(),f=o;continue}C({type:"slash",value:d,output:b});continue}if(d==="."){if(h.braces>0&&f.type==="dot"){f.value==="."&&(f.output=g);let y=I[I.length-1];f.type="dots",f.output+=d,f.value+=d,y.dots=!0;continue}if(h.braces+h.parens===0&&f.type!=="bos"&&f.type!=="slash"){C({type:"text",value:d,output:g});continue}C({type:"dot",value:d,output:g});continue}if(d==="?"){if(!(f&&f.value==="(")&&r.noextglob!==!0&&A()==="("&&A(2)!=="?"){ze("qmark",d);continue}if(f&&f.type==="paren"){let T=A(),D=d;if(T==="<"&&!Q.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(f.value==="("&&!/[!=<:]/.test(T)||T==="<"&&!/<([!=]|\w+>)/.test(ne()))&&(D=`\\${d}`),C({type:"text",value:d,output:D});continue}if(r.dot!==!0&&(f.type==="slash"||f.type==="bos")){C({type:"qmark",value:d,output:q});continue}C({type:"qmark",value:d,output:O});continue}if(d==="!"){if(r.noextglob!==!0&&A()==="("&&(A(2)!=="?"||!/[!=<:]/.test(A(3)))){ze("negate",d);continue}if(r.nonegate!==!0&&h.index===0){Ds();continue}}if(d==="+"){if(r.noextglob!==!0&&A()==="("&&A(2)!=="?"){ze("plus",d);continue}if(f&&f.value==="("||r.regex===!1){C({type:"plus",value:d,output:p});continue}if(f&&(f.type==="bracket"||f.type==="paren"||f.type==="brace")||h.parens>0){C({type:"plus",value:d});continue}C({type:"plus",value:p});continue}if(d==="@"){if(r.noextglob!==!0&&A()==="("&&A(2)!=="?"){C({type:"at",extglob:!0,value:d,output:""});continue}C({type:"text",value:d});continue}if(d!=="*"){(d==="$"||d==="^")&&(d=`\\${d}`);let y=Yo.exec(ne());y&&(d+=y[0],h.index+=y[0].length),C({type:"text",value:d});continue}if(f&&(f.type==="globstar"||f.star===!0)){f.type="star",f.star=!0,f.value+=d,f.output=U,h.backtrack=!0,h.globstar=!0,X(d);continue}let x=ne();if(r.noextglob!==!0&&/^\([^?]/.test(x)){ze("star",d);continue}if(f.type==="star"){if(r.noglobstar===!0){X(d);continue}let y=f.prev,T=y.prev,D=y.type==="slash"||y.type==="bos",V=T&&(T.type==="star"||T.type==="globstar");if(r.bash===!0&&(!D||x[0]&&x[0]!=="/")){C({type:"star",value:d,output:""});continue}let j=h.braces>0&&(y.type==="comma"||y.type==="brace"),Et=L.length&&(y.type==="pipe"||y.type==="paren");if(!D&&y.type!=="paren"&&!j&&!Et){C({type:"star",value:d,output:""});continue}for(;x.slice(0,3)==="/**";){let Qe=e[h.index+4];if(Qe&&Qe!=="/")break;x=x.slice(3),X("/**",3)}if(y.type==="bos"&&G()){f.type="globstar",f.value+=d,f.output=F(r),h.output=f.output,h.globstar=!0,X(d);continue}if(y.type==="slash"&&y.prev.type!=="bos"&&!V&&G()){h.output=h.output.slice(0,-(y.output+f.output).length),y.output=`(?:${y.output}`,f.type="globstar",f.output=F(r)+(r.strictSlashes?")":"|$)"),f.value+=d,h.globstar=!0,h.output+=y.output+f.output,X(d);continue}if(y.type==="slash"&&y.prev.type!=="bos"&&x[0]==="/"){let Qe=x[1]!==void 0?"|$":"";h.output=h.output.slice(0,-(y.output+f.output).length),y.output=`(?:${y.output}`,f.type="globstar",f.output=`${F(r)}${b}|${b}${Qe})`,f.value+=d,h.output+=y.output+f.output,h.globstar=!0,X(d+re()),C({type:"slash",value:"/",output:""});continue}if(y.type==="bos"&&x[0]==="/"){f.type="globstar",f.value+=d,f.output=`(?:^|${b}|${F(r)}${b})`,h.output=f.output,h.globstar=!0,X(d+re()),C({type:"slash",value:"/",output:""});continue}h.output=h.output.slice(0,-f.output.length),f.type="globstar",f.output=F(r),f.value+=d,h.output+=f.output,h.globstar=!0,X(d);continue}let N={type:"star",value:d,output:U};if(r.bash===!0){N.output=".*?",(f.type==="bos"||f.type==="slash")&&(N.output=S+N.output),C(N);continue}if(f&&(f.type==="bracket"||f.type==="paren")&&r.regex===!0){N.output=d,C(N);continue}(h.index===h.start||f.type==="slash"||f.type==="dot")&&(f.type==="dot"?(h.output+=k,f.output+=k):r.dot===!0?(h.output+=$,f.output+=$):(h.output+=S,f.output+=S),A()!=="*"&&(h.output+=_,f.output+=_)),C(N)}for(;h.brackets>0;){if(r.strictBrackets===!0)throw new SyntaxError(Re("closing","]"));h.output=Q.escapeLast(h.output,"["),_e("brackets")}for(;h.parens>0;){if(r.strictBrackets===!0)throw new SyntaxError(Re("closing",")"));h.output=Q.escapeLast(h.output,"("),_e("parens")}for(;h.braces>0;){if(r.strictBrackets===!0)throw new SyntaxError(Re("closing","}"));h.output=Q.escapeLast(h.output,"{"),_e("braces")}if(r.strictSlashes!==!0&&(f.type==="star"||f.type==="bracket")&&C({type:"maybe_slash",value:"",output:`${b}?`}),h.backtrack===!0){h.output="";for(let x of h.tokens)h.output+=x.output!=null?x.output:x.value,x.suffix&&(h.output+=x.suffix)}return h};Bt.fastpaths=(e,t)=>{let r={...t},n=typeof r.maxLength=="number"?Math.min(nt,r.maxLength):nt,s=e.length;if(s>n)throw new SyntaxError(`Input length: ${s}, exceeds maximum allowed length: ${n}`);e=on[e]||e;let o=Q.isWindows(t),{DOT_LITERAL:i,SLASH_LITERAL:a,ONE_CHAR:l,DOTS_SLASH:u,NO_DOT:c,NO_DOTS:g,NO_DOTS_SLASH:p,STAR:b,START_ANCHOR:_}=rt.globChars(o),m=r.dot?g:c,v=r.dot?p:c,k=r.capture?"":"?:",$={negated:!1,prefix:""},O=r.bash===!0?".*?":b;r.capture&&(O=`(${O})`);let q=S=>S.noglobstar===!0?O:`(${k}(?:(?!${_}${S.dot?u:i}).)*?)`,B=S=>{switch(S){case"*":return`${m}${l}${O}`;case".*":return`${i}${l}${O}`;case"*.*":return`${m}${O}${i}${l}${O}`;case"*/*":return`${m}${O}${a}${l}${v}${O}`;case"**":return m+q(r);case"**/*":return`(?:${m}${q(r)}${a})?${v}${l}${O}`;case"**/*.*":return`(?:${m}${q(r)}${a})?${v}${O}${i}${l}${O}`;case"**/.*":return`(?:${m}${q(r)}${a})?${i}${l}${O}`;default:{let R=/^(.*?)\.(\w+)$/.exec(S);if(!R)return;let U=B(R[1]);return U?U+i+R[2]:void 0}}},ee=Q.removePrefix(e,$),F=B(ee);return F&&r.strictSlashes!==!0&&(F+=`${a}?`),F};an.exports=Bt});var cn=w((ol,un)=>{"use strict";var ei=E("path"),ti=sn(),Ht=ln(),Mt=Le(),ri=Pe(),ni=e=>e&&typeof e=="object"&&!Array.isArray(e),H=(e,t,r=!1)=>{if(Array.isArray(e)){let c=e.map(p=>H(p,t,r));return p=>{for(let b of c){let _=b(p);if(_)return _}return!1}}let n=ni(e)&&e.tokens&&e.input;if(e===""||typeof e!="string"&&!n)throw new TypeError("Expected pattern to be a non-empty string");let s=t||{},o=Mt.isWindows(t),i=n?H.compileRe(e,t):H.makeRe(e,t,!1,!0),a=i.state;delete i.state;let l=()=>!1;if(s.ignore){let c={...t,ignore:null,onMatch:null,onResult:null};l=H(s.ignore,c,r)}let u=(c,g=!1)=>{let{isMatch:p,match:b,output:_}=H.test(c,i,t,{glob:e,posix:o}),m={glob:e,state:a,regex:i,posix:o,input:c,output:_,match:b,isMatch:p};return typeof s.onResult=="function"&&s.onResult(m),p===!1?(m.isMatch=!1,g?m:!1):l(c)?(typeof s.onIgnore=="function"&&s.onIgnore(m),m.isMatch=!1,g?m:!1):(typeof s.onMatch=="function"&&s.onMatch(m),g?m:!0)};return r&&(u.state=a),u};H.test=(e,t,r,{glob:n,posix:s}={})=>{if(typeof e!="string")throw new TypeError("Expected input to be a string");if(e==="")return{isMatch:!1,output:""};let o=r||{},i=o.format||(s?Mt.toPosixSlashes:null),a=e===n,l=a&&i?i(e):e;return a===!1&&(l=i?i(e):e,a=l===n),(a===!1||o.capture===!0)&&(o.matchBase===!0||o.basename===!0?a=H.matchBase(e,t,r,s):a=t.exec(l)),{isMatch:!!a,match:a,output:l}};H.matchBase=(e,t,r,n=Mt.isWindows(r))=>(t instanceof RegExp?t:H.makeRe(t,r)).test(ei.basename(e));H.isMatch=(e,t,r)=>H(t,r)(e);H.parse=(e,t)=>Array.isArray(e)?e.map(r=>H.parse(r,t)):Ht(e,{...t,fastpaths:!1});H.scan=(e,t)=>ti(e,t);H.compileRe=(e,t,r=!1,n=!1)=>{if(r===!0)return e.output;let s=t||{},o=s.contains?"":"^",i=s.contains?"":"$",a=`${o}(?:${e.output})${i}`;e&&e.negated===!0&&(a=`^(?!${a}).*$`);let l=H.toRegex(a,t);return n===!0&&(l.state=e),l};H.makeRe=(e,t={},r=!1,n=!1)=>{if(!e||typeof e!="string")throw new TypeError("Expected a non-empty string");let s={negated:!1,fastpaths:!0};return t.fastpaths!==!1&&(e[0]==="."||e[0]==="*")&&(s.output=Ht.fastpaths(e,t)),s.output||(s=Ht(e,t)),H.compileRe(s,t,r,n)};H.toRegex=(e,t)=>{try{let r=t||{};return new RegExp(e,r.flags||(r.nocase?"i":""))}catch(r){if(t&&t.debug===!0)throw r;return/$^/}};H.constants=ri;un.exports=H});var pn=w((il,fn)=>{"use strict";fn.exports=cn()});var yn=w((al,_n)=>{"use strict";var hn=E("util"),gn=Gr(),ie=pn(),$t=Le(),dn=e=>e===""||e==="./",mn=e=>{let t=e.indexOf("{");return t>-1&&e.indexOf("}",t)>-1},P=(e,t,r)=>{t=[].concat(t),e=[].concat(e);let n=new Set,s=new Set,o=new Set,i=0,a=c=>{o.add(c.output),r&&r.onResult&&r.onResult(c)};for(let c=0;c<t.length;c++){let g=ie(String(t[c]),{...r,onResult:a},!0),p=g.state.negated||g.state.negatedExtglob;p&&i++;for(let b of e){let _=g(b,!0);(p?!_.isMatch:_.isMatch)&&(p?n.add(_.output):(n.delete(_.output),s.add(_.output)))}}let u=(i===t.length?[...o]:[...s]).filter(c=>!n.has(c));if(r&&u.length===0){if(r.failglob===!0)throw new Error(`No matches found for "${t.join(", ")}"`);if(r.nonull===!0||r.nullglob===!0)return r.unescape?t.map(c=>c.replace(/\\/g,"")):t}return u};P.match=P;P.matcher=(e,t)=>ie(e,t);P.isMatch=(e,t,r)=>ie(t,r)(e);P.any=P.isMatch;P.not=(e,t,r={})=>{t=[].concat(t).map(String);let n=new Set,s=[],o=a=>{r.onResult&&r.onResult(a),s.push(a.output)},i=new Set(P(e,t,{...r,onResult:o}));for(let a of s)i.has(a)||n.add(a);return[...n]};P.contains=(e,t,r)=>{if(typeof e!="string")throw new TypeError(`Expected a string: "${hn.inspect(e)}"`);if(Array.isArray(t))return t.some(n=>P.contains(e,n,r));if(typeof t=="string"){if(dn(e)||dn(t))return!1;if(e.includes(t)||e.startsWith("./")&&e.slice(2).includes(t))return!0}return P.isMatch(e,t,{...r,contains:!0})};P.matchKeys=(e,t,r)=>{if(!$t.isObject(e))throw new TypeError("Expected the first argument to be an object");let n=P(Object.keys(e),t,r),s={};for(let o of n)s[o]=e[o];return s};P.some=(e,t,r)=>{let n=[].concat(e);for(let s of[].concat(t)){let o=ie(String(s),r);if(n.some(i=>o(i)))return!0}return!1};P.every=(e,t,r)=>{let n=[].concat(e);for(let s of[].concat(t)){let o=ie(String(s),r);if(!n.every(i=>o(i)))return!1}return!0};P.all=(e,t,r)=>{if(typeof e!="string")throw new TypeError(`Expected a string: "${hn.inspect(e)}"`);return[].concat(t).every(n=>ie(n,r)(e))};P.capture=(e,t,r)=>{let n=$t.isWindows(r),o=ie.makeRe(String(e),{...r,capture:!0}).exec(n?$t.toPosixSlashes(t):t);if(o)return o.slice(1).map(i=>i===void 0?"":i)};P.makeRe=(...e)=>ie.makeRe(...e);P.scan=(...e)=>ie.scan(...e);P.parse=(e,t)=>{let r=[];for(let n of[].concat(e||[]))for(let s of gn(String(n),t))r.push(ie.parse(s,t));return r};P.braces=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a string");return t&&t.nobrace===!0||!mn(e)?[e]:gn(e,t)};P.braceExpand=(e,t)=>{if(typeof e!="string")throw new TypeError("Expected a string");return P.braces(e,{...t,expand:!0})};P.hasBraces=mn;_n.exports=P});var vn=w((fl,Sn)=>{"use strict";var{Duplex:ii}=E("stream");function xn(e){e.emit("close")}function ai(){!this.destroyed&&this._writableState.finished&&this.destroy()}function bn(e){this.removeListener("error",bn),this.destroy(),this.listenerCount("error")===0&&this.emit("error",e)}function li(e,t){let r=!0,n=new ii({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(o,i){let a=!i&&n._readableState.objectMode?o.toString():o;n.push(a)||e.pause()}),e.once("error",function(o){n.destroyed||(r=!1,n.destroy(o))}),e.once("close",function(){n.destroyed||n.push(null)}),n._destroy=function(s,o){if(e.readyState===e.CLOSED){o(s),process.nextTick(xn,n);return}let i=!1;e.once("error",function(l){i=!0,o(l)}),e.once("close",function(){i||o(s),process.nextTick(xn,n)}),r&&e.terminate()},n._final=function(s){if(e.readyState===e.CONNECTING){e.once("open",function(){n._final(s)});return}e._socket!==null&&(e._socket._writableState.finished?(s(),n._readableState.endEmitted&&n.destroy()):(e._socket.once("finish",function(){s()}),e.close()))},n._read=function(){e.isPaused&&e.resume()},n._write=function(s,o,i){if(e.readyState===e.CONNECTING){e.once("open",function(){n._write(s,o,i)});return}e.send(s,i)},n.on("end",ai),n.on("error",bn),n}Sn.exports=li});var he=w((pl,En)=>{"use strict";En.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var Be=w((dl,st)=>{"use strict";var{EMPTY_BUFFER:ui}=he(),Ft=Buffer[Symbol.species];function ci(e,t){if(e.length===0)return ui;if(e.length===1)return e[0];let r=Buffer.allocUnsafe(t),n=0;for(let s=0;s<e.length;s++){let o=e[s];r.set(o,n),n+=o.length}return n<t?new Ft(r.buffer,r.byteOffset,n):r}function Tn(e,t,r,n,s){for(let o=0;o<s;o++)r[n+o]=e[o]^t[o&3]}function Cn(e,t){for(let r=0;r<e.length;r++)e[r]^=t[r&3]}function fi(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)}function Ut(e){if(Ut.readOnly=!0,Buffer.isBuffer(e))return e;let t;return e instanceof ArrayBuffer?t=new Ft(e):ArrayBuffer.isView(e)?t=new Ft(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),Ut.readOnly=!1),t}st.exports={concat:ci,mask:Tn,toArrayBuffer:fi,toBuffer:Ut,unmask:Cn};if(!process.env.WS_NO_BUFFER_UTIL)try{let e=E("bufferutil");st.exports.mask=function(t,r,n,s,o){o<48?Tn(t,r,n,s,o):e.mask(t,r,n,s,o)},st.exports.unmask=function(t,r){t.length<32?Cn(t,r):e.unmask(t,r)}}catch{}});var wn=w((hl,An)=>{"use strict";var Rn=Symbol("kDone"),jt=Symbol("kRun"),qt=class{constructor(t){this[Rn]=()=>{this.pending--,this[jt]()},this.concurrency=t||1/0,this.jobs=[],this.pending=0}add(t){this.jobs.push(t),this[jt]()}[jt](){if(this.pending!==this.concurrency&&this.jobs.length){let t=this.jobs.shift();this.pending++,t(this[Rn])}}};An.exports=qt});var $e=w((gl,Nn)=>{"use strict";var He=E("zlib"),kn=Be(),pi=wn(),{kStatusCode:On}=he(),di=Buffer[Symbol.species],hi=Buffer.from([0,0,255,255]),at=Symbol("permessage-deflate"),ce=Symbol("total-length"),Me=Symbol("callback"),ge=Symbol("buffers"),it=Symbol("error"),ot,Wt=class{constructor(t,r,n){if(this._maxPayload=n|0,this._options=t||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!r,this._deflate=null,this._inflate=null,this.params=null,!ot){let s=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;ot=new pi(s)}}static get extensionName(){return"permessage-deflate"}offer(){let t={};return this._options.serverNoContextTakeover&&(t.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(t.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(t.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?t.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(t.client_max_window_bits=!0),t}accept(t){return t=this.normalizeParams(t),this.params=this._isServer?this.acceptAsServer(t):this.acceptAsClient(t),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let t=this._deflate[Me];this._deflate.close(),this._deflate=null,t&&t(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(t){let r=this._options,n=t.find(s=>!(r.serverNoContextTakeover===!1&&s.server_no_context_takeover||s.server_max_window_bits&&(r.serverMaxWindowBits===!1||typeof r.serverMaxWindowBits=="number"&&r.serverMaxWindowBits>s.server_max_window_bits)||typeof r.clientMaxWindowBits=="number"&&!s.client_max_window_bits));if(!n)throw new Error("None of the extension offers can be accepted");return r.serverNoContextTakeover&&(n.server_no_context_takeover=!0),r.clientNoContextTakeover&&(n.client_no_context_takeover=!0),typeof r.serverMaxWindowBits=="number"&&(n.server_max_window_bits=r.serverMaxWindowBits),typeof r.clientMaxWindowBits=="number"?n.client_max_window_bits=r.clientMaxWindowBits:(n.client_max_window_bits===!0||r.clientMaxWindowBits===!1)&&delete n.client_max_window_bits,n}acceptAsClient(t){let r=t[0];if(this._options.clientNoContextTakeover===!1&&r.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!r.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(r.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&r.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return r}normalizeParams(t){return t.forEach(r=>{Object.keys(r).forEach(n=>{let s=r[n];if(s.length>1)throw new Error(`Parameter "${n}" must have only a single value`);if(s=s[0],n==="client_max_window_bits"){if(s!==!0){let o=+s;if(!Number.isInteger(o)||o<8||o>15)throw new TypeError(`Invalid value for parameter "${n}": ${s}`);s=o}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${n}": ${s}`)}else if(n==="server_max_window_bits"){let o=+s;if(!Number.isInteger(o)||o<8||o>15)throw new TypeError(`Invalid value for parameter "${n}": ${s}`);s=o}else if(n==="client_no_context_takeover"||n==="server_no_context_takeover"){if(s!==!0)throw new TypeError(`Invalid value for parameter "${n}": ${s}`)}else throw new Error(`Unknown parameter "${n}"`);r[n]=s})}),t}decompress(t,r,n){ot.add(s=>{this._decompress(t,r,(o,i)=>{s(),n(o,i)})})}compress(t,r,n){ot.add(s=>{this._compress(t,r,(o,i)=>{s(),n(o,i)})})}_decompress(t,r,n){let s=this._isServer?"client":"server";if(!this._inflate){let o=`${s}_max_window_bits`,i=typeof this.params[o]!="number"?He.Z_DEFAULT_WINDOWBITS:this.params[o];this._inflate=He.createInflateRaw({...this._options.zlibInflateOptions,windowBits:i}),this._inflate[at]=this,this._inflate[ce]=0,this._inflate[ge]=[],this._inflate.on("error",mi),this._inflate.on("data",In)}this._inflate[Me]=n,this._inflate.write(t),r&&this._inflate.write(hi),this._inflate.flush(()=>{let o=this._inflate[it];if(o){this._inflate.close(),this._inflate=null,n(o);return}let i=kn.concat(this._inflate[ge],this._inflate[ce]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[ce]=0,this._inflate[ge]=[],r&&this.params[`${s}_no_context_takeover`]&&this._inflate.reset()),n(null,i)})}_compress(t,r,n){let s=this._isServer?"server":"client";if(!this._deflate){let o=`${s}_max_window_bits`,i=typeof this.params[o]!="number"?He.Z_DEFAULT_WINDOWBITS:this.params[o];this._deflate=He.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:i}),this._deflate[ce]=0,this._deflate[ge]=[],this._deflate.on("data",gi)}this._deflate[Me]=n,this._deflate.write(t),this._deflate.flush(He.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let o=kn.concat(this._deflate[ge],this._deflate[ce]);r&&(o=new di(o.buffer,o.byteOffset,o.length-4)),this._deflate[Me]=null,this._deflate[ce]=0,this._deflate[ge]=[],r&&this.params[`${s}_no_context_takeover`]&&this._deflate.reset(),n(null,o)})}};Nn.exports=Wt;function gi(e){this[ge].push(e),this[ce]+=e.length}function In(e){if(this[ce]+=e.length,this[at]._maxPayload<1||this[ce]<=this[at]._maxPayload){this[ge].push(e);return}this[it]=new RangeError("Max payload size exceeded"),this[it].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[it][On]=1009,this.removeListener("data",In),this.reset()}function mi(e){this[at]._inflate=null,e[On]=1007,this[Me](e)}});var Fe=w((ml,lt)=>{"use strict";var{isUtf8:Pn}=E("buffer"),_i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function yi(e){return e>=1e3&&e<=1014&&e!==1004&&e!==1005&&e!==1006||e>=3e3&&e<=4999}function Gt(e){let t=e.length,r=0;for(;r<t;)if((e[r]&128)===0)r++;else if((e[r]&224)===192){if(r+1===t||(e[r+1]&192)!==128||(e[r]&254)===192)return!1;r+=2}else if((e[r]&240)===224){if(r+2>=t||(e[r+1]&192)!==128||(e[r+2]&192)!==128||e[r]===224&&(e[r+1]&224)===128||e[r]===237&&(e[r+1]&224)===160)return!1;r+=3}else if((e[r]&248)===240){if(r+3>=t||(e[r+1]&192)!==128||(e[r+2]&192)!==128||(e[r+3]&192)!==128||e[r]===240&&(e[r+1]&240)===128||e[r]===244&&e[r+1]>143||e[r]>244)return!1;r+=4}else return!1;return!0}lt.exports={isValidStatusCode:yi,isValidUTF8:Gt,tokenChars:_i};if(Pn)lt.exports.isValidUTF8=function(e){return e.length<24?Gt(e):Pn(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let e=E("utf-8-validate");lt.exports.isValidUTF8=function(t){return t.length<32?Gt(t):e(t)}}catch{}});var Qt=w((_l,Fn)=>{"use strict";var{Writable:xi}=E("stream"),Ln=$e(),{BINARY_TYPES:bi,EMPTY_BUFFER:Dn,kStatusCode:Si,kWebSocket:vi}=he(),{concat:Vt,toArrayBuffer:Ei,unmask:Ti}=Be(),{isValidStatusCode:Ci,isValidUTF8:Bn}=Fe(),ut=Buffer[Symbol.species],J=0,Hn=1,Mn=2,$n=3,Kt=4,Xt=5,ct=6,zt=class extends xi{constructor(t={}){super(),this._allowSynchronousEvents=t.allowSynchronousEvents!==void 0?t.allowSynchronousEvents:!0,this._binaryType=t.binaryType||bi[0],this._extensions=t.extensions||{},this._isServer=!!t.isServer,this._maxPayload=t.maxPayload|0,this._skipUTF8Validation=!!t.skipUTF8Validation,this[vi]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=J}_write(t,r,n){if(this._opcode===8&&this._state==J)return n();this._bufferedBytes+=t.length,this._buffers.push(t),this.startLoop(n)}consume(t){if(this._bufferedBytes-=t,t===this._buffers[0].length)return this._buffers.shift();if(t<this._buffers[0].length){let n=this._buffers[0];return this._buffers[0]=new ut(n.buffer,n.byteOffset+t,n.length-t),new ut(n.buffer,n.byteOffset,t)}let r=Buffer.allocUnsafe(t);do{let n=this._buffers[0],s=r.length-t;t>=n.length?r.set(this._buffers.shift(),s):(r.set(new Uint8Array(n.buffer,n.byteOffset,t),s),this._buffers[0]=new ut(n.buffer,n.byteOffset+t,n.length-t)),t-=n.length}while(t>0);return r}startLoop(t){this._loop=!0;do switch(this._state){case J:this.getInfo(t);break;case Hn:this.getPayloadLength16(t);break;case Mn:this.getPayloadLength64(t);break;case $n:this.getMask();break;case Kt:this.getData(t);break;case Xt:case ct:this._loop=!1;return}while(this._loop);this._errored||t()}getInfo(t){if(this._bufferedBytes<2){this._loop=!1;return}let r=this.consume(2);if((r[0]&48)!==0){let s=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");t(s);return}let n=(r[0]&64)===64;if(n&&!this._extensions[Ln.extensionName]){let s=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");t(s);return}if(this._fin=(r[0]&128)===128,this._opcode=r[0]&15,this._payloadLength=r[1]&127,this._opcode===0){if(n){let s=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");t(s);return}if(!this._fragmented){let s=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");t(s);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let s=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");t(s);return}this._compressed=n}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let s=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");t(s);return}if(n){let s=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");t(s);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let s=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");t(s);return}}else{let s=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");t(s);return}if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(r[1]&128)===128,this._isServer){if(!this._masked){let s=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");t(s);return}}else if(this._masked){let s=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");t(s);return}this._payloadLength===126?this._state=Hn:this._payloadLength===127?this._state=Mn:this.haveLength(t)}getPayloadLength16(t){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(t)}getPayloadLength64(t){if(this._bufferedBytes<8){this._loop=!1;return}let r=this.consume(8),n=r.readUInt32BE(0);if(n>Math.pow(2,21)-1){let s=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");t(s);return}this._payloadLength=n*Math.pow(2,32)+r.readUInt32BE(4),this.haveLength(t)}haveLength(t){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){let r=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");t(r);return}this._masked?this._state=$n:this._state=Kt}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Kt}getData(t){let r=Dn;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}r=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0&&Ti(r,this._mask)}if(this._opcode>7){this.controlMessage(r,t);return}if(this._compressed){this._state=Xt,this.decompress(r,t);return}r.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(r)),this.dataMessage(t)}decompress(t,r){this._extensions[Ln.extensionName].decompress(t,this._fin,(s,o)=>{if(s)return r(s);if(o.length){if(this._messageLength+=o.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let i=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");r(i);return}this._fragments.push(o)}this.dataMessage(r),this._state===J&&this.startLoop(r)})}dataMessage(t){if(!this._fin){this._state=J;return}let r=this._messageLength,n=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let s;this._binaryType==="nodebuffer"?s=Vt(n,r):this._binaryType==="arraybuffer"?s=Ei(Vt(n,r)):s=n,this._allowSynchronousEvents?(this.emit("message",s,!0),this._state=J):(this._state=ct,setImmediate(()=>{this.emit("message",s,!0),this._state=J,this.startLoop(t)}))}else{let s=Vt(n,r);if(!this._skipUTF8Validation&&!Bn(s)){let o=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");t(o);return}this._state===Xt||this._allowSynchronousEvents?(this.emit("message",s,!1),this._state=J):(this._state=ct,setImmediate(()=>{this.emit("message",s,!1),this._state=J,this.startLoop(t)}))}}controlMessage(t,r){if(this._opcode===8){if(t.length===0)this._loop=!1,this.emit("conclude",1005,Dn),this.end();else{let n=t.readUInt16BE(0);if(!Ci(n)){let o=this.createError(RangeError,`invalid status code ${n}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");r(o);return}let s=new ut(t.buffer,t.byteOffset+2,t.length-2);if(!this._skipUTF8Validation&&!Bn(s)){let o=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");r(o);return}this._loop=!1,this.emit("conclude",n,s),this.end()}this._state=J;return}this._allowSynchronousEvents?(this.emit(this._opcode===9?"ping":"pong",t),this._state=J):(this._state=ct,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",t),this._state=J,this.startLoop(r)}))}createError(t,r,n,s,o){this._loop=!1,this._errored=!0;let i=new t(n?`Invalid WebSocket frame: ${r}`:r);return Error.captureStackTrace(i,this.createError),i.code=o,i[Si]=s,i}};Fn.exports=zt});var Zt=w((xl,qn)=>{"use strict";var{Duplex:yl}=E("stream"),{randomFillSync:Ri}=E("crypto"),Un=$e(),{EMPTY_BUFFER:Ai}=he(),{isValidStatusCode:wi}=Fe(),{mask:jn,toBuffer:Ae}=Be(),te=Symbol("kByteLength"),ki=Buffer.alloc(4),ft=8*1024,be,we=ft,Yt=class e{constructor(t,r,n){this._extensions=r||{},n&&(this._generateMask=n,this._maskBuffer=Buffer.alloc(4)),this._socket=t,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(t,r){let n,s=!1,o=2,i=!1;r.mask&&(n=r.maskBuffer||ki,r.generateMask?r.generateMask(n):(we===ft&&(be===void 0&&(be=Buffer.alloc(ft)),Ri(be,0,ft),we=0),n[0]=be[we++],n[1]=be[we++],n[2]=be[we++],n[3]=be[we++]),i=(n[0]|n[1]|n[2]|n[3])===0,o=6);let a;typeof t=="string"?(!r.mask||i)&&r[te]!==void 0?a=r[te]:(t=Buffer.from(t),a=t.length):(a=t.length,s=r.mask&&r.readOnly&&!i);let l=a;a>=65536?(o+=8,l=127):a>125&&(o+=2,l=126);let u=Buffer.allocUnsafe(s?a+o:o);return u[0]=r.fin?r.opcode|128:r.opcode,r.rsv1&&(u[0]|=64),u[1]=l,l===126?u.writeUInt16BE(a,2):l===127&&(u[2]=u[3]=0,u.writeUIntBE(a,4,6)),r.mask?(u[1]|=128,u[o-4]=n[0],u[o-3]=n[1],u[o-2]=n[2],u[o-1]=n[3],i?[u,t]:s?(jn(t,n,u,o,a),[u]):(jn(t,n,t,0,a),[u,t])):[u,t]}close(t,r,n,s){let o;if(t===void 0)o=Ai;else{if(typeof t!="number"||!wi(t))throw new TypeError("First argument must be a valid error code number");if(r===void 0||!r.length)o=Buffer.allocUnsafe(2),o.writeUInt16BE(t,0);else{let a=Buffer.byteLength(r);if(a>123)throw new RangeError("The message must not be greater than 123 bytes");o=Buffer.allocUnsafe(2+a),o.writeUInt16BE(t,0),typeof r=="string"?o.write(r,2):o.set(r,2)}}let i={[te]:o.length,fin:!0,generateMask:this._generateMask,mask:n,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,o,!1,i,s]):this.sendFrame(e.frame(o,i),s)}ping(t,r,n){let s,o;if(typeof t=="string"?(s=Buffer.byteLength(t),o=!1):(t=Ae(t),s=t.length,o=Ae.readOnly),s>125)throw new RangeError("The data size must not be greater than 125 bytes");let i={[te]:s,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:9,readOnly:o,rsv1:!1};this._deflating?this.enqueue([this.dispatch,t,!1,i,n]):this.sendFrame(e.frame(t,i),n)}pong(t,r,n){let s,o;if(typeof t=="string"?(s=Buffer.byteLength(t),o=!1):(t=Ae(t),s=t.length,o=Ae.readOnly),s>125)throw new RangeError("The data size must not be greater than 125 bytes");let i={[te]:s,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:10,readOnly:o,rsv1:!1};this._deflating?this.enqueue([this.dispatch,t,!1,i,n]):this.sendFrame(e.frame(t,i),n)}send(t,r,n){let s=this._extensions[Un.extensionName],o=r.binary?2:1,i=r.compress,a,l;if(typeof t=="string"?(a=Buffer.byteLength(t),l=!1):(t=Ae(t),a=t.length,l=Ae.readOnly),this._firstFragment?(this._firstFragment=!1,i&&s&&s.params[s._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(i=a>=s._threshold),this._compress=i):(i=!1,o=0),r.fin&&(this._firstFragment=!0),s){let u={[te]:a,fin:r.fin,generateMask:this._generateMask,mask:r.mask,maskBuffer:this._maskBuffer,opcode:o,readOnly:l,rsv1:i};this._deflating?this.enqueue([this.dispatch,t,this._compress,u,n]):this.dispatch(t,this._compress,u,n)}else this.sendFrame(e.frame(t,{[te]:a,fin:r.fin,generateMask:this._generateMask,mask:r.mask,maskBuffer:this._maskBuffer,opcode:o,readOnly:l,rsv1:!1}),n)}dispatch(t,r,n,s){if(!r){this.sendFrame(e.frame(t,n),s);return}let o=this._extensions[Un.extensionName];this._bufferedBytes+=n[te],this._deflating=!0,o.compress(t,n.fin,(i,a)=>{if(this._socket.destroyed){let l=new Error("The socket was closed while data was being compressed");typeof s=="function"&&s(l);for(let u=0;u<this._queue.length;u++){let c=this._queue[u],g=c[c.length-1];typeof g=="function"&&g(l)}return}this._bufferedBytes-=n[te],this._deflating=!1,n.readOnly=!1,this.sendFrame(e.frame(a,n),s),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let t=this._queue.shift();this._bufferedBytes-=t[3][te],Reflect.apply(t[0],this,t.slice(1))}}enqueue(t){this._bufferedBytes+=t[3][te],this._queue.push(t)}sendFrame(t,r){t.length===2?(this._socket.cork(),this._socket.write(t[0]),this._socket.write(t[1],r),this._socket.uncork()):this._socket.write(t[0],r)}};qn.exports=Yt});var Zn=w((bl,Yn)=>{"use strict";var{kForOnEventAttribute:Ue,kListener:Jt}=he(),Wn=Symbol("kCode"),Gn=Symbol("kData"),Vn=Symbol("kError"),Kn=Symbol("kMessage"),Xn=Symbol("kReason"),ke=Symbol("kTarget"),zn=Symbol("kType"),Qn=Symbol("kWasClean"),fe=class{constructor(t){this[ke]=null,this[zn]=t}get target(){return this[ke]}get type(){return this[zn]}};Object.defineProperty(fe.prototype,"target",{enumerable:!0});Object.defineProperty(fe.prototype,"type",{enumerable:!0});var Se=class extends fe{constructor(t,r={}){super(t),this[Wn]=r.code===void 0?0:r.code,this[Xn]=r.reason===void 0?"":r.reason,this[Qn]=r.wasClean===void 0?!1:r.wasClean}get code(){return this[Wn]}get reason(){return this[Xn]}get wasClean(){return this[Qn]}};Object.defineProperty(Se.prototype,"code",{enumerable:!0});Object.defineProperty(Se.prototype,"reason",{enumerable:!0});Object.defineProperty(Se.prototype,"wasClean",{enumerable:!0});var Oe=class extends fe{constructor(t,r={}){super(t),this[Vn]=r.error===void 0?null:r.error,this[Kn]=r.message===void 0?"":r.message}get error(){return this[Vn]}get message(){return this[Kn]}};Object.defineProperty(Oe.prototype,"error",{enumerable:!0});Object.defineProperty(Oe.prototype,"message",{enumerable:!0});var je=class extends fe{constructor(t,r={}){super(t),this[Gn]=r.data===void 0?null:r.data}get data(){return this[Gn]}};Object.defineProperty(je.prototype,"data",{enumerable:!0});var Oi={addEventListener(e,t,r={}){for(let s of this.listeners(e))if(!r[Ue]&&s[Jt]===t&&!s[Ue])return;let n;if(e==="message")n=function(o,i){let a=new je("message",{data:i?o:o.toString()});a[ke]=this,pt(t,this,a)};else if(e==="close")n=function(o,i){let a=new Se("close",{code:o,reason:i.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});a[ke]=this,pt(t,this,a)};else if(e==="error")n=function(o){let i=new Oe("error",{error:o,message:o.message});i[ke]=this,pt(t,this,i)};else if(e==="open")n=function(){let o=new fe("open");o[ke]=this,pt(t,this,o)};else return;n[Ue]=!!r[Ue],n[Jt]=t,r.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let r of this.listeners(e))if(r[Jt]===t&&!r[Ue]){this.removeListener(e,r);break}}};Yn.exports={CloseEvent:Se,ErrorEvent:Oe,Event:fe,EventTarget:Oi,MessageEvent:je};function pt(e,t,r){typeof e=="object"&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}});var er=w((Sl,Jn)=>{"use strict";var{tokenChars:qe}=Fe();function ae(e,t,r){e[t]===void 0?e[t]=[r]:e[t].push(r)}function Ii(e){let t=Object.create(null),r=Object.create(null),n=!1,s=!1,o=!1,i,a,l=-1,u=-1,c=-1,g=0;for(;g<e.length;g++)if(u=e.charCodeAt(g),i===void 0)if(c===-1&&qe[u]===1)l===-1&&(l=g);else if(g!==0&&(u===32||u===9))c===-1&&l!==-1&&(c=g);else if(u===59||u===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${g}`);c===-1&&(c=g);let b=e.slice(l,c);u===44?(ae(t,b,r),r=Object.create(null)):i=b,l=c=-1}else throw new SyntaxError(`Unexpected character at index ${g}`);else if(a===void 0)if(c===-1&&qe[u]===1)l===-1&&(l=g);else if(u===32||u===9)c===-1&&l!==-1&&(c=g);else if(u===59||u===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${g}`);c===-1&&(c=g),ae(r,e.slice(l,c),!0),u===44&&(ae(t,i,r),r=Object.create(null),i=void 0),l=c=-1}else if(u===61&&l!==-1&&c===-1)a=e.slice(l,g),l=c=-1;else throw new SyntaxError(`Unexpected character at index ${g}`);else if(s){if(qe[u]!==1)throw new SyntaxError(`Unexpected character at index ${g}`);l===-1?l=g:n||(n=!0),s=!1}else if(o)if(qe[u]===1)l===-1&&(l=g);else if(u===34&&l!==-1)o=!1,c=g;else if(u===92)s=!0;else throw new SyntaxError(`Unexpected character at index ${g}`);else if(u===34&&e.charCodeAt(g-1)===61)o=!0;else if(c===-1&&qe[u]===1)l===-1&&(l=g);else if(l!==-1&&(u===32||u===9))c===-1&&(c=g);else if(u===59||u===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${g}`);c===-1&&(c=g);let b=e.slice(l,c);n&&(b=b.replace(/\\/g,""),n=!1),ae(r,a,b),u===44&&(ae(t,i,r),r=Object.create(null),i=void 0),a=void 0,l=c=-1}else throw new SyntaxError(`Unexpected character at index ${g}`);if(l===-1||o||u===32||u===9)throw new SyntaxError("Unexpected end of input");c===-1&&(c=g);let p=e.slice(l,c);return i===void 0?ae(t,p,r):(a===void 0?ae(r,p,!0):n?ae(r,a,p.replace(/\\/g,"")):ae(r,a,p),ae(t,i,r)),t}function Ni(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(n=>[t].concat(Object.keys(n).map(s=>{let o=n[s];return Array.isArray(o)||(o=[o]),o.map(i=>i===!0?s:`${s}=${i}`).join("; ")})).join("; ")).join(", ")}).join(", ")}Jn.exports={format:Ni,parse:Ii}});var or=w((Tl,cs)=>{"use strict";var Pi=E("events"),Li=E("https"),Di=E("http"),rs=E("net"),Bi=E("tls"),{randomBytes:Hi,createHash:Mi}=E("crypto"),{Duplex:vl,Readable:El}=E("stream"),{URL:tr}=E("url"),me=$e(),$i=Qt(),Fi=Zt(),{BINARY_TYPES:es,EMPTY_BUFFER:dt,GUID:Ui,kForOnEventAttribute:rr,kListener:ji,kStatusCode:qi,kWebSocket:W,NOOP:ns}=he(),{EventTarget:{addEventListener:Wi,removeEventListener:Gi}}=Zn(),{format:Vi,parse:Ki}=er(),{toBuffer:Xi}=Be(),zi=30*1e3,ss=Symbol("kAborted"),nr=[8,13],pe=["CONNECTING","OPEN","CLOSING","CLOSED"],Qi=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,M=class e extends Pi{constructor(t,r,n){super(),this._binaryType=es[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=dt,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=e.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,t!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,r===void 0?r=[]:Array.isArray(r)||(typeof r=="object"&&r!==null?(n=r,r=[]):r=[r]),os(this,t,r,n)):(this._autoPong=n.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(t){es.includes(t)&&(this._binaryType=t,this._receiver&&(this._receiver._binaryType=t))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(t,r,n){let s=new $i({allowSynchronousEvents:n.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation});this._sender=new Fi(t,this._extensions,n.generateMask),this._receiver=s,this._socket=t,s[W]=this,t[W]=this,s.on("conclude",Ji),s.on("drain",ea),s.on("error",ta),s.on("message",ra),s.on("ping",na),s.on("pong",sa),t.setTimeout&&t.setTimeout(0),t.setNoDelay&&t.setNoDelay(),r.length>0&&t.unshift(r),t.on("close",as),t.on("data",gt),t.on("end",ls),t.on("error",us),this._readyState=e.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=e.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[me.extensionName]&&this._extensions[me.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=e.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(t,r){if(this.readyState!==e.CLOSED){if(this.readyState===e.CONNECTING){Y(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===e.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=e.CLOSING,this._sender.close(t,r,!this._isServer,n=>{n||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),zi)}}pause(){this.readyState===e.CONNECTING||this.readyState===e.CLOSED||(this._paused=!0,this._socket.pause())}ping(t,r,n){if(this.readyState===e.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof t=="function"?(n=t,t=r=void 0):typeof r=="function"&&(n=r,r=void 0),typeof t=="number"&&(t=t.toString()),this.readyState!==e.OPEN){sr(this,t,n);return}r===void 0&&(r=!this._isServer),this._sender.ping(t||dt,r,n)}pong(t,r,n){if(this.readyState===e.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof t=="function"?(n=t,t=r=void 0):typeof r=="function"&&(n=r,r=void 0),typeof t=="number"&&(t=t.toString()),this.readyState!==e.OPEN){sr(this,t,n);return}r===void 0&&(r=!this._isServer),this._sender.pong(t||dt,r,n)}resume(){this.readyState===e.CONNECTING||this.readyState===e.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(t,r,n){if(this.readyState===e.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof r=="function"&&(n=r,r={}),typeof t=="number"&&(t=t.toString()),this.readyState!==e.OPEN){sr(this,t,n);return}let s={binary:typeof t!="string",mask:!this._isServer,compress:!0,fin:!0,...r};this._extensions[me.extensionName]||(s.compress=!1),this._sender.send(t||dt,s,n)}terminate(){if(this.readyState!==e.CLOSED){if(this.readyState===e.CONNECTING){Y(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=e.CLOSING,this._socket.destroy())}}};Object.defineProperty(M,"CONNECTING",{enumerable:!0,value:pe.indexOf("CONNECTING")});Object.defineProperty(M.prototype,"CONNECTING",{enumerable:!0,value:pe.indexOf("CONNECTING")});Object.defineProperty(M,"OPEN",{enumerable:!0,value:pe.indexOf("OPEN")});Object.defineProperty(M.prototype,"OPEN",{enumerable:!0,value:pe.indexOf("OPEN")});Object.defineProperty(M,"CLOSING",{enumerable:!0,value:pe.indexOf("CLOSING")});Object.defineProperty(M.prototype,"CLOSING",{enumerable:!0,value:pe.indexOf("CLOSING")});Object.defineProperty(M,"CLOSED",{enumerable:!0,value:pe.indexOf("CLOSED")});Object.defineProperty(M.prototype,"CLOSED",{enumerable:!0,value:pe.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(M.prototype,e,{enumerable:!0})});["open","error","close","message"].forEach(e=>{Object.defineProperty(M.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[rr])return t[ji];return null},set(t){for(let r of this.listeners(e))if(r[rr]){this.removeListener(e,r);break}typeof t=="function"&&this.addEventListener(e,t,{[rr]:!0})}})});M.prototype.addEventListener=Wi;M.prototype.removeEventListener=Gi;cs.exports=M;function os(e,t,r,n){let s={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:nr[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...n,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(e._autoPong=s.autoPong,!nr.includes(s.protocolVersion))throw new RangeError(`Unsupported protocol version: ${s.protocolVersion} (supported versions: ${nr.join(", ")})`);let o;if(t instanceof tr)o=t;else try{o=new tr(t)}catch{throw new SyntaxError(`Invalid URL: ${t}`)}o.protocol==="http:"?o.protocol="ws:":o.protocol==="https:"&&(o.protocol="wss:"),e._url=o.href;let i=o.protocol==="wss:",a=o.protocol==="ws+unix:",l;if(o.protocol!=="ws:"&&!i&&!a?l=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`:a&&!o.pathname?l="The URL's pathname is empty":o.hash&&(l="The URL contains a fragment identifier"),l){let m=new SyntaxError(l);if(e._redirects===0)throw m;ht(e,m);return}let u=i?443:80,c=Hi(16).toString("base64"),g=i?Li.request:Di.request,p=new Set,b;if(s.createConnection=s.createConnection||(i?Zi:Yi),s.defaultPort=s.defaultPort||u,s.port=o.port||u,s.host=o.hostname.startsWith("[")?o.hostname.slice(1,-1):o.hostname,s.headers={...s.headers,"Sec-WebSocket-Version":s.protocolVersion,"Sec-WebSocket-Key":c,Connection:"Upgrade",Upgrade:"websocket"},s.path=o.pathname+o.search,s.timeout=s.handshakeTimeout,s.perMessageDeflate&&(b=new me(s.perMessageDeflate!==!0?s.perMessageDeflate:{},!1,s.maxPayload),s.headers["Sec-WebSocket-Extensions"]=Vi({[me.extensionName]:b.offer()})),r.length){for(let m of r){if(typeof m!="string"||!Qi.test(m)||p.has(m))throw new SyntaxError("An invalid or duplicated subprotocol was specified");p.add(m)}s.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(s.origin&&(s.protocolVersion<13?s.headers["Sec-WebSocket-Origin"]=s.origin:s.headers.Origin=s.origin),(o.username||o.password)&&(s.auth=`${o.username}:${o.password}`),a){let m=s.path.split(":");s.socketPath=m[0],s.path=m[1]}let _;if(s.followRedirects){if(e._redirects===0){e._originalIpc=a,e._originalSecure=i,e._originalHostOrSocketPath=a?s.socketPath:o.host;let m=n&&n.headers;if(n={...n,headers:{}},m)for(let[v,k]of Object.entries(m))n.headers[v.toLowerCase()]=k}else if(e.listenerCount("redirect")===0){let m=a?e._originalIpc?s.socketPath===e._originalHostOrSocketPath:!1:e._originalIpc?!1:o.host===e._originalHostOrSocketPath;(!m||e._originalSecure&&!i)&&(delete s.headers.authorization,delete s.headers.cookie,m||delete s.headers.host,s.auth=void 0)}s.auth&&!n.headers.authorization&&(n.headers.authorization="Basic "+Buffer.from(s.auth).toString("base64")),_=e._req=g(s),e._redirects&&e.emit("redirect",e.url,_)}else _=e._req=g(s);s.timeout&&_.on("timeout",()=>{Y(e,_,"Opening handshake has timed out")}),_.on("error",m=>{_===null||_[ss]||(_=e._req=null,ht(e,m))}),_.on("response",m=>{let v=m.headers.location,k=m.statusCode;if(v&&s.followRedirects&&k>=300&&k<400){if(++e._redirects>s.maxRedirects){Y(e,_,"Maximum redirects exceeded");return}_.abort();let $;try{$=new tr(v,t)}catch{let q=new SyntaxError(`Invalid URL: ${v}`);ht(e,q);return}os(e,$,r,n)}else e.emit("unexpected-response",_,m)||Y(e,_,`Unexpected server response: ${m.statusCode}`)}),_.on("upgrade",(m,v,k)=>{if(e.emit("upgrade",m),e.readyState!==M.CONNECTING)return;_=e._req=null;let $=m.headers.upgrade;if($===void 0||$.toLowerCase()!=="websocket"){Y(e,v,"Invalid Upgrade header");return}let O=Mi("sha1").update(c+Ui).digest("base64");if(m.headers["sec-websocket-accept"]!==O){Y(e,v,"Invalid Sec-WebSocket-Accept header");return}let q=m.headers["sec-websocket-protocol"],B;if(q!==void 0?p.size?p.has(q)||(B="Server sent an invalid subprotocol"):B="Server sent a subprotocol but none was requested":p.size&&(B="Server sent no subprotocol"),B){Y(e,v,B);return}q&&(e._protocol=q);let ee=m.headers["sec-websocket-extensions"];if(ee!==void 0){if(!b){Y(e,v,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let F;try{F=Ki(ee)}catch{Y(e,v,"Invalid Sec-WebSocket-Extensions header");return}let S=Object.keys(F);if(S.length!==1||S[0]!==me.extensionName){Y(e,v,"Server indicated an extension that was not requested");return}try{b.accept(F[me.extensionName])}catch{Y(e,v,"Invalid Sec-WebSocket-Extensions header");return}e._extensions[me.extensionName]=b}e.setSocket(v,k,{allowSynchronousEvents:s.allowSynchronousEvents,generateMask:s.generateMask,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation})}),s.finishRequest?s.finishRequest(_,e):_.end()}function ht(e,t){e._readyState=M.CLOSING,e.emit("error",t),e.emitClose()}function Yi(e){return e.path=e.socketPath,rs.connect(e)}function Zi(e){return e.path=void 0,!e.servername&&e.servername!==""&&(e.servername=rs.isIP(e.host)?"":e.host),Bi.connect(e)}function Y(e,t,r){e._readyState=M.CLOSING;let n=new Error(r);Error.captureStackTrace(n,Y),t.setHeader?(t[ss]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(ht,e,n)):(t.destroy(n),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function sr(e,t,r){if(t){let n=Xi(t).length;e._socket?e._sender._bufferedBytes+=n:e._bufferedAmount+=n}if(r){let n=new Error(`WebSocket is not open: readyState ${e.readyState} (${pe[e.readyState]})`);process.nextTick(r,n)}}function Ji(e,t){let r=this[W];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,r._socket[W]!==void 0&&(r._socket.removeListener("data",gt),process.nextTick(is,r._socket),e===1005?r.close():r.close(e,t))}function ea(){let e=this[W];e.isPaused||e._socket.resume()}function ta(e){let t=this[W];t._socket[W]!==void 0&&(t._socket.removeListener("data",gt),process.nextTick(is,t._socket),t.close(e[qi])),t.emit("error",e)}function ts(){this[W].emitClose()}function ra(e,t){this[W].emit("message",e,t)}function na(e){let t=this[W];t._autoPong&&t.pong(e,!this._isServer,ns),t.emit("ping",e)}function sa(e){this[W].emit("pong",e)}function is(e){e.resume()}function as(){let e=this[W];this.removeListener("close",as),this.removeListener("data",gt),this.removeListener("end",ls),e._readyState=M.CLOSING;let t;!this._readableState.endEmitted&&!e._closeFrameReceived&&!e._receiver._writableState.errorEmitted&&(t=e._socket.read())!==null&&e._receiver.write(t),e._receiver.end(),this[W]=void 0,clearTimeout(e._closeTimer),e._receiver._writableState.finished||e._receiver._writableState.errorEmitted?e.emitClose():(e._receiver.on("error",ts),e._receiver.on("finish",ts))}function gt(e){this[W]._receiver.write(e)||this.pause()}function ls(){let e=this[W];e._readyState=M.CLOSING,e._receiver.end(),this.end()}function us(){let e=this[W];this.removeListener("error",us),this.on("error",ns),e&&(e._readyState=M.CLOSING,this.destroy())}});var ps=w((Cl,fs)=>{"use strict";var{tokenChars:oa}=Fe();function ia(e){let t=new Set,r=-1,n=-1,s=0;for(s;s<e.length;s++){let i=e.charCodeAt(s);if(n===-1&&oa[i]===1)r===-1&&(r=s);else if(s!==0&&(i===32||i===9))n===-1&&r!==-1&&(n=s);else if(i===44){if(r===-1)throw new SyntaxError(`Unexpected character at index ${s}`);n===-1&&(n=s);let a=e.slice(r,n);if(t.has(a))throw new SyntaxError(`The "${a}" subprotocol is duplicated`);t.add(a),r=n=-1}else throw new SyntaxError(`Unexpected character at index ${s}`)}if(r===-1||n!==-1)throw new SyntaxError("Unexpected end of input");let o=e.slice(r,s);if(t.has(o))throw new SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}fs.exports={parse:ia}});var xs=w((Al,ys)=>{"use strict";var aa=E("events"),mt=E("http"),{Duplex:Rl}=E("stream"),{createHash:la}=E("crypto"),ds=er(),ve=$e(),ua=ps(),ca=or(),{GUID:fa,kWebSocket:pa}=he(),da=/^[+/0-9A-Za-z]{22}==$/,hs=0,gs=1,_s=2,ir=class extends aa{constructor(t,r){if(super(),t={allowSynchronousEvents:!0,autoPong:!0,maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:ca,...t},t.port==null&&!t.server&&!t.noServer||t.port!=null&&(t.server||t.noServer)||t.server&&t.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(t.port!=null?(this._server=mt.createServer((n,s)=>{let o=mt.STATUS_CODES[426];s.writeHead(426,{"Content-Length":o.length,"Content-Type":"text/plain"}),s.end(o)}),this._server.listen(t.port,t.host,t.backlog,r)):t.server&&(this._server=t.server),this._server){let n=this.emit.bind(this,"connection");this._removeListeners=ha(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(s,o,i)=>{this.handleUpgrade(s,o,i,n)}})}t.perMessageDeflate===!0&&(t.perMessageDeflate={}),t.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=t,this._state=hs}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(t){if(this._state===_s){t&&this.once("close",()=>{t(new Error("The server is not running"))}),process.nextTick(We,this);return}if(t&&this.once("close",t),this._state!==gs)if(this._state=gs,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(We,this):process.nextTick(We,this);else{let r=this._server;this._removeListeners(),this._removeListeners=this._server=null,r.close(()=>{We(this)})}}shouldHandle(t){if(this.options.path){let r=t.url.indexOf("?");if((r!==-1?t.url.slice(0,r):t.url)!==this.options.path)return!1}return!0}handleUpgrade(t,r,n,s){r.on("error",ms);let o=t.headers["sec-websocket-key"],i=t.headers.upgrade,a=+t.headers["sec-websocket-version"];if(t.method!=="GET"){Ee(this,t,r,405,"Invalid HTTP method");return}if(i===void 0||i.toLowerCase()!=="websocket"){Ee(this,t,r,400,"Invalid Upgrade header");return}if(o===void 0||!da.test(o)){Ee(this,t,r,400,"Missing or invalid Sec-WebSocket-Key header");return}if(a!==8&&a!==13){Ee(this,t,r,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(t)){Ge(r,400);return}let l=t.headers["sec-websocket-protocol"],u=new Set;if(l!==void 0)try{u=ua.parse(l)}catch{Ee(this,t,r,400,"Invalid Sec-WebSocket-Protocol header");return}let c=t.headers["sec-websocket-extensions"],g={};if(this.options.perMessageDeflate&&c!==void 0){let p=new ve(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let b=ds.parse(c);b[ve.extensionName]&&(p.accept(b[ve.extensionName]),g[ve.extensionName]=p)}catch{Ee(this,t,r,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let p={origin:t.headers[`${a===8?"sec-websocket-origin":"origin"}`],secure:!!(t.socket.authorized||t.socket.encrypted),req:t};if(this.options.verifyClient.length===2){this.options.verifyClient(p,(b,_,m,v)=>{if(!b)return Ge(r,_||401,m,v);this.completeUpgrade(g,o,u,t,r,n,s)});return}if(!this.options.verifyClient(p))return Ge(r,401)}this.completeUpgrade(g,o,u,t,r,n,s)}completeUpgrade(t,r,n,s,o,i,a){if(!o.readable||!o.writable)return o.destroy();if(o[pa])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>hs)return Ge(o,503);let u=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${la("sha1").update(r+fa).digest("base64")}`],c=new this.options.WebSocket(null,void 0,this.options);if(n.size){let g=this.options.handleProtocols?this.options.handleProtocols(n,s):n.values().next().value;g&&(u.push(`Sec-WebSocket-Protocol: ${g}`),c._protocol=g)}if(t[ve.extensionName]){let g=t[ve.extensionName].params,p=ds.format({[ve.extensionName]:[g]});u.push(`Sec-WebSocket-Extensions: ${p}`),c._extensions=t}this.emit("headers",u,s),o.write(u.concat(`\r
`).join(`\r
`)),o.removeListener("error",ms),c.setSocket(o,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(c),c.on("close",()=>{this.clients.delete(c),this._shouldEmitClose&&!this.clients.size&&process.nextTick(We,this)})),a(c,s)}};ys.exports=ir;function ha(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let n of Object.keys(t))e.removeListener(n,t[n])}}function We(e){e._state=_s,e.emit("close")}function ms(){this.destroy()}function Ge(e,t,r,n){r=r||mt.STATUS_CODES[t],n={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r),...n},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${mt.STATUS_CODES[t]}\r
`+Object.keys(n).map(s=>`${s}: ${n[s]}`).join(`\r
`)+`\r
\r
`+r)}function Ee(e,t,r,n,s){if(e.listenerCount("wsClientError")){let o=new Error(s);Error.captureStackTrace(o,Ee),e.emit("wsClientError",o,r,t)}else Ge(r,n,s)}});var bt=E("child_process"),St=se(E("fs")),Te=se(E("inspector")),Ps=se(yn()),vt=se(E("path"));var si={"pwa-extensionHost":null,"node-terminal":null,"pwa-node":null,"pwa-chrome":null,"pwa-msedge":null},oi={"extension.js-debug.addCustomBreakpoints":null,"extension.js-debug.addXHRBreakpoints":null,"extension.js-debug.editXHRBreakpoints":null,"extension.pwa-node-debug.attachNodeProcess":null,"extension.js-debug.clearAutoAttachVariables":null,"extension.js-debug.setAutoAttachVariables":null,"extension.js-debug.autoAttachToProcess":null,"extension.js-debug.createDebuggerTerminal":null,"extension.js-debug.createDiagnostics":null,"extension.js-debug.getDiagnosticLogs":null,"extension.js-debug.debugLink":null,"extension.js-debug.npmScript":null,"extension.js-debug.pickNodeProcess":null,"extension.js-debug.prettyPrint":null,"extension.js-debug.removeXHRBreakpoint":null,"extension.js-debug.removeAllCustomBreakpoints":null,"extension.js-debug.revealPage":null,"extension.js-debug.startProfile":null,"extension.js-debug.stopProfile":null,"extension.js-debug.toggleSkippingFile":null,"extension.node-debug.startWithStopOnEntry":null,"extension.js-debug.requestCDPProxy":null,"extension.js-debug.openEdgeDevTools":null,"extension.js-debug.callers.add":null,"extension.js-debug.callers.goToCaller":null,"extension.js-debug.callers.gotToTarget":null,"extension.js-debug.callers.remove":null,"extension.js-debug.callers.removeAll":null,"extension.js-debug.enableSourceMapStepping":null,"extension.js-debug.disableSourceMapStepping":null,"extension.js-debug.network.viewRequest":null,"extension.js-debug.network.copyUri":null,"extension.js-debug.network.openBody":null,"extension.js-debug.network.openBodyInHex":null,"extension.js-debug.network.replayXHR":null,"extension.js-debug.network.clear":null,"extension.js-debug.completion.nodeTool":null},ll=new Set(Object.keys(oi)),ul=new Set(Object.keys(si));var Ss=E("child_process");var ga=se(vn(),1),ma=se(Qt(),1),_a=se(Zt(),1),ya=se(or(),1),bs=se(xs(),1);var Il=Symbol("unset");var Nl=2**31-1;var Sa=Object.freeze(function(e,t){let r=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(r)}}}),va=Object.freeze({isCancellationRequested:!1,onCancellationRequested:()=>({dispose:()=>{}})}),Vl=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Sa});var ar=(e,t)=>e+Math.floor(Math.random()*(t-e));function vs({min:e=53e3,max:t=54e3,attempts:r=1e3}={}){let n=Ea(),s=ar(e,t);for(let o=Math.min(r,t-e);o>=0;o--){if(n(s))return s;s=s===t-1?e:s+1}throw new Error("No open port found")}var Ea=()=>e=>(0,Ss.spawnSync)(process.execPath,["-e",'require("net").createServer().on("listening",()=>process.exit(0)).on("error",()=>process.exit(1)).listen(+process.env.PORT)'],{env:{...process.env,PORT:String(e),NODE_OPTIONS:void 0,ELECTRON_RUN_AS_NODE:"1"}}).status===0;var Ta=new Set(["mocha","jest","jest-cli","ava","tape","tap","ts-node","babel-node"]),Es="$KNOWN_TOOLS$",Ts=`{${[...Ta].join(",")}}`;var Ca={runtime:null,"runtime.sourcecreate":null,"runtime.assertion":null,"runtime.launch":null,"runtime.target":null,"runtime.welcome":null,"runtime.exception":null,"runtime.sourcemap":null,"runtime.breakpoints":null,"sourcemap.parsing":null,"perf.function":null,"cdp.send":null,"cdp.receive":null,"dap.send":null,"dap.receive":null,internal:null,proxyActivity:null},fu=Object.keys(Ca),pu=Symbol("ILogger");var Ra=":::",_t=class{constructor(t){this.processEnv=t}get nodeOptions(){return this.processEnv.NODE_OPTIONS}set nodeOptions(t){t===void 0?delete this.processEnv.NODE_OPTIONS:this.processEnv.NODE_OPTIONS=t}get inspectorOptions(){let t=this.processEnv.VSCODE_INSPECTOR_OPTIONS;if(!t)return;let r=t.split(Ra).find(n=>!!n);if(r)try{return JSON.parse(r)}catch{return}}set inspectorOptions(t){t===void 0?delete this.processEnv.VSCODE_INSPECTOR_OPTIONS:this.processEnv.VSCODE_INSPECTOR_OPTIONS=JSON.stringify(t)}unsetForTree(){delete this.processEnv.VSCODE_INSPECTOR_OPTIONS}updateInspectorOption(t,r){let n=this.inspectorOptions;n&&(this.inspectorOptions={...n,[t]:r})}};var xt=E("path");var Cs=E("crypto"),Ve=E("fs"),Rs=E("os"),As=se(E("path")),Ie=class Ie{constructor(){this.disposed=!1;this.path=As.join((0,Rs.tmpdir)(),`node-debug-callback-${(0,Cs.randomBytes)(8).toString("hex")}`);this.file=Ve.promises.open(this.path,"w")}static isValid(t){try{let r=(0,Ve.readFileSync)(t);return r.length?r.readDoubleBE()>Date.now()-Ie.recencyDeadline:!1}catch{return!1}}async startTouchLoop(){await this.touch(),this.disposed||(this.updateInterval=setInterval(()=>this.touch(),Ie.updateInterval))}async touch(t=()=>Date.now()){let r=await this.file,n=Buffer.alloc(8);n.writeDoubleBE(t()),await r.write(n,0,n.length,0)}async dispose(){if(!this.disposed){this.disposed=!0,this.updateInterval&&clearInterval(this.updateInterval);try{await(await this.file).close(),await Ve.promises.unlink(this.path)}catch{}}}};Ie.updateInterval=1e3,Ie.recencyDeadline=2e3;var yt=Ie;var le={enabled:!1,info:(...e)=>{le.enabled&&console.log(...e)}};var wa=e=>!e||!e.inspectorIpc?(le.info("runtime.launch","Disabling due to lack of IPC server"),!1):!0,ka=e=>{let t=e.requireLease;return t&&!yt.isValid(t)?(le.info("runtime.launch","Disabling due to invalid lease file"),!1):!0},Oa=()=>typeof window<"u"?(le.info("runtime.launch","Disabling in Electron (window is set)"),!1):!0,Ia=e=>{let t="";try{t=E.resolve(process.argv[1])}catch{t=process.argv[1]}let r;try{r=new RegExp(e.waitForDebugger||"").test(t)}catch{r=!0}return r||le.info("runtime.launch","Disabling due to not matching pattern",{pattern:e.waitForDebugger,scriptName:t}),r},Na=()=>{let e=process.argv;return!(e.length===4&&(0,xt.basename)(e[1])==="npm-cli.js"&&e[2]==="prefix"&&e[3]==="-g")},Pa=()=>{let e=process.argv;return!(e.length===2&&(0,xt.basename)(e[1])==="npm-prefix.js")},La=e=>!(e.deferredMode&&process.argv.length>=2&&(0,xt.basename)(process.argv[1])==="node-gyp.js"),Da=[La,wa,ka,Oa,Ia,Na,Pa],ws=e=>!!e&&!Da.some(t=>!t(e));var lr=E("path"),ks=(0,lr.join)(__dirname,"watchdog.js"),vu=(0,lr.join)(__dirname,"bootloader.js");var Os=E("crypto"),Is=()=>(0,Os.randomBytes)(12).toString("hex");var Ls={cwd:process.cwd(),processId:process.pid,nodeVersion:process.version,architecture:process.arch},Ns="$jsDebugIsRegistered";(()=>{try{if(Ns in global)return;let e=new _t(process.env),t=e.inspectorOptions;if(le.enabled=!!(t!=null&&t.verbose),le.info("runtime.launch","Bootloader imported",{env:t,args:process.argv}),Object.defineProperty(global,Ns,{value:!0,enumerable:!1}),!ws(t)){e.unsetForTree();return}try{if(!E("worker_threads").isMainThread)return}catch{}ja(e),/(\\|\/|^)node(64)?(.exe)?$/.test(process.execPath)&&(t.execPath=process.execPath);let r=Is(),n=Ba(t,r);t.onlyEntrypoint?e.unsetForTree():n&&e.updateInspectorOption("openerId",r)}catch(e){console.error(`Error in the js-debug bootloader, please report to https://aka.ms/js-dbg-issue: ${e.stack||e.message||e}`)}})();function Ba(e,t){let r=Ua(e.inspectorIpc)?e.deferredMode?1:0:2;if(le.info("runtime","Set debug mode",{mode:r}),r===2)return!1;let n=Te.url()!==void 0;if(!n){if(!Ma(e))return!1;Te.open(Ha(e),void 0,!1)}let s={ipcAddress:e.inspectorIpc||"",pid:String(process.pid),telemetry:Ls,scriptName:process.argv[1],inspectorURL:Te.url(),waitForDebugger:!0,ownId:t,openerId:e.openerId};if(r===0)qa(e.execPath||process.execPath,s);else{let{status:i,stderr:a}=(0,bt.spawnSync)(e.execPath||process.execPath,["-e",'const c=require("net").createConnection(process.env.NODE_INSPECTOR_IPC);setTimeout(()=>{console.error("timeout"),process.exit(1)},10000),c.on("error",e=>{console.error(e),process.exit(1)}),c.on("connect",()=>{c.write(process.env.NODE_INSPECTOR_INFO,"utf-8"),c.write(Buffer.from([0])),c.on("data",e=>{console.error("read byte",e[0]),process.exit(e[0])})});'],{env:{NODE_SKIP_PLATFORM_CHECK:process.env.NODE_SKIP_PLATFORM_CHECK,NODE_INSPECTOR_INFO:JSON.stringify(s),NODE_INSPECTOR_IPC:e.inspectorIpc,ELECTRON_RUN_AS_NODE:"1"}});if(i)return console.error(a.toString()),console.error("Error activating auto attach, please report to https://aka.ms/js-dbg-issue"),!1}let o=Te;return o.waitForDebugger?o.waitForDebugger():Te.open(n?void 0:0,void 0,!0),!0}function Ha(e){if(!e.mandatePortTracking)return 0;try{return vs({attempts:20})}catch{return 0}}function Ma(e){switch(e.autoAttachMode){case"always":return!0;case"smart":return $a(e);case"onlyWithFlag":default:return!1}}function $a(e){let t=process.argv[1];return t?Fa(t,e):!0}function Fa(e,t){return t.aaPatterns?(0,Ps.default)([e.replace(/\\/g,"/")],[...t.aaPatterns.map(n=>n.replace(Es,Ts))],{dot:!0,nocase:!0}).length>0:!1}function Ua(e){if(!e)return!1;try{return St.readdirSync(vt.dirname(e)).includes(vt.basename(e))}catch{return!1}}function ja(e){var r;let t=(r=e.inspectorOptions)==null?void 0:r.fileCallback;if(t){try{St.writeFileSync(t,JSON.stringify(Ls))}catch{}e.updateInspectorOption("fileCallback",void 0)}}function qa(e,t){let r=(0,bt.spawn)(e,[ks],{env:{NODE_INSPECTOR_INFO:JSON.stringify(t),NODE_SKIP_PLATFORM_CHECK:process.env.NODE_SKIP_PLATFORM_CHECK,ELECTRON_RUN_AS_NODE:"1"},stdio:"ignore",detached:!0});return r.unref(),r}})();
/*! Bundled license information:

is-number/index.js:
  (*!
   * is-number <https://github.com/jonschlinkert/is-number>
   *
   * Copyright (c) 2014-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

to-regex-range/index.js:
  (*!
   * to-regex-range <https://github.com/micromatch/to-regex-range>
   *
   * Copyright (c) 2015-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

fill-range/index.js:
  (*!
   * fill-range <https://github.com/jonschlinkert/fill-range>
   *
   * Copyright (c) 2014-present, Jon Schlinkert.
   * Licensed under the MIT License.
   *)
*/
//# sourceMappingURL=bootloader.js.map

//# sourceURL=bootloader.bundle.cdp