import React, { useState, useEffect, useRef } from "react";
import {
  HiXMark,
  HiChevronLeft,
  HiChevronRight,
  HiUser,
} from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { getConfidenceColor } from "../../utils/colorUtils";
import { usePicks } from "../../contexts/PicksContext";
import { useAuth } from "../../contexts/AuthContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { generateHandicapperIdFromName } from "../../utils/dataTransforms";

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

interface PickDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  pick: Pick | null;
  sourceType?: string;
}

interface ResearchItem {
  id: number;
  title: string;
  content: string;
}

const PickDetailModal: React.FC<PickDetailModalProps> = ({
  isOpen,
  onClose,
  pick,
  sourceType,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentResearchIndex, setCurrentResearchIndex] = useState(0);
  const [isPickedByExpanded, setIsPickedByExpanded] = useState(false);
  const [chartData, setChartData] = useState<string | null>(null);
  const [loadingChart, setLoadingChart] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const { addPick, removePick, isPickSelected, isPickSelectedByContent } =
    usePicks();
  const { navigateToView } = useAuth();
  const { selectHandicapper } = useHandicapperProfile();

  // Mock research data - replace with real data later
  const researchItems: ResearchItem[] = [
    {
      id: 1,
      title: "Capper Research 1",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 2,
      title: "Capper Research 2",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 3,
      title: "Capper Research 3",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 4,
      title: "Capper Research 4",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 5,
      title: "Capper Research 5",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 6,
      title: "Capper Research 6",
      content: "Placeholder text for handicapper research",
    },
    {
      id: 7,
      title: "Capper Research 7",
      content: "Placeholder text for handicapper research",
    },
  ];

  // Handle modal open/close animations
  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
      const timer = setTimeout(() => setIsAnimating(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isOpen, onClose]);

  // Fetch chart data when modal opens and pick is available
  useEffect(() => {
    const fetchChartData = async () => {
      if (!isOpen || !pick || !pick.id) return;

      setLoadingChart(true);
      try {
        // Generate event_id for the pick - this should match the format used in model confidence
        const today = new Date().toISOString().split("T")[0];
        const cleanPlayerName = pick.playerName
          .replace(/\s+/g, "")
          .toUpperCase();

        // Try to extract threshold and stat type from betType (e.g., "25.5 Points" -> threshold: 25.5, stat: POINTS)
        const betTypeMatch = pick.betType.match(/^(\d+\.?\d*)\s*(.+)$/);
        let threshold = "0.5";
        let statType = "POINTS";

        if (betTypeMatch) {
          threshold = betTypeMatch[1];
          statType = betTypeMatch[2].replace(/\s+/g, "").toUpperCase();
        }

        // Try to extract league from gameInfo
        let league = "NBA"; // Default assumption
        if (
          pick.gameInfo.toLowerCase().includes("mlb") ||
          pick.gameInfo.toLowerCase().includes("baseball")
        ) {
          league = "MLB";
        } else if (
          pick.gameInfo.toLowerCase().includes("nfl") ||
          pick.gameInfo.toLowerCase().includes("football")
        ) {
          league = "NFL";
        } else if (
          pick.gameInfo.toLowerCase().includes("nhl") ||
          pick.gameInfo.toLowerCase().includes("hockey")
        ) {
          league = "NHL";
        }

        const eventId = `${today}-${league}-${cleanPlayerName}${threshold}${statType}`;
        console.log("Generated event_id for chart:", eventId);

        const response = await fetch(`/api/player_chart/${eventId}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.chart_data) {
            setChartData(result.chart_data);
          } else {
            console.error("Chart fetch failed:", result.message);
            setChartData(null);
          }
        } else {
          console.error("Chart API call failed with status:", response.status);
          setChartData(null);
        }
      } catch (error) {
        console.error("Error fetching chart data:", error);
        setChartData(null);
      } finally {
        setLoadingChart(false);
      }
    };

    fetchChartData();
  }, [isOpen, pick]);

  // Research navigation functions
  const goToPreviousResearch = () => {
    setCurrentResearchIndex((prev) =>
      prev === 0 ? researchItems.length - 1 : prev - 1
    );
  };

  const goToNextResearch = () => {
    setCurrentResearchIndex((prev) =>
      prev === researchItems.length - 1 ? 0 : prev + 1
    );
  };

  // Toggle picked by expansion
  const togglePickedByExpanded = () => {
    setIsPickedByExpanded((prev) => !prev);
  };

  // Handle handicapper click for navigation
  const handleHandicapperClick = (handicapperName: string) => {
    // Generate deterministic ID from handicapper name
    const handicapperId = generateHandicapperIdFromName(handicapperName);
    selectHandicapper(handicapperId);
    navigateToView("handicapperProfile");
    onClose(); // Close the modal when navigating
  };

  // Handle add to list (toggle functionality)
  const handleAddToList = () => {
    if (!pick) return;

    // Check if pick exists by content (regardless of source type)
    const existingPick = isPickSelectedByContent(
      pick.playerName || "Unknown Player",
      pick.betType || "Standard Bet",
      pick.gameInfo || "Game info unavailable"
    );

    if (existingPick) {
      // Remove the existing pick (regardless of source)
      removePick(existingPick.id);
    } else {
      // Add the pick if it's not selected
      addPick({
        sourceType: "pick",
        sourceId: pick.id,
        playerName: pick.playerName || "Unknown Player",
        playerNumber: pick.playerNumber || "?",
        betType: pick.betType || "Standard Bet",
        gameInfo: pick.gameInfo || "Game info unavailable",
        confidence: pick.confidence || 50,
      });
    }
  };

  if (!isAnimating && !isOpen) return null;
  if (!pick) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ease-in-out ${
        isOpen ? "backdrop-blur-sm" : "bg-transparent pointer-events-none"
      }`}
      style={{
        backgroundColor: isOpen ? "rgba(0, 0, 0, 0.15)" : "transparent",
      }}
    >
      <div
        ref={modalRef}
        className={`bg-[#233e6c] rounded-xl shadow-2xl w-full max-w-7xl max-h-[95vh] lg:max-h-[70vh] overflow-hidden transition-all duration-300 ease-in-out transform ${
          isOpen
            ? "scale-100 opacity-100 translate-y-0"
            : "scale-95 opacity-0 translate-y-4"
        }`}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center transition-colors duration-200"
        >
          <HiXMark className="w-5 h-5 text-white" />
        </button>

        <div className="p-4 lg:p-6 overflow-y-auto max-h-[95vh] lg:max-h-[70vh]">
          {/* Top Section - 2x3 Grid Layout with custom column widths */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-[3fr_1fr_2fr] lg:grid-rows-2 gap-4 lg:gap-6 mb-6 lg:h-[500px]">
            {/* Row 1, Column 1 - Jersey Icon + Player Info */}
            <div className="flex items-center gap-6 lg:gap-8 lg:h-full">
              {/* Jersey Icon - Significantly larger to fill vertical space */}
              <div
                className="w-32 h-32 lg:w-48 lg:h-48 xl:w-56 xl:h-56 rounded-full flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden flex-shrink-0"
                style={{
                  border: `4px solid ${getConfidenceColor(pick.confidence)}`,
                }}
              >
                <IoShirtOutline
                  className="w-20 h-20 lg:w-32 lg:h-32 xl:w-36 xl:h-36 absolute"
                  style={{ color: getConfidenceColor(pick.confidence) }}
                />
                <div className="text-white font-bold text-xl lg:text-2xl xl:text-3xl z-10 relative">
                  {pick.playerNumber || "?"}
                </div>
              </div>

              {/* Player Information Text - Expanded with better spacing */}
              <div className="flex flex-col justify-center flex-grow min-w-0 h-full py-4 lg:py-6">
                <h2 className="text-2xl lg:text-4xl xl:text-5xl font-bold text-white mb-4 lg:mb-6 xl:mb-8 leading-tight">
                  {pick.playerName || "Unknown Player"}
                </h2>
                <p className="text-lg lg:text-2xl xl:text-3xl font-bold text-white mb-4 lg:mb-6 xl:mb-8 leading-tight">
                  {pick.betType || "Standard Bet"}
                </p>
                <p className="text-base lg:text-xl xl:text-2xl text-gray-300 leading-relaxed whitespace-pre-line">
                  {(pick.gameInfo || "Game info unavailable").replace(
                    /\s\|\s([^|]*)$/,
                    "\n$1"
                  )}
                </p>
              </div>
            </div>

            {/* Row 1, Column 2 - Confidence Rating (much bigger) */}
            <div className="flex flex-col items-center justify-center lg:h-full">
              <div
                className="text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-bold mb-2 lg:mb-4"
                style={{
                  color: getConfidenceColor(pick.confidence || 50),
                }}
              >
                {pick.confidence || 50}
              </div>
              <div className="text-base md:text-lg lg:text-xl xl:text-2xl font-bold text-white text-center">
                Confidence
                <br />
                Rating
              </div>
            </div>

            {/* Row 1 & 2, Column 3 - Game Logs (expanded right column, spans both rows) */}
            <div className="md:col-span-2 lg:col-span-1 lg:row-span-2 bg-[#1a2d54] rounded-lg p-6 lg:p-8 flex items-center justify-center lg:min-h-full">
              <div className="text-center">
                <div className="text-2xl lg:text-4xl xl:text-5xl font-bold text-white mb-2">
                  Game
                  <br />
                  Logs
                </div>
              </div>
            </div>

            {/* Row 2, Column 1 - Player Statistical Distribution Chart */}
            <div className="bg-[#1a2d54] rounded-lg overflow-hidden lg:h-full">
              {loadingChart ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-xl lg:text-2xl font-bold text-white mb-2">
                      Loading Chart...
                    </div>
                  </div>
                </div>
              ) : chartData ? (
                <img
                  src={`data:image/png;base64,${chartData}`}
                  alt="Player Statistical Distribution"
                  className="w-full h-full object-fill"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-xl lg:text-2xl font-bold text-white mb-2">
                      Chart Unavailable
                    </div>
                    <div className="text-sm lg:text-base text-gray-400">
                      Statistical data not found
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Row 2, Column 2 - Picked By Section with expand/collapse */}
            <div className="flex flex-col items-center justify-center lg:h-full">
              <h3 className="text-lg lg:text-2xl xl:text-3xl font-bold text-white mb-2 lg:mb-3 text-center">
                Picked By
              </h3>
              {!isPickedByExpanded ? (
                <>
                  {pick.handicapperNames && pick.handicapperNames.length > 0 ? (
                    <>
                      <div className="flex justify-center mb-2">
                        <div
                          className="w-12 h-12 lg:w-16 lg:h-16 rounded-full border-4 border-blue-400 bg-gray-700 flex items-center justify-center hover:border-blue-300 cursor-pointer transition-all duration-200 hover:scale-105"
                          title={`Click to view ${pick.handicapperNames[0]}'s profile`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleHandicapperClick(pick.handicapperNames[0]);
                          }}
                        >
                          <HiUser className="w-6 h-6 lg:w-10 lg:h-10 text-blue-300 group-hover:text-blue-200 transition-colors duration-200" />
                        </div>
                      </div>
                      {pick.handicapperNames.length > 1 && (
                        <div className="grid grid-cols-4 gap-1 justify-center items-center w-[108px] lg:w-[132px] xl:w-[156px] mx-auto">
                          {pick.handicapperNames
                            .slice(1, 9)
                            .map((name, idx) => (
                              <div
                                key={idx}
                                className="w-5 h-5 lg:w-7 lg:h-7 rounded-full border-2 border-gray-600 bg-gray-700 flex items-center justify-center hover:border-blue-400 cursor-pointer transition-all duration-200 hover:scale-105"
                                title={`Click to view ${name}'s profile`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleHandicapperClick(name);
                                }}
                              >
                                <HiUser className="w-3 h-3 lg:w-4 lg:h-4 text-gray-400 hover:text-blue-300 transition-colors duration-200" />
                              </div>
                            ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="grid grid-cols-3 gap-2 mb-2 justify-center justify-items-center content-start w-[108px] lg:w-[132px] xl:w-[156px] h-[108px] lg:h-[132px] xl:h-[156px] mx-auto">
                      {Array.from({
                        length: Math.min(pick.expertCount || 0, 9),
                      }).map((_, index) => (
                        <div
                          key={index}
                          className="w-7 h-7 lg:w-10 lg:h-10 xl:w-12 xl:h-12 rounded-full border-2 border-gray-600 bg-gray-700 flex items-center justify-center"
                        >
                          <HiUser className="w-3 h-3 lg:w-5 lg:h-5 xl:w-6 xl:h-6 text-gray-400" />
                        </div>
                      ))}
                    </div>
                  )}
                  {(pick.additionalExperts || 0) > 0 && (
                    <p
                      className="text-sm lg:text-base text-gray-400 text-center hover:text-blue-400 cursor-pointer transition-colors duration-200"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent modal from closing
                        togglePickedByExpanded();
                      }}
                    >
                      and {pick.additionalExperts} more
                    </p>
                  )}
                </>
              ) : (
                <>
                  <div className="relative w-[108px] lg:w-[132px] xl:w-[156px] h-[108px] lg:h-[132px] xl:h-[156px] mx-auto">
                    <div
                      className="grid grid-cols-3 gap-2 justify-center justify-items-center content-start w-full h-full overflow-y-auto overflow-x-hidden handicapper-scroll"
                      style={{
                        scrollbarWidth: "thin",
                        scrollbarGutter: "stable overlay",
                        paddingRight: "0px",
                      }}
                    >
                      {pick.handicapperNames && pick.handicapperNames.length > 0
                        ? pick.handicapperNames.map((name, idx) => (
                            <div
                              key={idx}
                              className={
                                idx === 0
                                  ? "w-12 h-12 lg:w-16 lg:h-16 rounded-full border-4 border-blue-400 bg-gray-700 flex items-center justify-center hover:border-blue-300 cursor-pointer transition-all duration-200 hover:scale-105"
                                  : "w-5 h-5 lg:w-7 lg:h-7 rounded-full border-2 border-gray-600 bg-gray-700 flex items-center justify-center hover:border-blue-400 cursor-pointer transition-all duration-200 hover:scale-105"
                              }
                              title={`Click to view ${name}'s profile`}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleHandicapperClick(name);
                              }}
                            >
                              <HiUser
                                className={
                                  idx === 0
                                    ? "w-6 h-6 lg:w-10 lg:h-10 text-blue-300 hover:text-blue-200 transition-colors duration-200"
                                    : "w-3 h-3 lg:w-4 lg:h-4 text-gray-400 hover:text-blue-300 transition-colors duration-200"
                                }
                              />
                            </div>
                          ))
                        : Array.from({
                            length:
                              (pick.expertCount || 0) +
                              (pick.additionalExperts || 0),
                          }).map((_, index) => (
                            <div
                              key={index}
                              className="w-5 h-5 lg:w-7 lg:h-7 rounded-full border-2 border-gray-600 bg-gray-700 flex items-center justify-center"
                            >
                              <HiUser className="w-3 h-3 lg:w-4 lg:h-4 text-gray-400" />
                            </div>
                          ))}
                    </div>
                  </div>
                  <p
                    className="text-sm lg:text-base text-blue-400 text-center hover:text-blue-300 cursor-pointer transition-colors duration-200"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent modal from closing
                      togglePickedByExpanded();
                    }}
                  >
                    show less
                  </p>
                </>
              )}
            </div>
          </div>

          {/* Bottom Section - Research and Add to Cart */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
            {/* Research Gallery - Takes up 3 columns */}
            <div className="lg:col-span-3">
              <h3 className="text-xl lg:text-2xl font-bold text-white mb-4">
                Research
              </h3>
              <div className="relative">
                {/* Navigation Arrows */}
                <button
                  onClick={goToPreviousResearch}
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 lg:w-10 lg:h-10 bg-[#233e6c] border-2 border-white rounded-full flex items-center justify-center hover:bg-[#1a2d54] hover:cursor-pointer transition-colors duration-200"
                >
                  <HiChevronLeft className="w-4 h-4 lg:w-6 lg:h-6 text-white" />
                </button>
                <button
                  onClick={goToNextResearch}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 w-8 h-8 lg:w-10 lg:h-10 bg-[#233e6c] border-2 border-white rounded-full flex items-center justify-center hover:bg-[#1a2d54] hover:cursor-pointer transition-colors duration-200"
                >
                  <HiChevronRight className="w-4 h-4 lg:w-6 lg:h-6 text-white" />
                </button>

                {/* Research Gallery Container - Show 3 items at a time to match mockup */}
                <div className="overflow-hidden mx-10 lg:mx-12">
                  <div
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{
                      transform: `translateX(-${
                        currentResearchIndex * 33.333
                      }%)`,
                    }}
                  >
                    {researchItems.map((item, index) => (
                      <div key={item.id} className="flex-shrink-0 w-1/3 px-2">
                        <div className="bg-[#1a2d54] rounded-lg p-3 lg:p-4 h-24 lg:h-32 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-xs lg:text-sm font-bold text-white mb-1 lg:mb-2">
                              {item.title}
                            </div>
                            <div className="text-xs text-gray-400">
                              {item.content}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Add to List Button - Takes up 1 column */}
            <div className="lg:col-span-1 flex items-end justify-center lg:justify-end">
              <button
                onClick={handleAddToList}
                className={`shadow-md hover:shadow-lg transition-all duration-300 ease-linear text-base lg:text-lg font-bold px-4 lg:px-6 py-2 lg:py-3 rounded-lg whitespace-nowrap hover:cursor-pointer ${
                  isPickSelectedByContent(
                    pick.playerName || "Unknown Player",
                    pick.betType || "Standard Bet",
                    pick.gameInfo || "Game info unavailable"
                  )
                    ? "bg-green-500 text-white hover:bg-green-600"
                    : "bg-white hover:bg-gray-300 text-[#061844]"
                }`}
              >
                {isPickSelectedByContent(
                  pick.playerName || "Unknown Player",
                  pick.betType || "Standard Bet",
                  pick.gameInfo || "Game info unavailable"
                )
                  ? "Added"
                  : "Add to List"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PickDetailModal;
