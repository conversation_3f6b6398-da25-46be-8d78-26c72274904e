import React, { useState } from "react";
import { HiUser } from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { getConfidenceColor } from "../../utils/colorUtils";
import { usePicks } from "../../contexts/PicksContext";
import { useAuth } from "../../contexts/AuthContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { generateHandicapperIdFromName } from "../../utils/dataTransforms";
import PickDetailModal from "./PickDetailModal";
import HandicapperAvatars from "../HandicapperAvatars";
import type { Pick } from "../../types";

interface PicksViewProps {
  picks: Pick[];
}

function PicksView({ picks }: PicksViewProps) {
  const [selectedPick, setSelectedPick] = useState<Pick | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { addPick, removePick, isPickSelected } = usePicks();
  const { navigateToView } = useAuth();
  const { selectHandicapper } = useHandicapperProfile();

  const handleHandicapperClick = (handicapperName: string) => {
    // Generate deterministic ID from handicapper name
    const handicapperId = generateHandicapperIdFromName(handicapperName);
    selectHandicapper(handicapperId);
    navigateToView("handicapperProfile");
  };

  const handlePickClick = (pick: Pick) => {
    setSelectedPick(pick);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPick(null);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {picks.map((pick) => (
        <div
          key={pick.id}
          className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%]"
          onClick={() => handlePickClick(pick)}
        >
          <div className="flex flex-row gap-6 my-auto justify-center items-center">
            <div className="flex flex-col items-center md:flex-1 min-w-0">
              <div
                className="w-32 h-32 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                style={{
                  border: `4px solid ${getConfidenceColor(pick.confidence)}`,
                }}
              >
                <IoShirtOutline
                  className="w-20 h-20 absolute"
                  style={{ color: getConfidenceColor(pick.confidence) }}
                />
                <div className="text-white font-bold text-lg sm:text-xl z-10 relative">
                  {pick.playerNumber || "?"}
                </div>
              </div>
              <h3 className="font-bold text-lg text-center">
                {pick.playerName || "Unknown Player"}
              </h3>
              <p
                className="text-sm text-center font-semibold"
                style={{ color: getConfidenceColor(pick.confidence || 50) }}
              >
                {pick.betType || "Standard Bet"}
              </p>
              <div className="text-xs text-gray-400 text-center">
                <p className="whitespace-nowrap overflow-hidden text-ellipsis">
                  {pick.gameInfo.split(" | ").slice(0, -1).join(" | ")}
                </p>
                <p className="whitespace-nowrap overflow-hidden text-ellipsis">
                  {pick.gameInfo.split(" | ").pop()}
                </p>
              </div>
            </div>

            <div className="flex flex-col items-center gap-2 md:flex-1 min-w-0 m-auto h-full">
              <HandicapperAvatars
                handicapperNames={pick.handicapperNames || []}
                maxVisible={3}
                size="md"
                onHandicapperClick={handleHandicapperClick}
              />
            </div>

            <div className="flex flex-col items-center gap-2 md:flex-1 min-w-0 m-auto h-full">
              <div className="text-center flex flex-col items-center justify-start h-full">
                <div
                  className="text-[75px] md:text-[100px] font-bold mt-[-24px]"
                  style={{ color: getConfidenceColor(pick.confidence || 50) }}
                >
                  {pick.confidence || 50}
                </div>
                <div className="text-[24px] md:text-[24px] font-bold text-white mt-[-24px]">
                  Confidence
                  <br />
                  Rating
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Prevent modal from opening
                  const isSelected = isPickSelected("pick", pick.id);
                  if (isSelected) {
                    // Remove the pick if it's already selected
                    const pickId = `pick_${pick.id}`;
                    removePick(pickId);
                  } else {
                    // Add the pick if it's not selected
                    addPick({
                      sourceType: "pick",
                      sourceId: pick.id,
                      playerName: pick.playerName || "Unknown Player",
                      playerNumber: pick.playerNumber || "?",
                      betType: pick.betType || "Standard Bet",
                      gameInfo: pick.gameInfo || "Game info unavailable",
                      confidence: pick.confidence || 50,
                      handicapperNames: pick.handicapperNames,
                    });
                  }
                }}
                className={`shadow-md hover:shadow-lg transition-all duration-300 ease-linear text-lg md:text-[24px] font-bold px-4 md:px-6 py-2 rounded-lg whitespace-nowrap hover:cursor-pointer ${
                  isPickSelected("pick", pick.id)
                    ? "bg-green-500 text-white hover:bg-green-600"
                    : "bg-white hover:bg-gray-300 text-[#061844]"
                }`}
              >
                {isPickSelected("pick", pick.id) ? "Added ✓" : "Add to List"}
              </button>
            </div>
          </div>
        </div>
      ))}

      {/* Pick Detail Modal */}
      <PickDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pick={selectedPick}
        sourceType="pick"
      />
    </div>
  );
}

export default PicksView;
