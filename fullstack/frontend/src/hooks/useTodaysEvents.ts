import { useState, useEffect, useRef, useCallback } from "react";
import {
  fetchTodaysEvents,
  EventData,
  TodaysEventsResponse,
} from "../utils/api";
import { createTodaysEventsCache } from "../utils/cache";
import {
  convertEventsToHandicappers,
  HandicapperPick,
} from "../utils/dataTransforms";

export interface UseTodaysEventsReturn {
  events: EventData[];
  loading: boolean;
  error: string | null;
  refetch: (customDate?: string) => Promise<void>;
  forceRefresh: () => Promise<void>; // Force fresh data, bypass cache
  date: string;
  // Cache status information
  isCacheUsed: boolean;
  cacheAge: number; // in minutes
  isDataFresh: boolean;
}

export interface UseHandicapperPicksReturn {
  picks: HandicapperPick[];
  handicapperName: string;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTodaysEvents = (
  useBackendConfiguredDate: boolean = false
): UseTodaysEventsReturn => {
  const [events, setEvents] = useState<EventData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [date, setDate] = useState<string>("");

  // Reset cache and state when switching between backend-configured vs explicit date
  useEffect(() => {
    setEvents([]);
    setDate(""); // Will be set by the response
    setError(null);
    setLoading(true);
  }, [useBackendConfiguredDate]);

  // Cache status state
  const [isCacheUsed, setIsCacheUsed] = useState<boolean>(false);
  const [cacheAge, setCacheAge] = useState<number>(0);
  const [isDataFresh, setIsDataFresh] = useState<boolean>(false);

  // Create cache instance (persistent across re-renders)
  const cache = useRef(createTodaysEventsCache()).current;

  // Debounce timer ref
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const loadEvents = useCallback(
    async (dateArg?: string, forceRefresh: boolean = false) => {
      // Clear existing timer
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }

      // Set new debounce timer
      debounceTimer.current = setTimeout(async () => {
        try {
          setError(null);

          const targetDate = dateArg;
          const cacheIdentifier = useBackendConfiguredDate 
            ? "backend-configured" 
            : (targetDate || "current");

          console.log(
            "🔍 Loading events with date:",
            targetDate || "current date",
            forceRefresh ? "(FORCE REFRESH)" : ""
          );

          // Skip cache if force refresh is requested
          let cachedResult: {
            data: TodaysEventsResponse | null;
            isExpired: boolean;
            isFresh: boolean;
            age: number;
          } = {
            data: null,
            isExpired: true,
            isFresh: false,
            age: 0,
          };

          if (!forceRefresh) {
            // Try to load from cache first
            cachedResult = cache.get<TodaysEventsResponse>(cacheIdentifier);
          } else {
            console.log("🚀 Force refresh requested - bypassing cache");
          }

          if (cachedResult.data && !cachedResult.isExpired) {
            // Load cached data immediately
            console.log("⚡ Loading cached data instantly...");
            setEvents(cachedResult.data.events);
            setDate(cachedResult.data.date);
            setIsCacheUsed(true);
            setCacheAge(Math.round(cachedResult.age / 1000 / 60)); // Convert to minutes
            setIsDataFresh(cachedResult.isFresh);
            setLoading(false); // Data is available immediately

            if (cachedResult.isFresh) {
              // Data is fresh, no need to fetch from server
              console.log("✨ Cache data is fresh, skipping server request");
              return;
            } else {
              // Data is stale, continue to fetch fresh data in background
              console.log(
                "🔄 Cache data is stale, fetching fresh data in background..."
              );
            }
          } else {
            // No cached data, show loading state
            console.log("📡 No cached data available, loading from server...");
            setLoading(true);
            setIsCacheUsed(false);
            setCacheAge(0);
            setIsDataFresh(false);
          }

          // Fetch fresh data from server
          console.log("🌐 Fetching fresh data from server...");
          const response: TodaysEventsResponse = await fetchTodaysEvents(
            targetDate,
            useBackendConfiguredDate
          );

          // Log the full JSON response from backend
          console.log(
            "📊 Full API Response (JSON):",
            JSON.stringify(response, null, 2)
          );
          console.log("📅 Response date:", response.date);
          console.log("✅ Response success:", response.success);
          console.log("📈 Number of events:", response.events?.length || 0);

          if (response.events && response.events.length > 0) {
            console.log(
              "🎯 First event sample:",
              JSON.stringify(response.events[0], null, 2)
            );
          }

          if (response.success) {
            // Check if data has changed compared to cache
            const dataChanged =
              !cachedResult.data ||
              JSON.stringify(cachedResult.data.events) !==
                JSON.stringify(response.events);

            if (dataChanged) {
              console.log("🔄 Data has changed, updating UI with fresh data");
              setEvents(response.events);
              setDate(response.date);
              setIsDataFresh(true);
              setCacheAge(0);
            } else {
              console.log(
                "📊 Data unchanged, keeping current UI but updating cache freshness"
              );
              setIsDataFresh(true);
              setCacheAge(0);
              // Force UI to re-render with updated formatting
              setEvents(response.events);
            }

            // Always cache the fresh response
            cache.set(response, cacheIdentifier);
            console.log("💾 Fresh data cached successfully");

            // If we used cache initially, this is a background update
            if (cachedResult.data && !cachedResult.isExpired) {
              console.log("✅ Background refresh completed");
            } else {
              console.log("✅ Initial load completed");
            }
          } else {
            console.error("❌ API Error:", response.message);
            setError(response.message || "Failed to load events");
          }
        } catch (err) {
          const errorMessage =
            err instanceof Error ? err.message : "An unknown error occurred";
          setError(errorMessage);
          console.error("💥 Error loading today's events:", err);

          // If we have cached data and fresh fetch fails, keep using cache
          if (isCacheUsed && events.length > 0) {
            console.log("🛡️ Using cached data due to fetch error");
            setError(`Using cached data: ${errorMessage}`);
          }
        } finally {
          setLoading(false);
        }
      }, 500); // 500ms debounce
    },
    [useBackendConfiguredDate]
  );

  useEffect(() => {
    loadEvents();
  }, [loadEvents, useBackendConfiguredDate]);

  // Force refresh function that bypasses cache
  const forceRefresh = async () => {
    console.log("🔄 Force refresh triggered by user");
    await loadEvents(undefined, true);
  };

  const refetch = useCallback(
    async (customDate?: string) => {
      console.log("🔄 Manual refetch triggered");
      await loadEvents(customDate);
    },
    [loadEvents]
  );

  return {
    events,
    loading,
    error,
    refetch,
    forceRefresh,
    date,
    isCacheUsed,
    cacheAge,
    isDataFresh,
  };
};

export const useHandicapperPicks = (
  handicapperId: number | null,
  customDate?: string
): UseHandicapperPicksReturn => {
  const [picks, setPicks] = useState<HandicapperPick[]>([]);
  const [handicapperName, setHandicapperName] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const {
    events,
    loading: eventsLoading,
    error: eventsError,
    refetch: refetchEvents,
  } = useTodaysEvents(false); // Always use explicit date for handicapper picks

  useEffect(() => {
    if (!eventsLoading && !eventsError && events.length > 0) {
      const allHandicappers = convertEventsToHandicappers(events);
      const targetHandicapper = allHandicappers.find(
        (h) => h.id === handicapperId
      );

      if (targetHandicapper) {
        setPicks(targetHandicapper.picks);
        setHandicapperName(targetHandicapper.name);
      } else {
        setPicks([]);
        setHandicapperName("Unknown");
      }
      setLoading(false);
    } else if (!eventsLoading && (eventsError || events.length === 0)) {
      setPicks([]);
      setHandicapperName("Unknown");
      setLoading(false);
      setError(eventsError || "No events found for this handicapper.");
    }
  }, [events, eventsLoading, eventsError, handicapperId]);

  const refetch = async () => {
    setLoading(true);
    await refetchEvents();
  };

  return {
    picks,
    handicapperName,
    loading,
    error,
    refetch,
  };
};
