import os
import requests
from datetime import datetime
import xml.etree.ElementTree as ET
from dotenv import load_dotenv
load_dotenv()
import collections
from typing import Dict, Tuple, Optional, List
import re

def fetch_odds(
    sportsbook: str,
    event_name: str | None,  # pass None to accept every player
    stat_type: str,
    stat_threshold: str | None,  # None → all lines
    league: str,
) -> Dict[str, Dict[str, float]]:
    """
    Returns { '1.5': {'over': 2.05, 'under': 1.78}, ... } for the
    requested bookmaker / stat-type / league on today's date.

    If `event_name` is None the function collects every player.
    If `stat_threshold` is None it collects every handicap.
    """

    endpoints = {
        "NBA":   "bsktbl/nba-shedule",
        "NFL":   "football/nfl-shedule",
        "NCAAF": "football/fbs-shedule",
        "NCAAB": "bsktbl/ncaa-shedule",
        "WNBA":  "bsktbl/wnba-shedule",
        "NHL":   "hockey/nhl-shedule",
        "MLB":   "baseball/mlb_shedule",
        "PGA":   "golf/pga_schedule",
        "MMA":   "mma/schedule",
    }
    if league not in endpoints:
        raise ValueError(f"Unsupported league '{league}'")

    url  = f"{os.getenv('GOALSERVE_BASE')}/{os.getenv('API_KEY')}/{endpoints[league]}"
    date = datetime.today().strftime("%d.%m.%Y")
    params = {"date1": date, "date2": date, "showodds": "1"}

    resp = requests.get(url, params=params, timeout=20)
    if not resp.content.strip():                       # 0-byte guard
        print("200 OK but empty body → odds unpublished.")
        return {}

    root = ET.fromstring(resp.content)
    odds_by_handicap: Dict[str, Dict[str, float]] = {}

    # locate the desired stat-type
    for market in root.findall(f".//type[@value='{stat_type}']"):
        # then the desired bookmaker
        for bk in market.findall(f"bookmaker[@name='{sportsbook}']"):
            # iterate over every <odd> row
            for odd in bk.findall(".//odd"):
                name_field = odd.get("name", "")
                # filter by player (if provided)
                if event_name and event_name.lower() not in name_field.lower():
                    continue

                handicap = odd.get("handicap") or name_field.split(":")[-1]
                # filter by threshold (if provided)
                if stat_threshold and handicap != stat_threshold:
                    continue

                price = float(odd.get("value"))
                side  = "over" if "over" in name_field.lower() else \
                        "under" if "under" in name_field.lower() else "price"

                odds_by_handicap.setdefault(handicap, {})[side] = price

    return odds_by_handicap

def fetch_odds_oddsapi(
    sportsbook: str,           # "fanduel", "draftkings", etc. (lower-case)
    event_name: str | None,    # player substring or None
    stat_type: str,            # your friendly label
    stat_threshold: str | None,
    league: str,               # "MLB"
) -> Dict[str, Dict[str, float]]:

    if league != "MLB":
        raise ValueError("This helper currently supports MLB only")

    try:
        import importlib
        lookup_mod = importlib.import_module('api_terms_lookup')
        lookup_market = getattr(lookup_mod, 'lookup_market')
    except Exception:
        def lookup_market(x: str):
            return x, None
    reg_key, alt_key = lookup_market(stat_type)

    api_key = os.getenv("ODDS_API_KEY")
    if not api_key:
        raise RuntimeError("Set ODDS_API_KEY in your environment")

    sport_key = "baseball_mlb"
    base_url  = "https://api.the-odds-api.com/v4/sports"

    # ---- STEP 1: fetch today's events (quota-free) -----------------------
    evs = requests.get(f"{base_url}/{sport_key}/events",
                       params={"apiKey": api_key, "dateFormat": "iso"},
                       timeout=12).json()
    event_ids = [ev["id"] for ev in evs]

    odds_by_thr = collections.defaultdict(dict)

    # ---- STEP 2: hit each event for both markets ------------------------
    markets_param = ",".join([reg_key] + ([alt_key] if alt_key else []))

    for eid in event_ids:
        resp = requests.get(f"{base_url}/{sport_key}/events/{eid}/odds",
                            params={
                                "apiKey": api_key,
                                "regions": "us",
                                "markets": markets_param,
                                "bookmakers": sportsbook,
                                "oddsFormat": "decimal",
                                "dateFormat": "iso",
                            },
                            timeout=12)
        if resp.status_code == 404:     # event not priced
            continue
        resp.raise_for_status()
        ev = resp.json()

        for bk in ev.get("bookmakers", []):
            if bk["key"] != sportsbook:
                continue
            for mk in bk.get("markets", []):
                if mk["key"] not in {reg_key, alt_key}:
                    continue
                for oc in mk["outcomes"]:
                    name = (oc.get("description") or oc["name"]).lower()
                    if event_name and event_name.lower() not in name:
                        continue

                    point = oc.get("point")
                    thr   = f"{point:g}".rstrip(".0") if point else ""
                    if mk["key"] == alt_key and thr:
                        thr += "+"                      # ladder key

                    if stat_threshold and thr != stat_threshold:
                        continue

                    side = ("over"  if oc["name"].lower().startswith("over")  else
                            "under" if oc["name"].lower().startswith("under") else
                            "over")   # ladder rows

                    odds_by_thr[thr][side] = oc["price"]

    return dict(odds_by_thr)
def get_mlb_events(api_key: Optional[str] = None) -> List[dict]:
    if api_key is None:
        api_key = os.getenv("ODDS_API_KEY")
    if not api_key:
        raise RuntimeError("Set ODDS_API_KEY in your environment")
    sport_key = "baseball_mlb"
    base_url = "https://api.the-odds-api.com/v4/sports"
    resp = requests.get(
        f"{base_url}/{sport_key}/events",
        params={"apiKey": api_key, "dateFormat": "iso"},
        timeout=12,
    )
    resp.raise_for_status()
    return resp.json()

def find_event_commence_time(away_nickname: str, home_nickname: str, events: Optional[List[dict]] = None) -> Optional[str]:
    if not away_nickname or not home_nickname:
        return None
    if events is None:
        events = get_mlb_events()
    a = away_nickname.strip().lower()
    h = home_nickname.strip().lower()
    for ev in events:
        home_name = (ev.get("home_team") or "").lower()
        away_name = (ev.get("away_team") or "").lower()
        if a in away_name and h in home_name:
            return ev.get("commence_time")
    return None

if __name__ == "__main__":
    d = fetch_odds_oddsapi(
        sportsbook="draftkings",
        event_name="Manny Machado",
        stat_type="Player Bases",
        stat_threshold=None,
        league="MLB",
    )
    print(d)

    result = fetch_odds(
        sportsbook="DraftKings",
        event_name="Manny Machado",
        stat_type="Player Bases",
        stat_threshold=None,
        league="MLB",
    )
    print("Result:", result)
