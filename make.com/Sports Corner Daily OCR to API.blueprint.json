{"name": "Sports Corner Daily OCR to API", "flow": [{"id": 213, "module": "gateway:CustomWebHook", "version": 1, "parameters": {"hook": 1059920, "maxResults": 1}, "mapper": {}, "metadata": {"designer": {"x": -123, "y": 419}, "restore": {"parameters": {"hook": {"data": {"editable": "true"}, "label": "Sports Corner Daily"}}}, "parameters": [{"name": "hook", "type": "hook:gateway-webhook", "label": "Webhook", "required": true}, {"name": "maxResults", "type": "number", "label": "Maximum number of results"}]}, "onerror": [{"id": 311, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": -83, "y": 798}}}]}, {"id": 59, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{213.task.capturedLists.`Picks List`}}"}, "metadata": {"designer": {"x": 163, "y": 361}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}, "onerror": [{"id": 309, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 189, "y": 798}}}]}, {"id": 312, "module": "googlecloudvision:DetectText", "version": 1, "parameters": {"__IMTCONN__": 4342407}, "mapper": {"imageURL": "{{59.`Post Image`}}", "imageSendMethod": "send_type_url", "isToOptimizeDetection": false, "isToIncludeFullTextAnnotation": false}, "metadata": {"designer": {"x": 537, "y": 343}, "restore": {"expect": {"imageSendMethod": {"label": "URL"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "googlecloudvision"}, "label": "<PERSON>s Personal Google Cloud API key"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:googlecloudvision", "label": "Connection", "required": true}], "expect": [{"name": "imageSendMethod", "type": "select", "label": "Data/URL", "required": true, "validate": {"enum": ["send_type_data", "send_type_url"]}}, {"name": "isToOptimizeDetection", "type": "boolean", "label": "Optimize the Detection for Dense Text and Documents", "required": true}, {"name": "isToIncludeFullTextAnnotation", "type": "boolean", "label": "Include Full Text Annotation", "required": true}, {"name": "imageURL", "type": "url", "label": "Image URL", "required": true}]}, "onerror": [{"id": 315, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 523, "y": 743}}}]}, {"id": 210, "module": "open-router:makeAnApiCall", "version": 1, "parameters": {"__IMTCONN__": 4422719}, "mapper": {"url": "/v1/chat/completions", "body": "{\"model\":\"openai/gpt-5-mini\",\"stream\":false,\"max_tokens\":600,\"response_format\":{\"type\":\"json_schema\",\"json_schema\":{\"name\":\"picks_schema\",\"schema\":{\"type\":\"object\",\"additionalProperties\":false,\"required\":[\"picks\"],\"properties\":{\"picks\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"additionalProperties\":false,\"required\":[\"league\",\"pick_type\",\"stat_type\",\"prediction\",\"game_date\",\"prediction_time\"],\"properties\":{\"league\":{\"type\":\"string\"},\"team_a\":{\"type\":\"string\"},\"team_b\":{\"type\":\"string\"},\"pick_type\":{\"type\":\"string\"},\"player_team\":{\"type\":\"string\"},\"player_name\":{\"type\":\"string\"},\"stat_type\":{\"type\":\"string\"},\"stat_threshold\":{\"type\":[\"number\",\"null\"]},\"odds\":{\"type\":\"string\"},\"prediction\":{\"type\":\"integer\"},\"game_date\":{\"type\":\"string\"},\"prediction_time\":{\"type\":\"string\"}}}}}}},\"messages\":[{\"role\":\"system\",\"content\":\"Extract Sports Corner Daily picks ONLY. Rules: 1) league: MLB,NBA,NFL,NHL,MMA,CFL,WNBA (map UFC→MMA). 2) Team nicknames only (e.g., White Sox, Yankees, Dodgers, Commanders). 3) MoneyLine/Spread: team_a = selected team, team_b = opponent from matchup header. 4) Totals: team_a=Over, team_b=Under. 5) Props: player_name as 'F. Lastname'; stat_type short code (K,H,HR,AST,REB,PTS,Total Bases...); stat_threshold numeric; player_team = the player's team nickname from the matchup header. 6) prediction: 1 for Over/MoneyLine/Spread/Yes; 0 for Under/No. 7) odds like +115 or -110; no parentheses. 8) game_date: YYYY-MM-DD (use date in text or Post Date). 9) prediction_time: YYYY-MM-DD HH:MM:SS America/Los_Angeles. Convert from ET/CT/MT/PT if present (ET→PT −3h). Output ONE valid JSON object only.\"},{\"role\":\"user\",\"content\":\"Post Date: {{59.`Post Date`}}\\nTitle: {{59.`Post Title`}}\\nSport: {{59.`Sport Type`}}\\nHandicapper: {{ifempty(59.`Creator Name`; \"Sports Corner Daily\")}}\\nDescription: {{59.`Post Description`}}\\nImage OCR: \\n{{312.fullTextAnnotation.text}}\"}]}", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}]}, "metadata": {"designer": {"x": 837, "y": 193}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "open-router-4ur2vj"}, "label": "My OpenRouter connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:open-router3,open-router-4ur2vj", "label": "Connection", "required": true}], "expect": [{"name": "url", "type": "text", "label": "URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key"}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "key", "type": "text", "label": "Key"}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "body", "type": "any", "label": "Body"}]}, "onerror": [{"id": 310, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 828, "y": 706}}}]}, {"id": 211, "module": "json:ParseJSON", "version": 1, "parameters": {}, "mapper": {"json": "{{trim(replace(replace(210.body.choices[1].message.content; \"```json\"; \"\"); \"```\"; \"\"))}}"}, "metadata": {"designer": {"x": 1223, "y": 269}, "restore": {}, "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}, "onerror": [{"id": 306, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 1200, "y": 600}}}]}, {"id": 300, "module": "util:SetVariables", "version": 1, "parameters": {}, "mapper": {"scope": "roundtrip", "variables": [{"name": "event_date", "value": "{{211.picks[].game_date}}"}, {"name": "expert_name", "value": "{{ifempty(59.`Creator Name`; \"Sports Corner Daily\")}}"}]}, "metadata": {"designer": {"x": 1500, "y": 150}, "restore": {"expect": {"scope": {"label": "One cycle"}, "variables": {"items": [null, null]}}}, "expect": [{"name": "variables", "spec": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "value", "type": "any", "label": "Variable value"}], "type": "array", "label": "Variables"}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}], "interface": [{"name": "event_date", "type": "any", "label": "event_date"}, {"name": "expert_name", "type": "any", "label": "expert_name"}]}, "onerror": [{"id": 308, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 1523, "y": 369}}}]}, {"id": 301, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{211.picks}}"}, "metadata": {"designer": {"x": 1799, "y": 73}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}, "onerror": [{"id": 307, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 1800, "y": 300}}}]}, {"id": 302, "module": "builtin:BasicRouter", "version": 1, "parameters": {"else": 0}, "mapper": null, "metadata": {"designer": {"x": 2100, "y": 0}}, "routes": [{"flow": [{"id": 303, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "https://project-parlay-mattfavela.replit.app/api/external/insert_picks", "data": "{\"picks\":[{\"league\":\"{{ifempty(301.league; )}}\",\"team_a\":\"{{ifempty(301.team_a; )}}\",\"team_b\":\"{{ifempty(301.team_b; )}}\",\"pick_type\":\"{{ifempty(301.pick_type; )}}\",\"player_team\":\"{{ifempty(301.player_team; )}}\",\"player_name\":\"{{ifempty(301.player_name; )}}\",\"stat_type\":\"{{ifempty(301.stat_type; )}}\",\"stat_threshold\":{{if(301.pick_type = \"MoneyLine\"; null; parseNumber(301.stat_threshold))}},\"odds\":\"{{ifempty(301.odds; )}}\",\"prediction\":{{if(lower(301.stat_type) = \"under\"; 0; 1)}},\"expert_name\":\"{{ifempty(300.expert_name; \"Sports Corner Daily\")}}\",\"event_date\":\"{{ifempty(301.game_date; )}}\",\"prediction_time\":\"{{ifempty(301.prediction_time; )}}\"}]}", "gzip": true, "method": "post", "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "pk_ext_api_2025_ppadmin"}], "timeout": "300", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 2400, "y": 0}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null, null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "Empty"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}, "onerror": [{"id": 304, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 2284, "y": 214}}}]}]}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}